# TPC-C性能测试使用指南

## 1. 概述

TPC-C (Transaction Processing Performance Council - C) 是一个标准的数据库性能测试基准，用于评估数据库系统在高并发事务处理场景下的性能表现。

### 1.1 测试文件结构
```
tpcc_run/tpc-c/
├── CMakeLists.txt              # 构建配置文件
├── tpcc_load                   # 数据加载程序（可执行文件）
├── tpcc_start                  # TPC-C测试程序（可执行文件）
├── test_db2025_compatibility   # 兼容性测试程序
├── simple_consistency_check    # 数据一致性检查程序
├── load.cpp                    # 数据加载源码
├── start.cpp                   # 测试主程序源码
├── neword.cpp                  # New Order事务实现
├── payment.cpp                 # Payment事务实现
├── ordstat.cpp                 # Order Status事务实现
├── delivery.cpp                # Delivery事务实现
├── slev.cpp                    # Stock Level事务实现
└── 其他支持文件...
```

### 1.2 测试阶段
1. **第一阶段**: 数据加载 (限时240秒)
2. **第二阶段**: 并发事务测试 (最长1200秒)
3. **第三阶段**: 数据一致性检验

## 2. 编译构建

### 2.1 进入测试目录
```bash
cd tpcc_run/tpc-c/
```

### 2.2 使用CMake构建
```bash
# 创建构建目录
mkdir -p build
cd build

# 生成Makefile
cmake ..

# 编译
make -j$(nproc)

# 或者直接在tpc-c目录下编译
cd ..
mkdir -p build && cd build && cmake .. && make -j$(nproc)
```

### 2.3 验证编译结果
编译成功后应该生成以下可执行文件：
- `tpcc_load`: 数据加载程序
- `tpcc_start`: TPC-C性能测试程序
- `test_db2025_compatibility`: 兼容性测试程序
- `simple_consistency_check`: 一致性检查程序

## 3. 准备工作

### 3.1 启动数据库服务器
```bash
# 在项目根目录下启动RMDB服务器
cd /path/to/db2025-x1
mkdir -p build && cd build
cmake .. && make -j$(nproc)

# 启动数据库服务器（默认端口8765）
./bin/rmdb testdb
```

### 3.2 创建TPC-C表结构
在另一个终端中连接数据库并创建表：
```bash
# 连接到数据库
./bin/rmdb testdb

# 或者通过脚本创建表结构
# 表结构通常包括：warehouse, district, customer, history, new_orders, orders, order_line, item, stock
```

## 4. 数据加载阶段

### 4.1 运行数据加载程序
```bash
cd tpcc_run/tpc-c/build
./tpcc_load
```

### 4.2 数据加载配置
数据加载程序会连接到本地数据库服务器 (127.0.0.1:8765) 并加载TPC-C标准数据集。

**关键参数**:
- 仓库数量: 默认50个仓库
- 连接方式: TCP连接到127.0.0.1:8765
- 数据量: 根据TPC-C标准生成相应数据

### 4.3 监控加载进度
数据加载过程中会显示进度信息：
```
Loading Item table...
Loading Warehouse table...
Loading District table...
Loading Customer table...
Loading History table...
Loading New Orders table...
Loading Orders table...
Loading Order Line table...
Loading Stock table...
```

### 4.4 验证数据加载
加载完成后，可以通过以下方式验证：
```sql
-- 检查各表记录数
SELECT COUNT(*) FROM warehouse;    -- 应该是50
SELECT COUNT(*) FROM district;     -- 应该是500 (50*10)
SELECT COUNT(*) FROM customer;     -- 应该是150000 (50*10*300)
-- 其他表类似...
```

## 5. 性能测试阶段

### 5.1 运行TPC-C性能测试
```bash
cd tpcc_run/tpc-c/build
./tpcc_start
```

### 5.2 测试配置参数
**位置**: `start.cpp:500-504`

```cpp
num_ware = 50;         // 仓库数量
num_conn = 16;         // 并发连接数
lampup_time = 2;       // 预热时间（秒）
measure_time = 10;     // 测量时间（秒）
num_trans = 500;       // 每个线程的事务数
```

### 5.3 事务类型分布
根据TPC-C标准，事务分布如下：
- **New Order**: 10/23 (43.5%) - 新订单事务
- **Payment**: 10/23 (43.5%) - 付款事务
- **Order Status**: 1/23 (4.3%) - 订单状态查询
- **Delivery**: 1/23 (4.3%) - 配送事务
- **Stock Level**: 1/23 (4.3%) - 库存水平查询

### 5.4 测试执行流程
1. **初始化阶段**: 创建16个并发线程
2. **预热阶段**: 运行2秒预热
3. **测量阶段**: 正式测量10秒
4. **结果统计**: 计算TpmC值

### 5.5 监控测试进度
测试过程中会显示实时统计信息：
```
RAMP-UP TIME.(2 sec.)
MEASURING START.

<Raw Results>
  [0] sc:45  lt:0  rt:0  fl:0 avg_rt: 12.3 (5)
  [1] sc:43  lt:0  rt:0  fl:0 avg_rt: 15.2 (5)
  [2] sc:2   lt:0  rt:0  fl:0 avg_rt: 8.1 (5)
  [3] sc:2   lt:0  rt:0  fl:0 avg_rt: 45.6 (5)
  [4] sc:2   lt:0  rt:0  fl:0 avg_rt: 23.4 (5)

<TpmC>
                 324.567 TpmC
```

## 6. 兼容性测试

### 6.1 运行兼容性测试
```bash
cd tpcc_run/tpc-c/build
./test_db2025_compatibility
```

### 6.2 测试内容
兼容性测试会验证基本SQL操作：
- 创建表
- 插入数据
- 查询数据
- 删除表

## 7. 数据一致性检查

### 7.1 运行一致性检查
```bash
cd tpcc_run/tpc-c/build
./simple_consistency_check
```

### 7.2 检查规则
一致性检查会验证：
- 仓库数据完整性
- 订单数据一致性
- 库存数据准确性
- 客户账户平衡

## 8. 性能调优建议

### 8.1 数据库配置优化
```cpp
// 在config.h中调整缓冲池大小
#define BUFFER_POOL_SIZE 262144  // 1GB缓冲池

// 调整并发连接数
#define MAX_CONN_LIMIT 16
```

### 8.2 测试参数调优
```cpp
// 在start.cpp中调整测试参数
num_ware = 10;         // 减少仓库数量以适应内存限制
num_conn = 8;          // 根据CPU核心数调整并发数
measure_time = 60;     // 增加测量时间获得更稳定结果
```

### 8.3 系统级优化
```bash
# 调整系统参数
echo 'vm.swappiness=1' >> /etc/sysctl.conf
echo 'vm.dirty_ratio=15' >> /etc/sysctl.conf
sysctl -p

# 设置CPU调度策略
echo performance > /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

## 9. 故障排除

### 9.1 常见问题

**问题1**: 连接数据库失败
```
Failed to connect to database server
```
**解决方案**:
- 确认数据库服务器已启动
- 检查端口8765是否被占用
- 验证防火墙设置

**问题2**: 数据加载超时
```
In performance test, server timeout in load_data phase
```
**解决方案**:
- 增加缓冲池大小
- 优化磁盘I/O性能
- 检查系统资源使用情况

**问题3**: 事务执行失败
```
Transaction abort or error
```
**解决方案**:
- 检查死锁检测机制
- 调整事务隔离级别
- 优化索引配置

### 9.2 调试技巧

**启用SQL日志**:
```cpp
#define PRINT_SQL_TOFILE 1  // 在start.cpp中已启用
```
这会将所有SQL命令记录到`sql.txt`文件中。

**增加调试输出**:
```cpp
printf("DEBUG: 发送SQL命令: %s\n", command);  // 已在代码中启用
```

## 10. 性能基准

### 10.1 评估指标
- **TpmC**: 每分钟处理的New Order事务数
- **响应时间**: 各类事务的平均响应时间
- **成功率**: 事务成功执行的百分比

### 10.2 性能目标
根据决赛要求：
- 数据加载: 240秒内完成
- 并发测试: 支持16个并发连接
- 一致性: 通过所有一致性检验

### 10.3 优化效果评估
```bash
# 运行多次测试取平均值
for i in {1..5}; do
    echo "Test run $i"
    ./tpcc_start
    sleep 10
done
```

## 11. 注意事项

1. **内存限制**: 评测机内存为8GB，注意内存使用
2. **时间限制**: 总测试时间不超过2400秒
3. **并发数**: 固定16个客户端连接
4. **数据一致性**: 必须通过所有一致性检验
5. **错误处理**: 任何崩溃都会导致测试失败

通过以上指南，您应该能够成功运行TPC-C性能测试并获得准确的性能评估结果。
