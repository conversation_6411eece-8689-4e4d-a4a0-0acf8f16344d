/*
 * -*-C-*-
 * delivery.pc
 * corresponds to A.4 in appendix A
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <thread>
#include <time.h>
#include "iostream"
#include "sstream"

#include "spt_proc.h"
#include "tpc.h"
#include "fstream"

inline int safe_stoi(const std::string& str, int default_value = 0) {
    if (str.empty()) return default_value;
    try { return std::stoi(str); }
    catch (...) { return default_value; }
}
inline float safe_stof(const std::string& str, float default_value = 0.0f) {
    if (str.empty()) return default_value;
    try { return std::stof(str); }
    catch (...) { return default_value; }
}
#define NNULL ((void *)0)
extern std::atomic<int> atomic_array[20];
extern void send_command(int sockfd, const char* command, SqlResult* result);
int delivery( int t_num,
	      int w_id_arg,
	      int o_carrier_id_arg
)
{
	int ret;
	int            w_id = w_id_arg;
	int            o_carrier_id = o_carrier_id_arg;
	int            d_id;
	int            c_id;
	int            no_o_id;
	float           ol_total;
	char            datetime[20];

	int proceed = 0;
	int num_cols;
	SqlResult *sqlite_stmt = new SqlResult();
	int sockfd = atomic_array[t_num];
	// 获取当前线程id
	std::thread::id threadId = std::this_thread::get_id();
	std::stringstream ss;
	ss << threadId;
	std::string threadIdStr = ss.str();

	std::string file_path = "delivery_" + threadIdStr + ".txt";
	std::fstream outfile;
	// outfile.open(file_path, std::ios::out | std::ios::app);

	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/

        gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);
	datetime[19] = '\0';
	/* For each district in warehouse */
	/* printf("W: %d\n", w_id); */
	// sqlite_stmt = stmt[t_num];
	ResetSqlResult(sqlite_stmt);
	char send_str[500];
	for (d_id = 1; d_id <= DIST_PER_WARE; d_id++) {
	        proceed = 1;
		/*EXEC_SQL SELECT COALESCE(MIN(no_o_id),0) INTO :no_o_id
		                FROM new_orders
		                WHERE no_d_id = :d_id AND no_w_id = :w_id;*/


		// sqlite3_bind_int64(sqlite_stmt, 1, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, w_id);

		// ret = sqlite3_step(sqlite_stmt);		
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 1) goto sqlerr;

		// 	no_o_id = sqlite3_column_int64(sqlite_stmt, 0);
		// }

		// sqlite3_reset(sqlite_stmt);

		sprintf(send_str, "SELECT MIN(no_o_id) as min_o_id FROM new_orders WHERE no_d_id = %d AND no_w_id = %d;", d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count == 0){
				no_o_id = 0;
			}
			else{
				num_cols = sqlite_stmt->field_count;
				// if(num_cols != 1) goto sqlerr;
				// Get the minimum no_o_id value
				if(sqlite_stmt->field_values[0][0].empty()) {
					no_o_id = 0;
				} else {
					try {
						no_o_id = safe_stoi(sqlite_stmt->field_values[0][0].c_str());
					} catch (const std::exception& e) {
						printf("Error converting no_o_id: %s\n", sqlite_stmt->field_values[0][0].c_str());
						goto sqlerr;
					}
				}
			}
		}


		ResetSqlResult(sqlite_stmt);
		if(no_o_id == 0) continue;
		proceed = 2;
		/*EXEC_SQL DELETE FROM new_orders WHERE no_o_id = :no_o_id AND no_d_id = :d_id
		  AND no_w_id = :w_id;*/
		// sqlite_stmt = stmt[t_num][26];

		// sqlite3_bind_int64(sqlite_stmt, 1, no_o_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, w_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, sizeof(send_str));
		sprintf(send_str, "DELETE FROM new_orders WHERE no_o_id = %d AND no_d_id = %d AND no_w_id = %d;", no_o_id, d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif

		if(!sqlite_stmt->success) goto sqlerr;

		ResetSqlResult(sqlite_stmt);

		proceed = 3;
		/*EXEC_SQL SELECT o_c_id INTO :c_id FROM orders
		                WHERE o_id = :no_o_id AND o_d_id = :d_id
				AND o_w_id = :w_id;*/
		// sqlite_stmt = stmt[t_num][27];

		// sqlite3_bind_int64(sqlite_stmt, 1, no_o_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, w_id);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 1) goto sqlerr;

		// 	c_id = sqlite3_column_int64(sqlite_stmt, 0);
		// }

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, sizeof(send_str));
		sprintf(send_str, "SELECT o_c_id FROM orders WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", no_o_id, d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif

		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			// if(num_cols != 1) goto sqlerr;
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[0][0].empty()) {
				c_id = 0;
			} else {
				try {
					c_id = safe_stoi(sqlite_stmt->field_values[0][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting c_id: %s\n", sqlite_stmt->field_values[0][0].c_str());
					goto sqlerr;
				}
			}
		}

		ResetSqlResult(sqlite_stmt);

		proceed = 4;
		/*EXEC_SQL UPDATE orders SET o_carrier_id = :o_carrier_id
		                WHERE o_id = :no_o_id AND o_d_id = :d_id AND
				o_w_id = :w_id;*/
		// sqlite_stmt = stmt[t_num][28];

		// sqlite3_bind_int64(sqlite_stmt, 1, o_carrier_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, no_o_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 4, w_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);
		
		memset(send_str, 0, sizeof(send_str));
		sprintf(send_str, "UPDATE orders SET o_carrier_id = %d WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", o_carrier_id, no_o_id, d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif

		if(!sqlite_stmt->success) goto sqlerr;

		ResetSqlResult(sqlite_stmt);

		proceed = 5;
		/*EXEC_SQL UPDATE order_line
		                SET ol_delivery_d = :datetime
		                WHERE ol_o_id = :no_o_id AND ol_d_id = :d_id AND
				ol_w_id = :w_id;*/
		// sqlite_stmt = stmt[t_num][29];

		// sqlite3_bind_text(sqlite_stmt, 1, datetime, -1, SQLITE_STATIC);
		// sqlite3_bind_int64(sqlite_stmt, 2, no_o_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 4, w_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, sizeof(send_str));
		sprintf(send_str, "UPDATE order_line SET ol_delivery_d = '%s' WHERE ol_o_id = %d AND ol_d_id = %d AND ol_w_id = %d;", datetime, no_o_id, d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif

		if(!sqlite_stmt->success) goto sqlerr;
		
		ResetSqlResult(sqlite_stmt);

		proceed = 6;
		/*EXEC_SQL SELECT SUM(ol_amount) INTO :ol_total
		                FROM order_line
		                WHERE ol_o_id = :no_o_id AND ol_d_id = :d_id
				AND ol_w_id = :w_id;*/
		// sqlite_stmt = stmt[t_num][30];

		// sqlite3_bind_int64(sqlite_stmt, 1, no_o_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, w_id);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 1) goto sqlerr;

		// 	ol_total = sqlite3_column_double(sqlite_stmt, 0);
		// }

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, sizeof(send_str));
		sprintf(send_str, "SELECT SUM(ol_amount) as sum_amount FROM order_line WHERE ol_o_id = %d AND ol_d_id = %d AND ol_w_id = %d;", no_o_id, d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif
		
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			// if(num_cols != 1) goto sqlerr;
			ol_total = safe_stof(sqlite_stmt->field_values[0][0].c_str());
		}

		ResetSqlResult(sqlite_stmt);

		proceed = 7;
		/*EXEC_SQL UPDATE customer SET c_balance = c_balance + :ol_total ,
		                             c_delivery_cnt = c_delivery_cnt + 1
		                WHERE c_id = :c_id AND c_d_id = :d_id AND
				c_w_id = :w_id;*/
		// sqlite_stmt = stmt[t_num][31];

		// sqlite3_bind_double(sqlite_stmt, 1, ol_total);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 4, w_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, sizeof(send_str));
		sprintf(send_str, "UPDATE customer SET c_balance = c_balance + %f , c_delivery_cnt = c_delivery_cnt + 1 WHERE c_id = %d AND c_d_id = %d AND c_w_id = %d;", ol_total, c_id, d_id, w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		#ifdef PRINT_RECV_BUF
		std::cout << sqlite_stmt->rec_msg << std::endl;
		#endif

		if(!sqlite_stmt->success) goto sqlerr;

		ResetSqlResult(sqlite_stmt);

		/*EXEC_SQL COMMIT WORK;*/
		//if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;

		/* printf("D: %d, O: %d, time: %d\n", d_id, o_id, tad); */

	}
	/*EXEC_SQL COMMIT WORK;*/
	delete sqlite_stmt;  // 删除分配的内存
	return (1);

sqlerr:
	fprintf(stderr, "delivery %d:%d\n",t_num,proceed);
	// printf("%s: error: %s\n", __func__, sqlite3_errmsg(ctx[t_num]));
	printf("failed in delivery\n");
	fflush(stdout);
	if(!sqlite_stmt->is_abort){
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << "send_str " << send_str<<" exec failed" << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
	}
	//error(ctx[t_num],mysql_stmt);
        /*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
	/*EXEC_SQL ROLLBACK WORK;*/
	// sqlite3_exec(ctx[t_num], "ROLLBACK;", NULL, NULL, NULL);
	ResetSqlResult(sqlite_stmt);
	send_command(sockfd, "abort;", sqlite_stmt);
	send_command(sockfd, "begin;", sqlite_stmt);
	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << "begin;" << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
		#ifdef PRINT_RECV_BUF
	std::cout << sqlite_stmt->rec_msg << std::endl;
	#endif

	ResetSqlResult(sqlite_stmt);
sqlerrerr:
	delete sqlite_stmt;  // 删除分配的内存
	return (0);
}
