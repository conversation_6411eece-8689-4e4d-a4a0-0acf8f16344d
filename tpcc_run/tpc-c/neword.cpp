/*
 * -*-C-*- 
 * neword.pc 
 * corresponds to A.1 in appendix A
 */

#include <iostream>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <thread>
#include <time.h>
#include "sstream"
#include "fstream"
#include "spt_proc.h"
#include "tpc.h"

inline int safe_stoi(const std::string& str, int default_value = 0) {
    if (str.empty()) return default_value;
    try { return std::stoi(str); }
    catch (...) { return default_value; }
}
inline float safe_stof(const std::string& str, float default_value = 0.0f) {
    if (str.empty()) return default_value;
    try { return std::stof(str); }
    catch (...) { return default_value; }
}
#define pick_dist_info(ol_dist_info,ol_supply_w_id) \
switch(ol_supply_w_id) { \
	case 1: strncpy(ol_dist_info, s_dist_01, 25); break; \
	case 2: strncpy(ol_dist_info, s_dist_02, 25); break; \
	case 3: strncpy(ol_dist_info, s_dist_03, 25); break; \
	case 4: strncpy(ol_dist_info, s_dist_04, 25); break; \
	case 5: strncpy(ol_dist_info, s_dist_05, 25); break; \
	case 6: strncpy(ol_dist_info, s_dist_06, 25); break; \
	case 7: strncpy(ol_dist_info, s_dist_07, 25); break; \
	case 8: strncpy(ol_dist_info, s_dist_08, 25); break; \
	case 9: strncpy(ol_dist_info, s_dist_09, 25); break; \
	case 10: strncpy(ol_dist_info, s_dist_10, 25); break; \
}

extern std::atomic<int> atomic_array[20];

extern SqlResult **stmt;
extern void send_command(int sockfd, const char* command, SqlResult* result);


// extern sqlite3 **ctx;
// extern sqlite3_stmt ***stmt;

#define NNULL ((void *)0)

/*
 * the new order transaction
 */
int neword( int t_num,
	    int w_id_arg,		/* warehouse id */
	    int d_id_arg,		/* district id */
	    int c_id_arg,		/* customer id */
	    int o_ol_cnt_arg,	        /* number of items */
	    int o_all_local_arg,	/* are all order lines local */
	    int itemid[],		/* ids of items to be ordered */
	    int supware[],		/* warehouses supplying items */
	    int qty[]		        /* quantity of each item */
)
{

	int ret;
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	int            o_ol_cnt = o_ol_cnt_arg;
	int            o_all_local = o_all_local_arg;
	float           c_discount;
	char            c_last[17];
	char            c_credit[3];
	float           w_tax;
	int            d_next_o_id = 0;
	float           d_tax;
	char            datetime[20];
	int            o_id = 0;
	char            i_name[25];
	float           i_price;
	char            i_data[51];
	int            ol_i_id;
	int            s_quantity;
	char            s_data[51];
	char            s_dist_01[25];
	char            s_dist_02[25];
	char            s_dist_03[25];
	char            s_dist_04[25];
	char            s_dist_05[25];
	char            s_dist_06[25];
	char            s_dist_07[25];
	char            s_dist_08[25];
	char            s_dist_09[25];
	char            s_dist_10[25];
	char            ol_dist_info[25];
	int            ol_supply_w_id;
	float           ol_amount;
	int            ol_number;
	int            ol_quantity;

	char            iname[MAX_NUM_ITEMS][MAX_ITEM_LEN];
	char            bg[MAX_NUM_ITEMS];
	float           amt[MAX_NUM_ITEMS];
	float           price[MAX_NUM_ITEMS];
	int            stock[MAX_NUM_ITEMS];
	float           total = 0.0;

	int            min_num;
	int            i,j,tmp,swp;
	int            ol_num_seq[MAX_NUM_ITEMS];

	int             proceed = 0;
 	struct timespec tbuf1,tbuf_start;
	clock_t clk1,clk_start;	


	SqlResult *sqlite_stmt = new SqlResult();
	int sockfd = atomic_array[t_num];
	int num_cols;
	// 获取当前线程id
	std::thread::id threadId = std::this_thread::get_id();
	std::stringstream ss;
	ss << threadId;
	std::string threadIdStr = ss.str();

	std::string file_path = "neword_" + threadIdStr + ".txt";
	std::fstream outfile;
	/* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
	/* EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/

	/*EXEC SQL CONTEXT USE :ctx[t_num];*/

	gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);
	datetime[19] = '\0';

	clk_start = clock_gettime(CLOCK_REALTIME, &tbuf_start );

	proceed = 1;
	/*EXEC_SQL SELECT c_discount, c_last, c_credit, w_tax
		INTO :c_discount, :c_last, :c_credit, :w_tax
	        FROM customer, warehouse
	        WHERE w_id = :w_id 
		AND c_w_id = w_id 
		AND c_d_id = :d_id 
		AND c_id = :c_id;*/
	// sqlite3_bind_int64(sqlite_stmt, 1, w_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, c_id);

	// ret = sqlite3_step(sqlite_stmt);
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 4) goto sqlerr;
	
	// 	c_discount = sqlite3_column_double(sqlite_stmt, 0);
	// 	strcpy(c_last, sqlite3_column_text(sqlite_stmt, 1));
	// 	strcpy(c_credit, sqlite3_column_text(sqlite_stmt, 2));
	// 	w_tax = sqlite3_column_double(sqlite_stmt, 3);
	// }

	// sqlite3_reset(sqlite_stmt);
	ResetSqlResult(sqlite_stmt);

	char send_str[500];
	memset(send_str, 0, 500);
	// sprintf(send_str, "SELECT c_discount, c_last, c_credit, w_tax FROM customer, warehouse WHERE w_id = %d AND c_w_id = w_id AND c_d_id = %d AND c_id = %d;", w_id, d_id, c_id);
	// send_command(sockfd, send_str, sqlite_stmt);
	// if(!sqlite_stmt->success) goto sqlerr;
	    // 拆分为单表SQL：
    // 查询customer表
    memset(send_str, 0, 500);
    sprintf(send_str, "SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", w_id, d_id, c_id);
    send_command(sockfd, send_str, sqlite_stmt);
    if(!sqlite_stmt->success || sqlite_stmt->row_count < 1) goto sqlerr;
    num_cols = sqlite_stmt->field_count;
    if(num_cols < 3) goto sqlerr;
    try {
        c_discount = safe_stof(sqlite_stmt->field_values[0][0].c_str());
    } catch (...) { c_discount = 0.0f; }
    strncpy(c_last, sqlite_stmt->field_values[0][1].c_str(), sizeof(c_last)-1);
    c_last[sizeof(c_last)-1] = '\0';
    strncpy(c_credit, sqlite_stmt->field_values[0][2].c_str(), sizeof(c_credit)-1);
    c_credit[sizeof(c_credit)-1] = '\0';
    ResetSqlResult(sqlite_stmt);
    proceed = 2;
    // 查询warehouse表
    memset(send_str, 0, 500);
    sprintf(send_str, "SELECT w_tax FROM warehouse WHERE w_id = %d;", w_id);
    send_command(sockfd, send_str, sqlite_stmt);
    if(!sqlite_stmt->success || sqlite_stmt->row_count < 1) goto sqlerr;
    num_cols = sqlite_stmt->field_count;
    if(num_cols < 1) goto sqlerr;
    try {
        w_tax = safe_stof(sqlite_stmt->field_values[0][0].c_str());
    } catch (...) { w_tax = 0.0f; }
    ResetSqlResult(sqlite_stmt);
    proceed = 3;

	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	if(sqlite_stmt->success){
		// if(sqlite_stmt->row_count < 1) {
		// 	printf("DEBUG: row_count = %d, expected >= 1\n", sqlite_stmt->row_count);
		// 	goto sqlerr;
		// }
		// num_cols = sqlite_stmt->field_count;
		// if(num_cols < 4) {
		// 	printf("DEBUG: field_count = %d, expected 4\n", num_cols);
		// 	goto sqlerr;
		// }

		// c_discount = std::stof(sqlite_stmt->field_values[0][0].c_str());
		// // strcpy(c_last, sqlite_stmt->field_values[0][1].c_str());
		// strncpy(c_last, sqlite_stmt->field_values[0][1].c_str(), sizeof(c_last)-1);
		// c_last[sizeof(c_last)-1] = '\0';
		// // strcpy(c_credit, sqlite_stmt->field_values[0][2].c_str());
		// strncpy(c_credit, sqlite_stmt->field_values[0][2].c_str(), sizeof(c_credit)-1);
		// c_credit[sizeof(c_credit)-1] = '\0';
		// w_tax = std::stof(sqlite_stmt->field_values[0][3].c_str());
	}

	ResetSqlResult(sqlite_stmt);

#ifdef DEBUG
	printf("n %d\n",proceed);
#endif

	proceed = 2;
	/*EXEC_SQL SELECT d_next_o_id, d_tax INTO :d_next_o_id, :d_tax
	        FROM district
	        WHERE d_id = :d_id
		AND d_w_id = :w_id
		FOR UPDATE;*/

	// sqlite_stmt = stmt[t_num][1];

	// sqlite3_bind_int64(sqlite_stmt, 1, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, w_id);

	// ret = sqlite3_step(sqlite_stmt);
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 2) goto sqlerr;

	// 	d_next_o_id = sqlite3_column_int64(sqlite_stmt, 0);
	// 	d_tax = sqlite3_column_double(sqlite_stmt, 1);
	// }

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "SELECT d_next_o_id, d_tax FROM district WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	if(sqlite_stmt->success){
		if(sqlite_stmt->row_count < 1) goto sqlerr;
		num_cols = sqlite_stmt->field_count;
		// if(num_cols < 2) goto sqlerr;

		// 添加错误处理，防止空字符串导致的stoi异常
		if(sqlite_stmt->field_values[0][0].empty()) {
			d_next_o_id = 0;
		} else {
			try {
				d_next_o_id = safe_stoi(sqlite_stmt->field_values[0][0].c_str());
			} catch (const std::exception& e) {
				printf("Error converting d_next_o_id: %s\n", sqlite_stmt->field_values[0][0].c_str());
				goto sqlerr;
			}
		}
		if(d_next_o_id == 0){
			std::cout << "invaliad d_next_o_id" << std::endl;
		}
		d_tax = safe_stof(sqlite_stmt->field_values[0][1].c_str());
	}

	ResetSqlResult(sqlite_stmt);

	proceed = 3;
	/*EXEC_SQL UPDATE district SET d_next_o_id = :d_next_o_id + 1
	        WHERE d_id = :d_id 
		AND d_w_id = :w_id;*/

	// sqlite_stmt = stmt[t_num][2];

	// sqlite3_bind_int64(sqlite_stmt, 1, d_next_o_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, w_id);

	// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	if(!sqlite_stmt->success) 
		goto sqlerr;
	ResetSqlResult(sqlite_stmt);


	o_id = d_next_o_id;

#ifdef DEBUG
	printf("n %d\n",proceed);
#endif

	proceed = 4;
	/*EXEC_SQL INSERT INTO orders (o_id, o_d_id, o_w_id, o_c_id,
			             o_entry_d, o_ol_cnt, o_all_local)
		VALUES(:o_id, :d_id, :w_id, :c_id, 
		       :datetime,
                       :o_ol_cnt, :o_all_local);*/


	// sqlite_stmt = stmt[t_num][3];

	// sqlite3_bind_int64(sqlite_stmt, 1, o_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, w_id);
	// sqlite3_bind_int64(sqlite_stmt, 4, c_id);
	// sqlite3_bind_text(sqlite_stmt, 5, datetime, -1, SQLITE_STATIC);
	// sqlite3_bind_int64(sqlite_stmt, 6, o_ol_cnt);
	// sqlite3_bind_int64(sqlite_stmt, 7, o_all_local);
			
	// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);

	if(o_id == 1){
		std::cout<< "invaliad o_id" << std::endl;
	}
	sprintf(send_str, "INSERT INTO orders  VALUES(%d, %d, %d, %d, '%s',-1,  %d, %d);", o_id, d_id, w_id, c_id, datetime, o_ol_cnt, o_all_local);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	if(!sqlite_stmt->success) 
		goto sqlerr;

	ResetSqlResult(sqlite_stmt);

#ifdef DEBUG
	printf("n %d\n",proceed);
#endif
	proceed = 5;
	/* EXEC_SQL INSERT INTO new_orders (no_o_id, no_d_id, no_w_id)
	   VALUES (:o_id,:d_id,:w_id); */

	// sqlite_stmt = stmt[t_num][4];

	// sqlite3_bind_int64(sqlite_stmt, 1, o_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, w_id);
			
	// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "INSERT INTO new_orders  VALUES(%d, %d, %d);", o_id, d_id, w_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	if(!sqlite_stmt->success) 
		goto sqlerr;

	ResetSqlResult(sqlite_stmt);

	/* sort orders to avoid DeadLock */
	for (i = 0; i < o_ol_cnt; i++) {
		ol_num_seq[i]=i;
	}
	for (i = 0; i < (o_ol_cnt - 1); i++) {
		tmp = (MAXITEMS + 1) * supware[ol_num_seq[i]] + itemid[ol_num_seq[i]];
		min_num = i;
		for ( j = i+1; j < o_ol_cnt; j++) {
		  if ( (MAXITEMS + 1) * supware[ol_num_seq[j]] + itemid[ol_num_seq[j]] < tmp ){
		    tmp = (MAXITEMS + 1) * supware[ol_num_seq[j]] + itemid[ol_num_seq[j]];
		    min_num = j;
		  }
		}
		if ( min_num != i ){
		  swp = ol_num_seq[min_num];
		  ol_num_seq[min_num] = ol_num_seq[i];
		  ol_num_seq[i] = swp;
		}
	}


	for (ol_number = 1; ol_number <= o_ol_cnt; ol_number++) {
		ol_supply_w_id = supware[ol_num_seq[ol_number - 1]];
		ol_i_id = itemid[ol_num_seq[ol_number - 1]];
		ol_quantity = qty[ol_num_seq[ol_number - 1]];

		/* EXEC SQL WHENEVER NOT FOUND GOTO invaliditem; */
		proceed = 6;
		/*EXEC_SQL SELECT i_price, i_name, i_data
			INTO :i_price, :i_name, :i_data
		        FROM item
		        WHERE i_id = :ol_i_id;*/

		// sqlite_stmt = stmt[t_num][5];

		// sqlite3_bind_int64(sqlite_stmt, 1, ol_i_id);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 3) goto sqlerr;

		// 	i_price = sqlite3_column_double(sqlite_stmt, 0);
		// 	strcpy(i_name, sqlite3_column_text(sqlite_stmt, 1));
		// 	strcpy(i_data, sqlite3_column_text(sqlite_stmt, 2));
		// }				

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "SELECT i_price, i_name, i_data FROM item WHERE i_id = %d;", ol_i_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) 
				goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			if(num_cols < 3) 
				goto sqlerr;

			i_price = safe_stof(sqlite_stmt->field_values[0][0].c_str());
			strncpy(i_name, sqlite_stmt->field_values[0][1].c_str(), sizeof(i_name)-1);
			i_name[sizeof(i_name)-1] = '\0';
			// strcpy(i_data, sqlite_stmt->field_values[0][2].c_str());
			strncpy(i_data, sqlite_stmt->field_values[0][2].c_str(), sizeof(i_data)-1);
			i_data[sizeof(i_data)-1] = '\0';
		}

		ResetSqlResult(sqlite_stmt);

		price[ol_num_seq[ol_number - 1]] = i_price;
		strncpy(iname[ol_num_seq[ol_number - 1]], i_name, 25);

		/* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr; */

#ifdef DEBUG
		printf("n %d\n",proceed);
#endif
		proceed = 7;

		/*EXEC_SQL SELECT s_quantity, s_data, s_dist_01, s_dist_02,
		                s_dist_03, s_dist_04, s_dist_05, s_dist_06,
		                s_dist_07, s_dist_08, s_dist_09, s_dist_10
			INTO :s_quantity, :s_data, :s_dist_01, :s_dist_02,
		             :s_dist_03, :s_dist_04, :s_dist_05, :s_dist_06,
		             :s_dist_07, :s_dist_08, :s_dist_09, :s_dist_10
		        FROM stock
		        WHERE s_i_id = :ol_i_id 
			AND s_w_id = :ol_supply_w_id
			FOR UPDATE;*/

		// sqlite_stmt = stmt[t_num][6];

		// sqlite3_bind_int64(sqlite_stmt, 1, ol_i_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, ol_supply_w_id);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 12) goto sqlerr;

		// 	s_quantity = sqlite3_column_int64(sqlite_stmt, 0);
		// 	strcpy(s_data, sqlite3_column_text(sqlite_stmt, 1));
		// 	strcpy(s_dist_01, sqlite3_column_text(sqlite_stmt, 2));
		// 	strcpy(s_dist_02, sqlite3_column_text(sqlite_stmt, 3));
		// 	strcpy(s_dist_03, sqlite3_column_text(sqlite_stmt, 4));
		// 	strcpy(s_dist_04, sqlite3_column_text(sqlite_stmt, 5));
		// 	strcpy(s_dist_05, sqlite3_column_text(sqlite_stmt, 6));
		// 	strcpy(s_dist_06, sqlite3_column_text(sqlite_stmt, 7));
		// 	strcpy(s_dist_07, sqlite3_column_text(sqlite_stmt, 8));
		// 	strcpy(s_dist_08, sqlite3_column_text(sqlite_stmt, 9));
		// 	strcpy(s_dist_09, sqlite3_column_text(sqlite_stmt, 10));
		// 	strcpy(s_dist_10, sqlite3_column_text(sqlite_stmt, 11));
		// }

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = %d AND s_w_id = %d;", ol_i_id, ol_supply_w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) 
				goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			if(num_cols < 12) 
				goto sqlerr;
				
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[0][0].empty()) {
				s_quantity = 0;
			} else {
				try {
					s_quantity = safe_stoi(sqlite_stmt->field_values[0][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting s_quantity: %s\n", sqlite_stmt->field_values[0][0].c_str());
					goto sqlerr;
				}
			}
			strcpy(s_data, sqlite_stmt->field_values[0][1].c_str());
			strcpy(s_dist_01, sqlite_stmt->field_values[0][2].c_str());
			strcpy(s_dist_02, sqlite_stmt->field_values[0][3].c_str());
			strcpy(s_dist_03, sqlite_stmt->field_values[0][4].c_str());
			strcpy(s_dist_04, sqlite_stmt->field_values[0][5].c_str());
			strcpy(s_dist_05, sqlite_stmt->field_values[0][6].c_str());
			strcpy(s_dist_06, sqlite_stmt->field_values[0][7].c_str());
			strcpy(s_dist_07, sqlite_stmt->field_values[0][8].c_str());
			strcpy(s_dist_08, sqlite_stmt->field_values[0][9].c_str());
			strcpy(s_dist_09, sqlite_stmt->field_values[0][10].c_str());
			strcpy(s_dist_10, sqlite_stmt->field_values[0][11].c_str());
		}

		ResetSqlResult(sqlite_stmt);

		pick_dist_info(ol_dist_info, d_id);	/* pick correct
							 * s_dist_xx */

		stock[ol_num_seq[ol_number - 1]] = s_quantity;

		if ((strstr(i_data, "original") != NULL) &&
		    (strstr(s_data, "original") != NULL))
			bg[ol_num_seq[ol_number - 1]] = 'B';
		else
			bg[ol_num_seq[ol_number - 1]] = 'G';

		if (s_quantity > ol_quantity)
			s_quantity = s_quantity - ol_quantity;
		else
			s_quantity = s_quantity - ol_quantity + 91;

#ifdef DEBUG
		printf("n %d\n",proceed);
#endif

		proceed = 8;
		/*EXEC_SQL UPDATE stock SET s_quantity = :s_quantity
		        WHERE s_i_id = :ol_i_id 
			AND s_w_id = :ol_supply_w_id;*/

		// sqlite_stmt = stmt[t_num][7];

		// sqlite3_bind_int64(sqlite_stmt, 1, s_quantity);
		// sqlite3_bind_int64(sqlite_stmt, 2, ol_i_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, ol_supply_w_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "UPDATE stock SET s_quantity = %d WHERE s_i_id = %d AND s_w_id = %d;", s_quantity, ol_i_id, ol_supply_w_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(!sqlite_stmt->success) 
			goto sqlerr;

		ResetSqlResult(sqlite_stmt);

		ol_amount = ol_quantity * i_price * (1 + w_tax + d_tax) * (1 - c_discount);
		amt[ol_num_seq[ol_number - 1]] = ol_amount;
		total += ol_amount;

#ifdef DEBUG
		printf("n %d\n",proceed);
#endif

		proceed = 9;
		/*EXEC_SQL INSERT INTO order_line (ol_o_id, ol_d_id, ol_w_id, 
						 ol_number, ol_i_id, 
						 ol_supply_w_id, ol_quantity, 
						 ol_amount, ol_dist_info)
			VALUES (:o_id, :d_id, :w_id, :ol_number, :ol_i_id,
				:ol_supply_w_id, :ol_quantity, :ol_amount,
				:ol_dist_info);*/

		// sqlite_stmt = stmt[t_num][8];

		// sqlite3_bind_int64(sqlite_stmt, 1, o_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, d_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, w_id);
		// sqlite3_bind_int64(sqlite_stmt, 4, ol_number);
		// sqlite3_bind_int64(sqlite_stmt, 5, ol_i_id);
		// sqlite3_bind_int64(sqlite_stmt, 6, ol_supply_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 7, ol_amount);
		// sqlite3_bind_double(sqlite_stmt, 8, ol_supply_w_id);
		// sqlite3_bind_text(sqlite_stmt, 9, ol_dist_info, -1, SQLITE_STATIC);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "INSERT INTO order_line  VALUES(%d, %d, %d, %d, %d, %d, 'NULL', %d, %f, '%s');", o_id, d_id, w_id, ol_number, ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_dist_info);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(!sqlite_stmt->success) 
			goto sqlerr;
		ResetSqlResult(sqlite_stmt);


	}			/* End Order Lines */

#ifdef DEBUG
	printf("insert 3\n");
	fflush(stdout);
#endif

	/*EXEC_SQL COMMIT WORK;*/
	//if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;

	clk1 = clock_gettime(CLOCK_REALTIME, &tbuf1 );

	delete sqlite_stmt;  // 删除分配的内存
	return (1);

invaliditem:
	/*EXEC_SQL ROLLBACK WORK;*/
	// if( sqlite3_exec(ctx[t_num], "ROLLBACK;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;

	/* printf("Item number is not valid\n"); */
	delete sqlite_stmt;  // 删除分配的内存
	return (1); /* OK? */

sqlerr:
    printf("FAILED at proceed=%d, SQL=%s\n", proceed, send_str);
    printf("RECV: %s\n", sqlite_stmt->rec_msg.c_str());
    printf("row_count=%d, field_count=%d\n", sqlite_stmt->row_count, sqlite_stmt->field_count);
    for(int i=0;i<sqlite_stmt->row_count;++i){
        for(int j=0;j<sqlite_stmt->field_count;++j){
            printf("field_values[%d][%d]=%s ", i, j, sqlite_stmt->field_values[i][j].c_str());
        }
        printf("\n");
    }
    fprintf(stderr,"neword %d:%d\n",t_num,proceed);
    printf("failed in neword\n");
    if(!sqlite_stmt->is_abort){
        outfile.open(file_path, std::ios::out | std::ios::app);
        outfile << "sql: " << "send_str " << send_str<<" exec failed" << std::endl;
        outfile << "recv: \n" << sqlite_stmt->rec_msg;
        outfile.close();
    }
    ResetSqlResult(sqlite_stmt);
    send_command(sockfd, "abort;", sqlite_stmt);
    send_command(sockfd, "begin;", sqlite_stmt);
    #if OUTPUT_TO_FILE
    outfile.open(file_path, std::ios::out | std::ios::app);
    outfile << "sql: " << "begin;" << std::endl;
    outfile << "recv: \n" << sqlite_stmt->rec_msg;
    outfile.close();
    #endif
    ResetSqlResult(sqlite_stmt);
    goto sqlerrerr;
sqlerrerr:
	delete sqlite_stmt;  // 删除分配的内存
	return (0);
}



