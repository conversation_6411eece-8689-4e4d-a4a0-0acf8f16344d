[{"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/simple_consistency_check.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/simple_consistency_check.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/simple_consistency_check.dir/support.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/test_db2025_compatibility.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/test_db2025_compatibility.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/start.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/start.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/start.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/spt_proc.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/driver.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/driver.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/driver.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/support.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/sequence.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sequence.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sequence.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/rthist.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/rthist.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/rthist.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sb_percentile.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sb_percentile.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/neword.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/neword.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/neword.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/payment.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/payment.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/payment.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/ordstat.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/ordstat.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/ordstat.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/delivery.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/delivery.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/delivery.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_start.dir/slev.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/slev.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/slev.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_load.dir/load.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/load.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/load.cpp"}, {"directory": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build", "command": "/usr/bin/c++   -I/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/.  -Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE   -std=gnu++17 -o CMakeFiles/tpcc_load.dir/support.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp", "file": "/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp"}]