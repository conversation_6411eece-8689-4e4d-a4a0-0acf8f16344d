begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
UPDATE warehouse SET w_ytd = w_ytd + 3836.000000 WHERE w_id = 11;
UPDATE warehouse SET w_ytd = w_ytd + 164.000000 WHERE w_id = 14;
begin;
begin;
begin;
begin;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 9 AND c_d_id = 5 AND c_id = 1577;
begin;
begin;
UPDATE warehouse SET w_ytd = w_ytd + 4518.000000 WHERE w_id = 13;
UPDATE warehouse SET w_ytd = w_ytd + 3885.000000 WHERE w_id = 28;
begin;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 14 AND c_d_id = 9 AND c_id = 1553;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 14;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 13;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 3 AND c_d_id = 10 AND c_id = 2085;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
UPDATE warehouse SET w_ytd = w_ytd + 4574.000000 WHERE w_id = 40;
begin;
begin;
begin;
begin;
UPDATE warehouse SET w_ytd = w_ytd + 2081.000000 WHERE w_id = 44;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 40;
SELECT d_next_o_id FROM district WHERE d_id = 6 AND d_w_id = 30;
UPDATE warehouse SET w_ytd = w_ytd + 2478.000000 WHERE w_id = 15;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 44;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 2 AND c_d_id = 1 AND c_id = 1304;
UPDATE warehouse SET w_ytd = w_ytd + 3347.000000 WHERE w_id = 33;
UPDATE warehouse SET w_ytd = w_ytd + 1645.000000 WHERE w_id = 48;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 33;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 35 AND c_d_id = 7 AND c_id = 23;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 3 AND c_d_id = 2 AND c_id = 1558;
UPDATE warehouse SET w_ytd = w_ytd + 4928.000000 WHERE w_id = 3;
UPDATE district SET d_ytd = d_ytd + 3347.000000 WHERE d_w_id = 33 AND d_id = 6;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 4 AND c_d_id = 1 AND c_id = 1685;
UPDATE warehouse SET w_ytd = w_ytd + 3858.000000 WHERE w_id = 44;
SELECT  ol_i_id FROM order_line WHERE ol_w_id = 30 AND ol_d_id = 6 AND ol_o_id < 3001 AND ol_o_id >= 2981;
UPDATE district SET d_ytd = d_ytd + 2081.000000 WHERE d_w_id = 44 AND d_id = 8;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 15;
UPDATE district SET d_ytd = d_ytd + 4574.000000 WHERE d_w_id = 40 AND d_id = 4;
SELECT w_tax FROM warehouse WHERE w_id = 4;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 10 AND c_d_id = 3 AND c_id = 825;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 46 AND c_d_id = 9 AND c_last = 'BARABLEBAR';
SELECT w_tax FROM warehouse WHERE w_id = 2;
UPDATE warehouse SET w_ytd = w_ytd + 3989.000000 WHERE w_id = 39;
SELECT w_tax FROM warehouse WHERE w_id = 10;
SELECT w_tax FROM warehouse WHERE w_id = 35;
SELECT MIN(no_o_id) as min_o_id FROM new_orders WHERE no_d_id = 1 AND no_w_id = 1;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 3;
SELECT w_tax FROM warehouse WHERE w_id = 3;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 33 AND d_id = 6;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 44;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 1 AND d_w_id = 4;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 48;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 7 AND d_w_id = 35;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 3 AND d_w_id = 10;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 40 AND d_id = 4;
UPDATE district SET d_ytd = d_ytd + 3858.000000 WHERE d_w_id = 44 AND d_id = 7;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 39;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 2 AND d_w_id = 3;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 27 AND c_d_id = 1 AND c_last = 'PRESESEOUGHT';
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 44 AND d_id = 8;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 1 AND d_w_id = 2;
UPDATE district SET d_ytd = d_ytd + 4928.000000 WHERE d_w_id = 3 AND d_id = 5;
UPDATE district SET d_ytd = d_ytd + 2478.000000 WHERE d_w_id = 15 AND d_id = 10;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 1 AND d_w_id = 4;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 2 AND d_w_id = 3;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 7 AND d_w_id = 35;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 3 AND d_w_id = 10;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 15 AND d_id = 10;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 44 AND d_id = 7;
INSERT INTO orders  VALUES(3001, 3, 10, 825, '2025-08-13 22:19:45',-1,  14, 1);
UPDATE district SET d_ytd = d_ytd + 1645.000000 WHERE d_w_id = 48 AND d_id = 10;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 44 AND c_d_id = 8 AND c_last = 'PRICALLYATION';
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 1 AND d_w_id = 2;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 3 AND d_id = 5;
INSERT INTO orders  VALUES(3001, 1, 2, 1304, '2025-08-13 22:19:45',-1,  11, 1);
SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = 40 AND c_d_id = 4 AND c_id = 1210;
INSERT INTO orders  VALUES(3001, 1, 4, 1685, '2025-08-13 22:19:45',-1,  5, 1);
INSERT INTO orders  VALUES(3001, 7, 35, 23, '2025-08-13 22:19:45',-1,  13, 1);
UPDATE district SET d_ytd = d_ytd + 3989.000000 WHERE d_w_id = 39 AND d_id = 2;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 15 AND c_d_id = 10 AND c_last = 'OUGHTOUGHTESE';
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 44 AND c_d_id = 7 AND c_last = 'CALLYATIONESE';
INSERT INTO new_orders  VALUES(3001, 3, 10);
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 48 AND d_id = 10;
INSERT INTO new_orders  VALUES(3001, 1, 2);
INSERT INTO orders  VALUES(3001, 2, 3, 1558, '2025-08-13 22:19:45',-1,  6, 0);
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 3 AND c_d_id = 5 AND c_last = 'ANTIPRIESE';
UPDATE customer SET c_balance = -4563.500000 WHERE c_w_id = 40 AND c_d_id = 4 AND c_id = 1210;
INSERT INTO new_orders  VALUES(3001, 1, 4);
INSERT INTO new_orders  VALUES(3001, 7, 35);
SELECT i_price, i_name, i_data FROM item WHERE i_id = 5078;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 9686;
INSERT INTO new_orders  VALUES(3001, 2, 3);
SELECT i_price, i_name, i_data FROM item WHERE i_id = 7123;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 39 AND d_id = 2;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 21208;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 18515;
SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = 48 AND c_d_id = 10 AND c_id = 2617;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 9686 AND s_w_id = 10;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 5078 AND s_w_id = 2;
SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = 39 AND c_d_id = 2 AND c_id = 185;
INSERT INTO history VALUES(1210, 4, 40, 4, 40, '2025-08-13 22:19:45', 4574.000000, 'KdqU9Zpgs0Wy5Z3nsJ');
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 21208 AND s_w_id = 3;
abort;
abort;
abort;
commit;
UPDATE customer SET c_balance = -1634.500000 WHERE c_w_id = 48 AND c_d_id = 10 AND c_id = 2617;
UPDATE customer SET c_balance = -3978.500000 WHERE c_w_id = 39 AND c_d_id = 2 AND c_id = 185;
begin;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 7123 AND s_w_id = 4;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 10 AND c_d_id = 3 AND c_id = 825;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 18515 AND s_w_id = 35;
begin;
begin;
INSERT INTO history VALUES(185, 2, 39, 2, 39, '2025-08-13 22:19:45', 3989.000000, 'eiWxWmyftD9EgSoffQ');
INSERT INTO history VALUES(2617, 10, 48, 10, 48, '2025-08-13 22:19:45', 1645.000000, 'm34QITocBIfMErpBfy');
begin;
abort;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 3 AND c_d_id = 2 AND c_id = 1558;
SELECT w_tax FROM warehouse WHERE w_id = 10;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 2 AND c_d_id = 1 AND c_id = 1304;
commit;
abort;
UPDATE warehouse SET w_ytd = w_ytd + 4279.000000 WHERE w_id = 48;
SELECT w_tax FROM warehouse WHERE w_id = 2;
begin;
SELECT w_tax FROM warehouse WHERE w_id = 3;
commit;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 3 AND d_w_id = 10;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 2 AND d_w_id = 3;
begin;
begin;
begin;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 1 AND d_w_id = 2;
abort;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 2 AND d_w_id = 3;
UPDATE warehouse SET w_ytd = w_ytd + 4414.000000 WHERE w_id = 29;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 2 AND c_d_id = 7 AND c_id = 9;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 35 AND c_d_id = 7 AND c_id = 23;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 4 AND c_d_id = 1 AND c_id = 1685;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 3 AND d_w_id = 10;
begin;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 1 AND d_w_id = 2;
INSERT INTO orders  VALUES(3001, 2, 3, 1558, '2025-08-13 22:19:45',-1,  6, 0);
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 29;
SELECT w_tax FROM warehouse WHERE w_id = 2;
SELECT w_tax FROM warehouse WHERE w_id = 35;
UPDATE warehouse SET w_ytd = w_ytd + 4279.000000 WHERE w_id = 48;
INSERT INTO orders  VALUES(3001, 3, 10, 825, '2025-08-13 22:19:45',-1,  14, 1);
INSERT INTO new_orders  VALUES(3001, 2, 3);
SELECT w_tax FROM warehouse WHERE w_id = 4;
INSERT INTO orders  VALUES(3001, 1, 2, 1304, '2025-08-13 22:19:45',-1,  11, 1);
UPDATE district SET d_ytd = d_ytd + 4414.000000 WHERE d_w_id = 29 AND d_id = 7;
INSERT INTO new_orders  VALUES(3001, 1, 2);
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 7 AND d_w_id = 2;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 7 AND d_w_id = 35;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 48;
INSERT INTO new_orders  VALUES(3001, 3, 10);
SELECT i_price, i_name, i_data FROM item WHERE i_id = 5078;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 21208;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 1 AND d_w_id = 4;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 29 AND d_id = 7;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 7 AND d_w_id = 2;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 7 AND d_w_id = 35;
UPDATE district SET d_ytd = d_ytd + 4279.000000 WHERE d_w_id = 48 AND d_id = 5;
INSERT INTO orders  VALUES(3001, 7, 2, 9, '2025-08-13 22:19:45',-1,  8, 1);
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 5078 AND s_w_id = 2;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 21208 AND s_w_id = 3;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 1 AND d_w_id = 4;
abort;
INSERT INTO orders  VALUES(3001, 7, 35, 23, '2025-08-13 22:19:45',-1,  13, 1);
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 48 AND d_id = 5;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 9686;
INSERT INTO new_orders  VALUES(3001, 7, 2);
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
begin;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 25 AND c_d_id = 4 AND c_id = 2521;
begin;
begin;
begin;
begin;
SELECT MIN(no_o_id) as min_o_id FROM new_orders WHERE no_d_id = 1 AND no_w_id = 1;
begin;
begin;
UPDATE warehouse SET w_ytd = w_ytd + 1934.000000 WHERE w_id = 16;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 38 AND c_d_id = 3 AND c_id = 2863;
UPDATE warehouse SET w_ytd = w_ytd + 3111.000000 WHERE w_id = 47;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 21 AND c_d_id = 9 AND c_id = 1673;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 18 AND c_d_id = 4 AND c_id = 867;
SELECT w_tax FROM warehouse WHERE w_id = 25;
SELECT d_next_o_id FROM district WHERE d_id = 2 AND d_w_id = 29;
UPDATE warehouse SET w_ytd = w_ytd + 3129.000000 WHERE w_id = 39;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 22 AND c_d_id = 4 AND c_id = 1645;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 39;
SELECT w_tax FROM warehouse WHERE w_id = 18;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 35 AND c_d_id = 4 AND c_last = 'CALLYOUGHTPRI';
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 42 AND c_d_id = 7 AND c_id = 1825;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 47 AND c_d_id = 10 AND c_id = 528;
UPDATE warehouse SET w_ytd = w_ytd + 1482.000000 WHERE w_id = 24;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 47;
SELECT w_tax FROM warehouse WHERE w_id = 38;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 24;
SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = 16;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 4 AND d_w_id = 25;
UPDATE district SET d_ytd = d_ytd + 1934.000000 WHERE d_w_id = 16 AND d_id = 9;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 25 AND c_d_id = 3 AND c_id = 1957;
UPDATE district SET d_ytd = d_ytd + 3129.000000 WHERE d_w_id = 39 AND d_id = 2;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 4 AND d_w_id = 18;
SELECT w_tax FROM warehouse WHERE w_id = 22;
SELECT w_tax FROM warehouse WHERE w_id = 42;
SELECT w_tax FROM warehouse WHERE w_id = 21;
SELECT w_tax FROM warehouse WHERE w_id = 47;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 16 AND d_id = 9;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 4 AND d_w_id = 25;
UPDATE district SET d_ytd = d_ytd + 1482.000000 WHERE d_w_id = 24 AND d_id = 10;
UPDATE district SET d_ytd = d_ytd + 3111.000000 WHERE d_w_id = 47 AND d_id = 7;
SELECT w_tax FROM warehouse WHERE w_id = 25;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 3 AND d_w_id = 38;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 39 AND d_id = 2;
SELECT  ol_i_id FROM order_line WHERE ol_w_id = 29 AND ol_d_id = 2 AND ol_o_id < 3001 AND ol_o_id >= 2981;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 4 AND d_w_id = 18;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 16 AND c_d_id = 9 AND c_last = 'ESECALLYEING';
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 7 AND d_w_id = 42;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 9 AND d_w_id = 21;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 10 AND d_w_id = 47;
INSERT INTO orders  VALUES(3001, 4, 25, 2521, '2025-08-13 22:21:17',-1,  11, 1);
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 47 AND d_id = 7;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 3 AND d_w_id = 38;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 3 AND d_w_id = 25;
SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = 24 AND d_id = 10;
INSERT INTO orders  VALUES(3001, 4, 18, 867, '2025-08-13 22:21:17',-1,  11, 1);
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 7 AND d_w_id = 42;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 4 AND d_w_id = 22;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 9 AND d_w_id = 21;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 10 AND d_w_id = 47;
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 47 AND c_d_id = 7 AND c_last = 'ANTIEINGATION';
SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = 39 AND c_d_id = 2 AND c_last = 'EINGPRIEING';
INSERT INTO new_orders  VALUES(3001, 4, 25);
INSERT INTO orders  VALUES(3001, 3, 38, 2863, '2025-08-13 22:21:17',-1,  7, 1);
SELECT i_price, i_name, i_data FROM item WHERE i_id = 11122;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 3 AND d_w_id = 25;
INSERT INTO new_orders  VALUES(3001, 4, 18);
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 4 AND d_w_id = 22;
INSERT INTO orders  VALUES(3001, 10, 47, 528, '2025-08-13 22:21:17',-1,  14, 1);
INSERT INTO orders  VALUES(3001, 7, 42, 1825, '2025-08-13 22:21:17',-1,  9, 1);
INSERT INTO orders  VALUES(3001, 9, 21, 1673, '2025-08-13 22:21:17',-1,  13, 0);
INSERT INTO new_orders  VALUES(3001, 3, 38);
SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = 24 AND c_d_id = 10 AND c_id = 1521;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 12130;
INSERT INTO orders  VALUES(3001, 4, 22, 1645, '2025-08-13 22:21:17',-1,  15, 1);
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 11122 AND s_w_id = 25;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 12130 AND s_w_id = 18;
INSERT INTO new_orders  VALUES(3001, 7, 42);
INSERT INTO new_orders  VALUES(3001, 9, 21);
INSERT INTO orders  VALUES(3001, 3, 25, 1957, '2025-08-13 22:21:17',-1,  6, 1);
SELECT i_price, i_name, i_data FROM item WHERE i_id = 29194;
INSERT INTO new_orders  VALUES(3001, 10, 47);
UPDATE customer SET c_balance = -1471.500000 WHERE c_w_id = 24 AND c_d_id = 10 AND c_id = 1521;
abort;
INSERT INTO new_orders  VALUES(3001, 4, 22);
abort;
INSERT INTO new_orders  VALUES(3001, 3, 25);
SELECT i_price, i_name, i_data FROM item WHERE i_id = 10514;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 23374;
begin;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 29194 AND s_w_id = 38;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 25 AND c_d_id = 4 AND c_id = 2521;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 814;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 10514 AND s_w_id = 42;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 12845;
SELECT i_price, i_name, i_data FROM item WHERE i_id = 7018;
INSERT INTO history VALUES(1521, 10, 24, 10, 24, '2025-08-13 22:21:17', 1482.000000, 'iBpaWwqkuVhmnRh0Ib');
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 23374 AND s_w_id = 1;
abort;
SELECT w_tax FROM warehouse WHERE w_id = 25;
begin;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 814 AND s_w_id = 22;
SELECT d_next_o_id, d_tax FROM district WHERE d_id = 4 AND d_w_id = 25;
abort;
commit;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 7018 AND s_w_id = 47;
begin;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 18 AND c_d_id = 4 AND c_id = 867;
abort;
begin;
begin;
abort;
begin;
SELECT c_discount, c_last, c_credit FROM customer WHERE c_w_id = 38 AND c_d_id = 3 AND c_id = 2863;
UPDATE district SET d_next_o_id = d_next_o_id + 1 WHERE d_id = 4 AND d_w_id = 25;
SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = 12845 AND s_w_id = 25;
begin;
