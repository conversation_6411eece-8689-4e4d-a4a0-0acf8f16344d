// 这个文件用于记录需要修复的stoi调用位置
// 需要修复的文件：
// 1. payment.cpp - 已修复
// 2. neword.cpp - 已修复
// 3. slev.cpp - 需要修复
// 4. delivery.cpp - 需要修复
// 5. ordstat.cpp - 需要修复

// 修复模式：
// 将：
// variable = std::stoi(sqlite_stmt->field_values[row][col].c_str());
// 
// 替换为：
// if(sqlite_stmt->field_values[row][col].empty()) {
//     variable = 0;
// } else {
//     try {
//         variable = std::stoi(sqlite_stmt->field_values[row][col].c_str());
//     } catch (const std::exception& e) {
//         printf("Error converting variable: %s\n", sqlite_stmt->field_values[row][col].c_str());
//         goto sqlerr;
//     }
// } 