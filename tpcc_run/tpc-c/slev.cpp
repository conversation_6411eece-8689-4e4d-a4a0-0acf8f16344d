/*
 * -*-C-*-
 * slev.pc 
 * corresponds to A.5 in appendix A
 */

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <thread>
#include "sstream"
#include "fstream"
// #include <sqlite3.h>

#include "spt_proc.h"
#include "tpc.h"

// extern sqlite3 **ctx;
// extern sqlite3_stmt ***stmt;
// extern void send_command(SqlResult* result, const char* command);
extern void send_command(int sockfd, const char* command, SqlResult* result);
extern std::atomic<int> atomic_array[20];


/*
 * the stock level transaction
 */
int slev( int t_num,
	  int w_id_arg,		/* warehouse id */
	  int d_id_arg,		/* district id */
	  int level_arg		/* stock level */
)
{
	printf("[SLEV] t_num=%d, w_id=%d, d_id=%d, level=%d\n", t_num, w_id_arg, d_id_arg, level_arg);
	int ret;
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            level = level_arg;
	int            d_next_o_id = 0;
	int            i_count;
	int            ol_i_id = -1;
		int ids[20];
	int cnt = 0;
	// 获取当前线程id
	std::thread::id threadId = std::this_thread::get_id();
	std::stringstream ss;
	ss << threadId;
	std::string threadIdStr = ss.str();

	std::string file_path = "slev_" + threadIdStr + ".txt";
	std::fstream outfile;
	SqlResult *sqlite_stmt = new SqlResult();
	SqlResult* sqlite_stmt2 = new SqlResult();
	int sockfd = atomic_array[t_num];
	ResetSqlResult(sqlite_stmt);
	
	int num_cols;

	char send_str[500];

	
	/*EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/

	/* find the next order id */
	printf("[SLEV] 1: SELECT d_next_o_id FROM district WHERE d_id = %d AND d_w_id = %d;\n", d_id, w_id);
#ifdef DEBUG
	printf("select 1\n");
#endif
	/*EXEC_SQL SELECT d_next_o_id
	                INTO :d_next_o_id
	                FROM district
	                WHERE d_id = :d_id
			AND d_w_id = :w_id;*/
	// sqlite_stmt = stmt[t_num][32];

	// sqlite3_bind_int64(sqlite_stmt, 1, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, w_id);

	// ret = sqlite3_step(sqlite_stmt);
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 1) goto sqlerr;

	// 	d_next_o_id = sqlite3_column_int64(sqlite_stmt, 0);
	// }

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "SELECT d_next_o_id FROM district WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);
	send_command(sockfd, send_str, sqlite_stmt);
	printf("[SLEV] 1: SQL success=%d, row_count=%d, field_count=%d\n", sqlite_stmt->success, sqlite_stmt->row_count, sqlite_stmt->field_count);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif

	if(sqlite_stmt->success){
		if(sqlite_stmt->row_count < 1) goto sqlerr;
		num_cols = sqlite_stmt->field_count;

		if (num_cols != 1) goto sqlerr;

		// 添加错误处理，防止空字符串导致的stoi异常
		if(sqlite_stmt->field_values[0][0].empty()) {
			d_next_o_id = 0;
		} else {
			try {
				d_next_o_id = std::stoi(sqlite_stmt->field_values[0][0].c_str());
			} catch (const std::exception& e) {
				printf("Error converting d_next_o_id: %s\n", sqlite_stmt->field_values[0][0].c_str());
				goto sqlerr;
			}
		}
	}
	printf("[SLEV] d_next_o_id=%d\n", d_next_o_id);
	ResetSqlResult(sqlite_stmt);

	/* find the most recent 20 orders for this district */
	printf("[SLEV] 2: SELECT ol_i_id FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d AND ol_o_id < %d AND ol_o_id >= %d;\n", w_id, d_id, d_next_o_id, d_next_o_id - 20);
	/*EXEC_SQL DECLARE ord_line CURSOR FOR
	                SELECT DISTINCT ol_i_id
	                FROM order_line
	                WHERE ol_w_id = :w_id
			AND ol_d_id = :d_id
			AND ol_o_id < :d_next_o_id
			AND ol_o_id >= (:d_next_o_id - 20);

	EXEC_SQL OPEN ord_line;*/
	// sqlite_stmt = stmt[t_num][33];

	// sqlite3_bind_int64(sqlite_stmt, 1, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, w_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, d_next_o_id);
	// sqlite3_bind_int64(sqlite_stmt, 4, d_next_o_id);

	memset(send_str, 0, 500);

	sprintf(send_str, "SELECT  ol_i_id FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d AND ol_o_id < %d AND ol_o_id >= %d;", w_id, d_id, d_next_o_id, d_next_o_id - 20);
	send_command(sockfd, send_str, sqlite_stmt);
	printf("[SLEV] 2: SQL success=%d, row_count=%d, field_count=%d\n", sqlite_stmt->success, sqlite_stmt->row_count, sqlite_stmt->field_count);
	if(!sqlite_stmt->success) goto sqlerr;


	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << send_str << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	cnt = 0;
	for(int i = 0; i < sqlite_stmt->row_count && cnt < 20; i++) {
		// num_cols = sqlite3_column_count(sqlite_stmt);
		num_cols = sqlite_stmt->field_count;
		if (num_cols != 1) goto sqlerr;
		// ol_i_id = sqlite3_column_int64(sqlite_stmt, 0);
		// 添加错误处理，防止空字符串导致的stoi异常
		if(sqlite_stmt->field_values[i][0].empty()) {
			ol_i_id = 0;
		} else {
			try {
				ol_i_id = std::stoi(sqlite_stmt->field_values[i][0].c_str());
			} catch (const std::exception& e) {
				printf("Error converting ol_i_id: %s\n", sqlite_stmt->field_values[i][0].c_str());
				goto sqlerr;
			}
		}
		printf("[SLEV] ol_i_id=%d\n", ol_i_id);
		int duplicate = 0;
		for(int j = 0; j < cnt; j++){
			if(ids[j] == ol_i_id){
				duplicate = 1;
			}
		}
		if(duplicate){
			printf("[SLEV] duplicate ol_i_id=%d\n", ol_i_id);
			continue;
		}
		ids[cnt++] = ol_i_id;

		/*EXEC_SQL SELECT count(*) INTO :i_count
			FROM stock
			WHERE s_w_id = :w_id
		        AND s_i_id = :ol_i_id
			AND s_quantity < :level;*/
		// sqlite_stmt2 = stmt[t_num][34];

		printf("[SLEV] check stock for ol_i_id=%d\n", ol_i_id);
		sqlite_stmt2->sockfd = sqlite_stmt->sockfd;
		sqlite_stmt2->field_count = 0;
		sqlite_stmt2->row_count = 0;
		sqlite_stmt2->field_values.clear();
		sqlite_stmt2->field_names.clear();
		sqlite_stmt2->success = 0;
		// sqlite3_bind_int64(sqlite_stmt, 1, w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, ol_i_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, level);
		memset(send_str, 0, 500);
		sprintf(send_str, "SELECT count(*) as count_stock FROM stock WHERE s_w_id = %d AND s_i_id = %d AND s_quantity < %d;", w_id, ol_i_id, level);
		send_command(sockfd, send_str, sqlite_stmt2);
		printf("[SLEV] 3: SQL success=%d, row_count=%d, field_count=%d\n", sqlite_stmt2->success, sqlite_stmt2->row_count, sqlite_stmt2->field_count);
		if(!sqlite_stmt2->success) goto sqlerr2;


		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt2->rec_msg;
		outfile.close();
		#endif

		// ret = sqlite3_step(sqlite_stmt2);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt2);
		// 	if (num_cols != 1) goto sqlerr;
		// 	i_count = sqlite3_column_int64(sqlite_stmt2, 0);
		// }

		if(sqlite_stmt2->success ){
			if(sqlite_stmt2->row_count < 1) goto sqlerr2;
			num_cols = sqlite_stmt2->field_count;
			if (num_cols != 1) goto sqlerr2;
			// Get the count value from the result
			if(sqlite_stmt2->field_values[0][0].empty()) {
				i_count = 0;
			} else {
				try {
					i_count = std::stoi(sqlite_stmt2->field_values[0][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting i_count: %s\n", sqlite_stmt2->field_values[0][0].c_str());
					goto sqlerr2;
				}
			}
		}
		printf("[SLEV] stock check result: i_count=%d\n", i_count);
		ResetSqlResult(sqlite_stmt2);
	}

	// sqlite3_reset(sqlite_stmt);

	ResetSqlResult(sqlite_stmt);
	printf("[SLEV] done, cnt=%d\n", cnt);

done:
	/*EXEC_SQL CLOSE ord_line;*/
	/*EXEC_SQL COMMIT WORK;*/
	//if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	delete sqlite_stmt;  // 删除分配的内存
	delete sqlite_stmt2; // 删除分配的内存
	return (1);

sqlerr:
	fprintf(stderr,"slev\n");
	// printf("%s: error: %s\n", __func__, sqlite3_errmsg(ctx[t_num]));
	printf("failed in slev1\n");
	if(!sqlite_stmt->is_abort){
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << "send_str " << send_str<<" exec failed" << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
	}
	ResetSqlResult(sqlite_stmt);
	send_command(sockfd, "abort;", sqlite_stmt);
	send_command(sockfd, "begin;", sqlite_stmt);
	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << "begin;" << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	ResetSqlResult(sqlite_stmt);
	delete sqlite_stmt;  // 删除分配的内存
	delete sqlite_stmt2; // 删除分配的内存
	return (0);

sqlerr2:
	fprintf(stderr,"slev\n");
	// printf("%s: error: %s\n", __func__, sqlite3_errmsg(ctx[t_num]));
	printf("failed in slev2\n");
	if(!sqlite_stmt2->is_abort){
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << "send_str " << send_str<<" exec failed" << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
	}
	ResetSqlResult(sqlite_stmt);
	send_command(sockfd, "abort;", sqlite_stmt);
	send_command(sockfd, "begin;", sqlite_stmt);
	ResetSqlResult(sqlite_stmt);
	ResetSqlResult(sqlite_stmt2);
	delete sqlite_stmt;  // 删除分配的内存
	delete sqlite_stmt2; // 删除分配的内存
	return (0);
}
