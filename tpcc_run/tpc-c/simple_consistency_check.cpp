/*
 * simple_consistency_check.cpp
 * 简化的数据一致性检验程序
 */

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <iostream>
#include <string>
#include <vector>
#include <sstream>
#include <fstream>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netdb.h>
#include <errno.h>
#include "tpc.h"

#define PORT_DEFAULT 8765

// 安全的字符串转换函数
inline int safe_stoi(const std::string& str, int default_value = 0) {
    if (str.empty()) return default_value;
    try { return std::stoi(str); }
    catch (...) { return default_value; }
}

// 简化的TCP连接函数
int init_tcp_sock(const char *server_host, int server_port) {
    struct hostent *host;
    struct sockaddr_in serv_addr;
    int sockfd;
    
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        printf("socket error\n");
        return -1;
    }
    
    host = gethostbyname(server_host);
    if (host == NULL) {
        printf("gethostbyname error\n");
        return -1;
    }
    
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(server_port);
    serv_addr.sin_addr = *((struct in_addr *)host->h_addr);
    
    if (connect(sockfd, (struct sockaddr *)&serv_addr, sizeof(struct sockaddr)) == -1) {
        printf("connect error\n");
        return -1;
    }
    
    return sockfd;
}

// 简化的命令发送函数
void send_command(int sockfd, const char* command, SqlResult* result) {
    printf("DEBUG: 发送SQL命令: %s\n", command);
    
    // 发送SQL命令到db2025数据库
    if (write(sockfd, command, strlen(command) + 1) == -1) {
        printf("send error: %d:%s \n", errno, strerror(errno));
        exit(1);
    }

    // 接收数据库响应
    char recv_buf[8192];
    int len = recv(sockfd, recv_buf, sizeof(recv_buf), 0);
    if (len < 0) {
        fprintf(stderr, "Connection was broken: %s\n", strerror(errno));
        exit(1);
    } else if (len == 0) {
        printf("Connection has been closed\n");
        exit(1);
    } else {
        // 处理db2025数据库的响应格式
        std::string response(recv_buf, len);
        
        printf("DEBUG: 数据库响应: %s\n", response.c_str());
        
        // 检查是否成功
        if (response.find("Error") != std::string::npos || 
            response.find("error") != std::string::npos) {
            printf("DEBUG: SQL执行失败: %s\n", response.c_str());
            result->success = 0;
            result->rec_msg = response;
            return;
        }
        
        // 检查是否中止
        if (response.find("abort") != std::string::npos) {
            result->success = 0;
            result->is_abort = 1;
            result->rec_msg = response;
            return;
        }
        
        // 成功响应
        result->success = 1;
        result->rec_msg = response;
        
        printf("DEBUG: SQL执行成功\n");
    }
}

// 简化的响应解析函数
void parse_db2025_response(const std::string& response, SqlResult* result) {
    result->field_names.clear();
    result->field_values.clear();
    result->field_count = 0;
    result->row_count = 0;
    std::istringstream stream(response);
    std::string line;
    enum ParseState { WAIT_HEADER, READ_HEADER, WAIT_DATA, READ_DATA, DONE } state = WAIT_HEADER;
    while (std::getline(stream, line)) {
        if (line.empty()) continue;
        if (line.find("Total record(s):") != std::string::npos) break;
        if (line.find("+") != std::string::npos) {
            if (state == WAIT_HEADER) state = READ_HEADER;
            else if (state == WAIT_DATA) state = READ_DATA;
            else if (state == READ_HEADER) state = WAIT_DATA;
            else if (state == READ_DATA) state = DONE;
            continue;
        }
        if (state == READ_HEADER && line.find("|") != std::string::npos) {
            // 解析表头
            std::vector<std::string> headers;
            size_t start = 0, end = 0;
            while ((end = line.find("|", start)) != std::string::npos) {
                std::string field = line.substr(start, end - start);
                start = end + 1;
                field.erase(0, field.find_first_not_of(" \t"));
                field.erase(field.find_last_not_of(" \t") + 1);
                if (!field.empty()) headers.push_back(field);
            }
            if (!headers.empty()) {
                result->field_names = headers;
                result->field_count = headers.size();
            }
            state = WAIT_DATA;
            continue;
        }
        if (state == READ_DATA && line.find("|") != std::string::npos) {
            // 解析数据行
            std::vector<std::string> values;
            size_t start = 0, end = 0;
            while ((end = line.find("|", start)) != std::string::npos) {
                std::string val = line.substr(start, end - start);
                start = end + 1;
                val.erase(0, val.find_first_not_of(" \t"));
                val.erase(val.find_last_not_of(" \t") + 1);
                if (!val.empty()) values.push_back(val);
            }
            if (!values.empty()) {
                result->field_values.push_back(values);
                result->row_count++;
                printf("DEBUG: 解析到一行数据: ");
                for (const auto& v : values) printf("[%s] ", v.c_str());
                printf("\n");
            }
            state = DONE;
            continue;
        }
        if (state == DONE) break;
    }
    printf("DEBUG: 解析完成 - 字段数: %d, 行数: %d\n", result->field_count, result->row_count);
}

// 执行SQL查询并返回结果
int execute_query(int sockfd, const char* sql, SqlResult* result) {
    send_command(sockfd, sql, result);
    if (!result->success) {
        printf("SQL执行失败: %s\n", result->rec_msg.c_str());
        return -1;
    }
    parse_db2025_response(result->rec_msg, result);
    return 0;
}

// 获取单个整数值的辅助函数
int get_single_int_value(int sockfd, const char* sql, int default_value = 0) {
    SqlResult result;
    if (execute_query(sockfd, sql, &result) != 0) {
        return default_value;
    }
    
    if (result.row_count > 0 && result.field_values.size() > 0) {
        return safe_stoi(result.field_values[0][0], default_value);
    }
    return default_value;
}

// 一致性检验结果结构
struct ConsistencyResult {
    bool passed;
    std::string description;
    std::string details;
};

// 全局输出文件路径
std::string failed_log_path = "consistency_failed.log";

void log_failed_check(const std::string& sql, const SqlResult& result, const std::string& check_desc) {
    std::ofstream ofs(failed_log_path, std::ios::app);
    ofs << "==== 一致性检查失败 ====" << std::endl;
    ofs << "检查类型: " << check_desc << std::endl;
    ofs << "SQL: " << sql << std::endl;
    ofs << "数据库返回: " << std::endl << result.rec_msg << std::endl;
    ofs << "========================" << std::endl << std::endl;
}

// 检验1: district, orders 和 new_orders 的一致性
ConsistencyResult check_district_orders_consistency(int sockfd, int w_id, int d_id) {
    ConsistencyResult result;
    result.passed = false;
    
    // 查询 d_next_o_id
    char sql1[256];
    sprintf(sql1, "SELECT d_next_o_id FROM district WHERE d_w_id=%d AND d_id=%d;", w_id, d_id);
    int d_next_o_id = get_single_int_value(sockfd, sql1);
    
    // 查询 MAX(o_id)
    char sql2[256];
    sprintf(sql2, "SELECT MAX(o_id) as max_o_id FROM orders WHERE o_w_id=%d AND o_d_id=%d;", w_id, d_id);
    int max_o_id = get_single_int_value(sockfd, sql2);
    
    // 查询 MAX(no_o_id)
    char sql3[256];
    sprintf(sql3, "SELECT MAX(no_o_id) as max_no_o_id FROM new_orders WHERE no_w_id=%d AND no_d_id=%d;", w_id, d_id);
    int max_no_o_id = get_single_int_value(sockfd, sql3);
    
    // 检查一致性条件: d_next_o_id - 1 = max(o_id) = max(no_o_id)
    bool condition1 = (d_next_o_id - 1) == max_o_id;
    bool condition2 = max_o_id == max_no_o_id;
    
    result.passed = condition1 && condition2;
    
    std::ostringstream details;
    details << "w_id=" << w_id << ", d_id=" << d_id << "\n";
    details << "d_next_o_id=" << d_next_o_id << "\n";
    details << "max_o_id=" << max_o_id << "\n";
    details << "max_no_o_id=" << max_no_o_id << "\n";
    details << "d_next_o_id-1=" << (d_next_o_id - 1) << "\n";
    details << "条件1 (d_next_o_id-1 == max_o_id): " << (condition1 ? "通过" : "失败") << "\n";
    details << "条件2 (max_o_id == max_no_o_id): " << (condition2 ? "通过" : "失败") << "\n";
    
    result.details = details.str();
    result.description = "district, orders 和 new_orders 一致性检验";
    
    return result;
}

// 检验2: new_orders 的一致性
ConsistencyResult check_new_orders_consistency(int sockfd, int w_id, int d_id) {
    ConsistencyResult result;
    result.passed = false;
    
    // 查询 COUNT(no_o_id)
    char sql1[256];
    sprintf(sql1, "SELECT COUNT(no_o_id) as count_no_o_id FROM new_orders WHERE no_w_id=%d AND no_d_id=%d;", w_id, d_id);
    SqlResult res1; execute_query(sockfd, sql1, &res1);
    int count_no_o_id = 0;
    if (res1.row_count > 0 && res1.field_values.size() > 0) count_no_o_id = safe_stoi(res1.field_values[0][0]);
    
    // 查询 MAX(no_o_id)
    char sql2[256];
    sprintf(sql2, "SELECT MAX(no_o_id) as max_no_o_id FROM new_orders WHERE no_w_id=%d AND no_d_id=%d;", w_id, d_id);
    SqlResult res2; execute_query(sockfd, sql2, &res2);
    int max_no_o_id = 0;
    if (res2.row_count > 0 && res2.field_values.size() > 0) max_no_o_id = safe_stoi(res2.field_values[0][0]);
    
    // 查询 MIN(no_o_id)
    char sql3[256];
    sprintf(sql3, "SELECT MIN(no_o_id) as min_no_o_id FROM new_orders WHERE no_w_id=%d AND no_d_id=%d;", w_id, d_id);
    SqlResult res3; execute_query(sockfd, sql3, &res3);
    int min_no_o_id = 0;
    if (res3.row_count > 0 && res3.field_values.size() > 0) min_no_o_id = safe_stoi(res3.field_values[0][0]);
    
    // 检查一致性条件: count(no_o_id) = max(no_o_id) - min(no_o_id) + 1
    int expected_count = max_no_o_id - min_no_o_id + 1;
    result.passed = (count_no_o_id == expected_count);
    
    std::ostringstream details;
    details << "w_id=" << w_id << ", d_id=" << d_id << "\n";
    details << "count_no_o_id=" << count_no_o_id << "\n";
    details << "max_no_o_id=" << max_no_o_id << "\n";
    details << "min_no_o_id=" << min_no_o_id << "\n";
    details << "expected_count=" << expected_count << "\n";
    details << "条件 (count == max-min+1): " << (result.passed ? "通过" : "失败") << "\n";
    
    result.details = details.str();
    result.description = "new_orders 一致性检验";
    
    // 记录失败
    if (!result.passed) {
        log_failed_check(sql1, res1, result.description);
        log_failed_check(sql2, res2, result.description);
        log_failed_check(sql3, res3, result.description);
    }
    return result;
}

// 检验3: orders 和 order_line 的一致性
ConsistencyResult check_orders_orderline_consistency(int sockfd, int w_id, int d_id) {
    ConsistencyResult result;
    result.passed = false;
    
    // 查询 SUM(o_ol_cnt)
    char sql1[256];
    sprintf(sql1, "SELECT SUM(o_ol_cnt) as sum_ol_cnt FROM orders WHERE o_w_id=%d AND o_d_id=%d;", w_id, d_id);
    int sum_ol_cnt = get_single_int_value(sockfd, sql1);
    
    // 查询 COUNT(ol_o_id)
    char sql2[256];
    sprintf(sql2, "SELECT COUNT(ol_o_id) as count_ol_o_id FROM order_line WHERE ol_w_id=%d AND ol_d_id=%d;", w_id, d_id);
    int count_ol_o_id = get_single_int_value(sockfd, sql2);
    
    // 检查一致性条件: sum(o_ol_cnt) = count(ol_o_id)
    result.passed = (sum_ol_cnt == count_ol_o_id);
    
    std::ostringstream details;
    details << "w_id=" << w_id << ", d_id=" << d_id << "\n";
    details << "sum_ol_cnt=" << sum_ol_cnt << "\n";
    details << "count_ol_o_id=" << count_ol_o_id << "\n";
    details << "条件 (sum == count): " << (result.passed ? "通过" : "失败") << "\n";
    
    result.details = details.str();
    result.description = "orders 和 order_line 一致性检验";
    
    return result;
}

// 执行所有一致性检验
void run_consistency_checks(int sockfd) {
    printf("========================================\n");
    printf("开始执行数据一致性检验\n");
    printf("========================================\n");
    
    int total_checks = 0;
    int passed_checks = 0;
    
    // 对每个仓库和区域执行检验
    for (int w_id = 1; w_id <= 2; w_id++) {
        for (int d_id = 1; d_id <= 10; d_id++) {
            printf("\n--- 检验仓库 %d, 区域 %d ---\n", w_id, d_id);
            
            // 检验1
            ConsistencyResult result1 = check_district_orders_consistency(sockfd, w_id, d_id);
            total_checks++;
            if (result1.passed) passed_checks++;
            
            printf("检验1: %s - %s\n", result1.description.c_str(), 
                   result1.passed ? "通过" : "失败");
            if (!result1.passed) {
                printf("详细信息:\n%s\n", result1.details.c_str());
            }
            
            // 检验2
            ConsistencyResult result2 = check_new_orders_consistency(sockfd, w_id, d_id);
            total_checks++;
            if (result2.passed) passed_checks++;
            
            printf("检验2: %s - %s\n", result2.description.c_str(), 
                   result2.passed ? "通过" : "失败");
            if (!result2.passed) {
                printf("详细信息:\n%s\n", result2.details.c_str());
            }
            
            // 检验3
            ConsistencyResult result3 = check_orders_orderline_consistency(sockfd, w_id, d_id);
            total_checks++;
            if (result3.passed) passed_checks++;
            
            printf("检验3: %s - %s\n", result3.description.c_str(), 
                   result3.passed ? "通过" : "失败");
            if (!result3.passed) {
                printf("详细信息:\n%s\n", result3.details.c_str());
            }
        }
    }
    
    printf("\n========================================\n");
    printf("一致性检验总结\n");
    printf("总检验数: %d\n", total_checks);
    printf("通过检验数: %d\n", passed_checks);
    printf("失败检验数: %d\n", total_checks - passed_checks);
    printf("通过率: %.2f%%\n", (double)passed_checks / total_checks * 100);
    printf("========================================\n");
}

// 主函数
int main(int argc, char* argv[]) {
    // 使用默认配置，与tpcc_start保持一致
    const char* server_host = "127.0.0.1";
    int server_port = PORT_DEFAULT;
    
    // 可选参数：允许用户指定服务器地址和端口
    if (argc >= 2) {
        server_host = argv[1];
    }
    if (argc >= 3) {
        server_port = atoi(argv[2]);
    }
    
    if (argc > 3) {
        printf("用法: %s [服务器地址] [端口]\n", argv[0]);
        printf("默认: %s 127.0.0.1 %d\n", argv[0], PORT_DEFAULT);
        printf("示例: %s localhost 8765\n", argv[0]);
        return 1;
    }
    
    // 连接到数据库
    int sockfd = init_tcp_sock(server_host, server_port);
    if (sockfd < 0) {
        printf("无法连接到数据库服务器 %s:%d\n", server_host, server_port);
        return 1;
    }
    
    printf("成功连接到数据库服务器 %s:%d\n", server_host, server_port);
    
    // 执行一致性检验
    run_consistency_checks(sockfd);
    
    close(sockfd);
    return 0;
} 