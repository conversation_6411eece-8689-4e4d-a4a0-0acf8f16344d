/*
 * main.pc
 * driver for the tpcc transactions
 */

#include <atomic>
#include <regex.h>
#include <stddef.h>
#include <netdb.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <signal.h>
#include <pthread.h>
#include <fcntl.h>
#include <time.h>
#include <netdb.h>
#include <netinet/in.h>
#include <sys/un.h>
#include <iostream>
#include <string>
#include <vector>
#include <regex>
#include <fstream>
#include <sstream>
#include <algorithm>
#include "pthread.h"
#include <cstring>

// 安全的字符串到整数转换函数
int safe_stoi(const std::string& str, int default_value = 0) {
    if (str.empty()) {
        return default_value;
    }
    try {
        return std::stoi(str);
    } catch (const std::exception& e) {
        printf("Error converting string to int: '%s'\n", str.c_str());
        return default_value;
    }
}

#include "tpc.h"
#include "trans_if.h"
#include "spt_proc.h"
#include "sequence.h"
#include "rthist.h"
#include "sb_percentile.h"
#include <errno.h>
#include "start.h"
#define PRINT_SQL_TOFILE 1

/* Global SQL Variables */
// sqlite3 **ctx;
#define PORT_DEFAULT        8765

// SqlResult **stmt;
// std::atomic<SqlResult**> stmt(nullptr);

#define DB_STRING_MAX 128
#define MAX_CLUSTER_SIZE 128
std::atomic<int> atomic_array[20];

int num_ware;
int num_conn;
int lampup_time;
int measure_time;

int num_node; /* number of servers that consists of cluster i.e. RAC (0:normal mode)*/
#define NUM_NODE_MAX 8
char node_string[NUM_NODE_MAX][DB_STRING_MAX];
std::atomic<std::uint_least64_t> Instrustats[INSTRUMENT_NUM];

int time_count;
int PRINT_INTERVAL=10;
int multi_schema = 0;
int multi_schema_offset = 0; 

int success[5];
int late[5];
int retry[5];
int failure[5];

int* success2[5];
int* late2[5];
int* retry2[5];
int* failure2[5];

int success2_sum[5];
int late2_sum[5];
int retry2_sum[5];
int failure2_sum[5];

int prev_s[5];
int prev_l[5];

double max_rt[5];
double total_rt[5];
double cur_max_rt[5];

double prev_total_rt[5];

#define RTIME_NEWORD   30
#define RTIME_PAYMENT  30
#define RTIME_ORDSTAT  30
#define RTIME_DELIVERY 120
#define RTIME_SLEV     60

int rt_limit[5] = {
 RTIME_NEWORD,
 RTIME_PAYMENT,
 RTIME_ORDSTAT,
 RTIME_DELIVERY,
 RTIME_SLEV
};

sb_percentile_t local_percentile;

int activate_transaction;
double time_taken;
clock_t time_start;
clock_t time_end;
int counting_on;
int num_trans;

long clk_tck;

int is_local = 0; /* "1" mean local */
int valuable_flg = 0; /* "1" mean valuable ratio */

#define MAX_MEM_BUFFER_SIZE 16384
typedef struct
{
  int number;
} thread_arg;
void* thread_main(void* arg);

std::atomic<int> sockedToTnum[500];
void alarm_handler(int signum);
void alarm_dummy();
void parse_recv_buf(const std::string& recv_buf, SqlResult* result) {
    result->rec_msg = recv_buf;
    // 检查第一行是否包含 "abort|Error"
    if (recv_buf.find("abort") != std::string::npos) {
        result->success = 0;
        result->is_abort = 1;
        return;
    }
    if (recv_buf.find("Error") != std::string::npos) {
        result->success = 0;
        return;
    }
    regex_t regex_table_attr, regex_value, regex_count;
    regmatch_t matches[10];
    FILE *stream;
    
    // 编译正则表达式
    if (regcomp(&regex_table_attr, "\\|\\s*([^|]+?\\|)", REG_EXTENDED) != 0 ||
        regcomp(&regex_value, "\\|\\s*([^|]+?)\\s*\\|", REG_EXTENDED) != 0 ||
        regcomp(&regex_count, "Total record\\(s\\):\\s*([0-9_]+)", REG_EXTENDED) != 0) {
        fprintf(stderr, "Could not compile regex\n");
        return;
    }
    
    // 打开内存中的字符串作为文件流
    stream = fmemopen((void *)recv_buf.c_str(), recv_buf.length(), "r");
    if (!stream) {
        fprintf(stderr, "Could not open memory stream\n");
        return;
    }
    
    char *line_buffer = NULL;
    size_t len = 0;
    int header_parsed = 0;

    // 初始化结果
    result->field_names.clear();
    result->field_values.clear();
    result->field_count = 0;
    result->row_count = 0;

    while (getline(&line_buffer, &len, stream) != -1) {
        // 忽略分隔符行
        if (strstr(line_buffer, "+") != NULL) {
            continue;
        }

        if (!header_parsed) {
            // 查找表属性
            if (regexec(&regex_table_attr, line_buffer, 10, matches, 0) == 0) {
                // 分隔字段
                char *start = line_buffer + matches[1].rm_so;
                char *end = line_buffer + strlen(line_buffer) - 1;
                char *ptr = start;

                while (ptr < line_buffer + strlen(line_buffer) - 1) {
                    while (*ptr == ' ') ptr++; // 跳过空格
                    start = ptr;
                    while (*ptr != ' ' && *ptr != '|' && *ptr != '\0') ptr++; // 跳过非空格
                    end = ptr;
                    if (start < end) {
                        // 提取字段
                        size_t length = end - start;
                        if (length > 0) {
                            // char *field = strndup(start, length);
                            std::string field(start, length);
                            // if (field) {
                            //     result->field_names = (char**)realloc(result->field_names, (result->field_count + 1) * sizeof(char *));
                            //     result->field_names[result->field_count++] = field;
                            // }
                            result->field_names.push_back(field);
                            result->field_count++;
                        }
                    }
                    while (*ptr == ' ') ptr++; // 跳过空格
                    if (*ptr == '|') ptr++;
                    start = ptr;
                    
                }
                header_parsed = 1;
            }
        } else {
            // 查找值
            if (regexec(&regex_value, line_buffer, 10, matches, 0) == 0) {
                // if (result->field_values == NULL) {
                //     result->field_values = (char***)malloc(MAX_RECORDS * sizeof(char **));
                // }
                // char **record = (char**)malloc(result->field_count * sizeof(char *));
                std::vector<std::string> record;
                char *start = line_buffer + matches[1].rm_so;
                char *end = line_buffer + matches[1].rm_eo;
                char *ptr = start;

                for (int i = 0; i < result->field_count; ++i) {
                    while (*ptr == ' ') ptr++; // 跳过空格
                    start = ptr;
                    while (*ptr != ' ' && *ptr != '|' && *ptr != '\0') ptr++;
                    end = ptr;
                    if (start < end) {
                        // 提取字段值
                        size_t length = end - start;
                        if (length > 0) {
                            // char *value = strndup(start, length);
                            std::string value(start, length);
                            // if (value) {
                            //     record[i] = value;
                            // }
                            record.push_back(value);
                        }
                    }
                    while (*ptr == ' ') ptr++; // 跳过空格
                    if (*ptr == '|') ptr++;
                }
                // result->field_values[result->record_count] = record;
                // result->record_count++;
                result->field_values.push_back(record);
                result->row_count++;
            }

            // // 查找记录数
            // if (regexec(&regex_count, line_buffer, 10, matches, 0) == 0) {
            //     size_t length = matches[1].rm_eo - matches[1].rm_so;
            //     if (length > 0) {
            //         strncpy(count, line_buffer + matches[1].rm_so, length);
            //         count[length] = '\0';
            //     }
            // }
        }
    }

    result->success = 1;
    
    free(line_buffer);
    fclose(stream);

    // 释放正则表达式内存
    regfree(&regex_table_attr);
    regfree(&regex_value);
    regfree(&regex_count);
}

// 辅助函数：去除字符串首尾空格
std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

// 修正版split_line_by_pipe，忽略首尾空列，只分割中间字段
std::vector<std::string> split_line_by_pipe(const std::string& line) {
    std::vector<std::string> result;
    size_t prev = 0, next = 0;
    // 跳过第一个'|'
    if (!line.empty() && line[0] == '|') prev = 1;
    while ((next = line.find('|', prev)) != std::string::npos) {
        std::string field = line.substr(prev, next - prev);
        result.push_back(field);
        prev = next + 1;
    }
    return result;
}

// 解析db2025数据库响应的函数
void parse_db2025_response(const std::string& response, SqlResult* result) {
    result->field_names.clear();
    result->field_values.clear();
    result->field_count = 0;
    result->row_count = 0;
    
    // 如果是简单的成功消息（如INSERT、UPDATE、DELETE），不需要解析字段
    if (response.find("Total record(s):") == std::string::npos && 
        response.find("+") == std::string::npos) {
        return;
    }
    
    std::istringstream stream(response);
    std::string line;
    bool header_parsed = false;
    bool separator_found = false;
    
    while (std::getline(stream, line)) {
        // 跳过空行
        if (line.empty()) continue;
        
        // 检查是否是分隔符行（包含+的行）
        if (line.find("+") != std::string::npos) {
            separator_found = true;
            continue;
        }
        
        // 如果还没有解析过表头，且遇到了分隔符，说明接下来是表头
        if (!header_parsed && separator_found) {
            // 解析表头
            std::vector<std::string> headers = split_line_by_pipe(line);
            printf("DEBUG header cols: %zu\n", headers.size());
            for (const auto& header : headers) {
                std::string trimmed = trim(header);
                printf("DEBUG header: '%s'\n", trimmed.c_str());
                if (!trimmed.empty()) {
                    result->field_names.push_back(trimmed);
                    result->field_count++;
                }
            }
            header_parsed = true;
            continue;
        }
        
        // 如果已经解析了表头，且遇到了分隔符，说明接下来是数据行
        if (header_parsed && separator_found) {
            // 解析数据行 - 修复：检查行是否包含数据（不是空行或只包含分隔符）
            if (line.find("|") != std::string::npos && line.length() > 2) {
                std::vector<std::string> values = split_line_by_pipe(line);
                printf("DEBUG value cols: %zu\n", values.size());
                for (const auto& v : values) printf("DEBUG value: '%s'\n", trim(v).c_str());
                // 修复：检查是否有实际的数据值
                bool has_data = false;
                for (const auto& value : values) {
                    std::string trimmed = trim(value);
                    if (!trimmed.empty() && trimmed != "|") {
                        has_data = true;
                        break;
                    }
                }
                
                if (has_data && values.size() >= result->field_count) {
                    std::vector<std::string> row;
                    for (int i = 0; i < result->field_count; i++) {
                        std::string trimmed = trim(values[i]);
                        row.push_back(trimmed);
                    }
                    result->field_values.push_back(row);
                    result->row_count++;
                }
            }
        }
    }
    
    printf("DEBUG: 解析完成 - 字段数: %d, 行数: %d\n", result->field_count, result->row_count);
}

void send_command(int sockfd, const char* command, SqlResult* result) {
    // 打印SQL命令
    printf("DEBUG: 发送SQL命令: %s\n", command);
    
    #if PRINT_SQL_TOFILE
      std::fstream output;
      output.open("sql.txt", std::ios::out | std::ios::app);
      output << command << std::endl;
      output.close();
    #endif
    
    // 发送SQL命令到db2025数据库
    if (write(sockfd, command, strlen(command) + 1) == -1) {
        printf("send error: %d:%s \n", errno, strerror(errno));
        exit(1);
    }

    // 接收数据库响应
    char recv_buf[MAX_MEM_BUFFER_SIZE];
    int len = recv(sockfd, recv_buf, MAX_MEM_BUFFER_SIZE, 0);
    if (len < 0) {
        fprintf(stderr, "Connection was broken: %s\n", strerror(errno));
        exit(1);
    } else if (len == 0) {
        printf("Connection has been closed\n");
        exit(1);
    } else {
        // 处理db2025数据库的响应格式
        std::string response(recv_buf, len);
        
        // 打印响应
        printf("DEBUG: 数据库响应: %s\n", response.c_str());
        
        // 检查是否成功
        if (response.find("Error") != std::string::npos || 
            response.find("error") != std::string::npos) {
            printf("DEBUG: SQL执行失败: %s\n", response.c_str());
            result->success = 0;
            result->rec_msg = response;
            return;
        }
        
        // 检查是否中止
        if (response.find("abort") != std::string::npos) {
            result->success = 0;
            result->is_abort = 1;
            result->rec_msg = response;
            return;
        }
        
        // 成功响应
        result->success = 1;
        result->rec_msg = response;
        
        printf("DEBUG: SQL执行成功，解析结果...\n");
        
        // 解析查询结果（如果有的话）
        parse_db2025_response(response, result);
    }
}

void ResetSqlResult(SqlResult* res){
  res->field_names.clear();
  res->field_values.clear();
  res->field_count = 0;
  res->row_count = 0;
  res->success = 0;
  res->is_abort = 0;
  res->rec_msg = "";
  // res->rec_msg =  
}
int main()
{
  int i, k, t_num, arg_offset, c;
  long j;
  float f;
  pthread_t *t;
  thread_arg *thd_arg;
  timer_t timer;
  struct itimerval itval;
  struct sigaction  sigact;
  int fd, seed;

  // printf("CHECKING IF SQLITE IS THREADSAFE: RETURN VALUE = %d\n", sqlite3_threadsafe());
  
  printf("***************************************\n");
  printf("*** ###easy### TPC-C Load Generator ***\n");
  printf("***************************************\n");

  /* initialize */
  hist_init();
  activate_transaction = 1;
  counting_on = 1;

  for ( i=0; i<5; i++ ){
    success[i]=0;
    late[i]=0;
    retry[i]=0;
    failure[i]=0;

    prev_s[i]=0;
    prev_l[i]=0;
 
    prev_total_rt[i] = 0.0;
    max_rt[i]=0.0;
    total_rt[i]=0.0;
  }

  /* dummy initialize*/
  // num_ware = 50;
  // num_conn = 16;
  // lampup_time = 10;
  // measure_time = 20;
  // num_trans = 20;
  num_ware = 2;         // 仓库数量改为2
  num_conn = 2;         // 减少并发连接数，避免死锁
  lampup_time = 2;      // 可以减少预热时间（从10改为5）
  measure_time = 10;    // 可以减少测量时间（从20改为10）
  num_trans = 10;       // 可以减少事务数（从20改为10）


  /* number of node (default 0) */
  num_node = 0;
  arg_offset = 0;


  clk_tck = sysconf(_SC_CLK_TCK);

  if( num_node > 0 ){
    if( num_ware % num_node != 0 ){
      fprintf(stderr, "\n [warehouse] value must be devided by [num_node].\n");
      exit(1);
    }
    if( num_conn % num_node != 0 ){
      fprintf(stderr, "\n [connection] value must be devided by [num_node].\n");
      exit(1);
    }
  }

  printf("<Parameters>\n");
  printf("  [warehouse]: %d\n", num_ware);
  printf(" [connection]: %d\n", num_conn);
  printf("     [rampup]: %d (sec.)\n", lampup_time);
  printf("    [measure]: %d (sec.)\n", measure_time);

  // if(valuable_flg==1){
  //   printf("      [ratio]: %d:%d:%d:%d:%d\n", atoi(argv[9 + arg_offset]), atoi(argv[10 + arg_offset]),
	//    atoi(argv[11 + arg_offset]), atoi(argv[12 + arg_offset]), atoi(argv[13 + arg_offset]) );
  // }

  /* alarm initialize */
  time_count = 0;
  itval.it_interval.tv_sec = PRINT_INTERVAL;
  itval.it_interval.tv_usec = 0;
  itval.it_value.tv_sec = PRINT_INTERVAL;
  itval.it_value.tv_usec = 0;
  sigact.sa_handler = alarm_handler;
  sigact.sa_flags = 0;
  sigemptyset(&sigact.sa_mask);

  /* setup handler&timer */
  if( sigaction( SIGALRM, &sigact, NULL ) == -1 ) {
    fprintf(stderr, "error in sigaction()\n");
    exit(1);
  }

  fd = open("/dev/urandom", O_RDONLY);
  if (fd == -1) {
    fd = open("/dev/random", O_RDONLY);
    if (fd == -1) {
      struct timeval  tv;
      gettimeofday(&tv, NULL);
      seed = (tv.tv_sec ^ tv.tv_usec) * tv.tv_sec * tv.tv_usec ^ tv.tv_sec;
    }else{
      read(fd, &seed, sizeof(seed));
      close(fd);
    }
  }else{
    read(fd, &seed, sizeof(seed));
    close(fd);
  }
  SetSeed(seed);

  if(valuable_flg==0){
    seq_init(10,11,1,1,1); /* normal ratio - increased payment to 11 */
  }else{
    // seq_init( atoi(argv[9 + arg_offset]), atoi(argv[10 + arg_offset]), atoi(argv[11 + arg_offset]),
	  //     atoi(argv[12 + arg_offset]), atoi(argv[13 + arg_offset]) );
  }

  /* set up each counter */
  for ( i=0; i<5; i++ ){
      success2[i] = (int*)malloc( sizeof(int) * num_conn );
      late2[i] = (int*)malloc( sizeof(int) * num_conn );
      retry2[i] = (int*)malloc( sizeof(int) * num_conn );
      failure2[i] = (int*)malloc( sizeof(int) * num_conn );
      for ( k=0; k<num_conn; k++ ){
	  success2[i][k] = 0;
	  late2[i][k] = 0;
	  retry2[i][k] = 0;
	  failure2[i][k] = 0;
      }
  }

  if (sb_percentile_init(&local_percentile, 100000, 1.0, 1e13))
    return NULL;

  /* set up threads */

  t = (pthread_t*)malloc( sizeof(pthread_t) * num_conn );
  if ( t == NULL ){
    fprintf(stderr, "error at malloc(pthread_t)\n");
    exit(1);
  }
  thd_arg = (thread_arg*)malloc( sizeof(thread_arg) * num_conn );
  if( thd_arg == NULL ){
    fprintf(stderr, "error at malloc(thread_arg)\n");
    exit(1);
  }

  // ctx = malloc( sizeof(SqlResult *) * num_conn );
  // stmt = (SqlResult**)malloc( sizeof(SqlResult *) * num_conn );
  // for( i=0; i < num_conn; i++ ){
  //   stmt[i] = (SqlResult*)malloc( sizeof(SqlResult));
  //   stmt[i]->field_count = 0;
  //   stmt[i]->row_count = 0;
  //   stmt[i]->field_names.clear();
  //   stmt[i]->field_values.clear();
  //   stmt[i]->sockfd = -1;
  //   stmt[i]->success = 0;
  // }

  // if ( ctx == NULL ){
  //   fprintf(stderr, "error at malloc(sql_context)\n");
  //   exit(1);
  // }

  /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */

  for(t_num = 0; t_num < num_conn; t_num++) {
    thd_arg[t_num].number = t_num;
    pthread_create(&t[t_num], NULL, thread_main, (void *)&(thd_arg[t_num]));
  }


  printf("\nRAMP-UP TIME.(%d sec.)\n",lampup_time);
  fflush(stdout);
  sleep(lampup_time);
  printf("\nMEASURING START.\n\n");
  fflush(stdout);

  /* sleep(measure_time); */
  /* start timer */

#ifndef _SLEEP_ONLY_
  if( setitimer(ITIMER_REAL, &itval, NULL) == -1 ) {
    fprintf(stderr, "error in setitimer()\n");
  }
#endif

  counting_on = 1;
  /* wait signal */
  /*
  for(i = 0; i < (measure_time / PRINT_INTERVAL); i++ ) {
  //while (activate_transaction) {	  
#ifndef _SLEEP_ONLY_
    pause();
#else
    sleep(PRINT_INTERVAL);
    alarm_dummy();
#endif
  }
  */
  // counting_on = 0;


#ifndef _SLEEP_ONLY_
  /* stop timer */
  itval.it_interval.tv_sec = 0;
  itval.it_interval.tv_usec = 0;
  itval.it_value.tv_sec = 0;
  itval.it_value.tv_usec = 0;
  if( setitimer(ITIMER_REAL, &itval, NULL) == -1 ) {
    fprintf(stderr, "error in setitimer()\n");
  }
#endif

  printf("\nSTOPPING THREADS");
  activate_transaction = 0;

  /* wait threads' ending and close connections*/
  for( i=0; i < num_conn; i++ ){
    printf("[MAIN] 等待线程 %d 结束...\n", i);
    pthread_join( t[i], NULL );
    printf("[MAIN] 线程 %d 已结束\n", i);
    printf("[MAIN] thread %d joined\n", i);
    fflush(stdout);
  }
  printf("[MAIN] all threads joined, ready to print statistics\n");
  fflush(stdout);

  // // free(ctx);
  // for( i=0; i < num_conn; i++ ){
  //     free(stmt[i]);
  // }
  // free(stmt);

  free(t);
  free(thd_arg);

  //hist_report();
  printf("\n<Raw Results>\n");
  for ( i=0; i<5; i++ ){
    printf("  [%d] sc:%d lt:%d  rt:%d  fl:%d avg_rt: %.1f (%d)\n",
           i, success[i], late[i], retry[i], failure[i],
           total_rt[i] / (success[i] + late[i]), rt_limit[i]);
  }
  printf(" in %d sec.\n", (measure_time / PRINT_INTERVAL) * PRINT_INTERVAL);

  printf("\n<Raw Results2(sum ver.)>\n");
  for( i=0; i<5; i++ ){
      success2_sum[i] = 0;
      late2_sum[i] = 0;
      retry2_sum[i] = 0;
      failure2_sum[i] = 0;
      for( k=0; k<num_conn; k++ ){
	  success2_sum[i] += success2[i][k];
	  late2_sum[i] += late2[i][k];
	  retry2_sum[i] += retry2[i][k];
	  failure2_sum[i] += failure2[i][k];
      }
  }
  for ( i=0; i<5; i++ ){
      printf("  [%d] sc:%d  lt:%d  rt:%d  fl:%d \n", i, success2_sum[i], late2_sum[i], retry2_sum[i], failure2_sum[i]);
  }

  printf("\n<Constraint Check> (all must be [OK])\n [transaction percentage]\n");
  for ( i=0, j=0; i<5; i++ ){
    j += (success[i] + late[i]);
  }

  f = 100.0 * (float)(success[1] + late[1])/(float)j;
  printf("        Payment: %3.2f%% (>=43.0%%)",f);
  if ( f >= 43.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)(success[2] + late[2])/(float)j;
  printf("   Order-Status: %3.2f%% (>= 4.0%%)",f);
  if ( f >= 4.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)(success[3] + late[3])/(float)j;
  printf("       Delivery: %3.2f%% (>= 4.0%%)",f);
  if ( f >= 4.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)(success[4] + late[4])/(float)j;
  printf("    Stock-Level: %3.2f%% (>= 4.0%%)",f);
  if ( f >= 4.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }

  printf(" [response time (at least 90%% passed)]\n");
  f = 100.0 * (float)success[0]/(float)(success[0] + late[0]);
  printf("      New-Order: %3.2f%% ",f);
  if ( f >= 90.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)success[1]/(float)(success[1] + late[1]);
  printf("        Payment: %3.2f%% ",f);
  if ( f >= 90.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)success[2]/(float)(success[2] + late[2]);
  printf("   Order-Status: %3.2f%% ",f);
  if ( f >= 90.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)success[3]/(float)(success[3] + late[3]);
  printf("       Delivery: %3.2f%% ",f);
  if ( f >= 90.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }
  f = 100.0 * (float)success[4]/(float)(success[4] + late[4]);
  printf("    Stock-Level: %3.2f%% ",f);
  if ( f >= 90.0 ){
    printf(" [OK]\n");
  }else{
    printf(" [NG] *\n");
  }

  printf("\n<TpmC>\n");
  f = (float)(success[0] + late[0]) * 60.0
    / (float)((measure_time / PRINT_INTERVAL) * PRINT_INTERVAL);
  printf("                 %.3f TpmC\n",f);

  printf("\nTime taken\n");
  time_taken = ((double) (time_end - time_start)) / CLOCKS_PER_SEC;
  printf("                 %.3f seconds\n", time_taken);

  exit(0);

 sqlerr:
  fprintf(stdout, "error at main\n");
  // error(ctx[i],0);
  exit(1);

}


void alarm_handler(int signum)
{
  int i;
  int s[5],l[5];
  double rt90[5];
  double trt[5];
  double percentile_val;
  double percentile_val99;

  for( i=0; i<5; i++ ){
    s[i] = success[i];
    l[i] = late[i];
    trt[i] = total_rt[i];
    //rt90[i] = hist_ckp(i);
  }

  time_count += PRINT_INTERVAL;
  percentile_val = sb_percentile_calculate(&local_percentile, 95);
  percentile_val99 = sb_percentile_calculate(&local_percentile, 99);
  sb_percentile_reset(&local_percentile);
//  printf("%4d, %d:%.3f|%.3f(%.3f), %d:%.3f|%.3f(%.3f), %d:%.3f|%.3f(%.3f), %d:%.3f|%.3f(%.3f), %d:%.3f|%.3f(%.3f)\n",
  printf("%4d, trx: %d, 95%: %.3f, 99%: %.3f, max_rt: %.3f, %d|%.3f, %d|%.3f, %d|%.3f, %d|%.3f\n",
	 time_count,
	 ( s[0] + l[0] - prev_s[0] - prev_l[0] ), percentile_val,percentile_val99,
	 (double)cur_max_rt[0],
	 ( s[1] + l[1] - prev_s[1] - prev_l[1] ),
	 (double)cur_max_rt[1],
	 ( s[2] + l[2] - prev_s[2] - prev_l[2] ),
	 (double)cur_max_rt[2],
	 ( s[3] + l[3] - prev_s[3] - prev_l[3] ),
	 (double)cur_max_rt[3],
	 ( s[4] + l[4] - prev_s[4] - prev_l[4] ),
	 (double)cur_max_rt[4]
	 );
  fflush(stdout);

  for( i=0; i<5; i++ ){
    prev_s[i] = s[i];
    prev_l[i] = l[i];
    prev_total_rt[i] = trt[i];
    cur_max_rt[i]=0.0;
  }
}

void alarm_dummy()
{
  int i;
  int s[5],l[5];
  float rt90[5];

  for( i=0; i<5; i++ ){
    s[i] = success[i];
    l[i] = late[i];
    rt90[i] = hist_ckp(i);
  }

  time_count += PRINT_INTERVAL;
  printf("%4d, %d(%d):%.2f, %d(%d):%.2f, %d(%d):%.2f, %d(%d):%.2f, %d(%d):%.2f\n",
	 time_count,
	 ( s[0] + l[0] - prev_s[0] - prev_l[0] ),
	 ( l[0] - prev_l[0] ),
	 rt90[0],
	 ( s[1] + l[1] - prev_s[1] - prev_l[1] ),
	 ( l[1] - prev_l[1] ),
	 rt90[1],
	 ( s[2] + l[2] - prev_s[2] - prev_l[2] ),
	 ( l[2] - prev_l[2] ),
	 rt90[2],
	 ( s[3] + l[3] - prev_s[3] - prev_l[3] ),
	 ( l[3] - prev_l[3] ),
	 rt90[3],
	 ( s[4] + l[4] - prev_s[4] - prev_l[4] ),
	 ( l[4] - prev_l[4] ),
	 rt90[4]
	 );
  fflush(stdout);

  for( i=0; i<5; i++ ){
    prev_s[i] = s[i];
    prev_l[i] = l[i];
  }
}

int init_tcp_sock(const char *server_host, int server_port) {
    struct hostent *host;
    struct sockaddr_in serv_addr;

    if ((host = gethostbyname(server_host)) == NULL) {
        fprintf(stderr, "gethostbyname failed. errmsg=%d:%s\n", errno, strerror(errno));
        return -1;
    }

    int sockfd;
    if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
        fprintf(stderr, "create socket error. errmsg=%d:%s\n", errno, strerror(errno));
        return -1;
    }

    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(server_port);
    serv_addr.sin_addr = *((struct in_addr *)host->h_addr);
    bzero(&(serv_addr.sin_zero), 8);

    if (connect(sockfd, (struct sockaddr *)&serv_addr, sizeof(struct sockaddr)) == -1) {
        fprintf(stderr, "Failed to connect. errmsg=%d:%s\n", errno, strerror(errno));
        close(sockfd);
        return -1;
    }
    return sockfd;
}

int init_unix_sock(const char *unix_sock_path) {
    int sockfd = socket(PF_UNIX, SOCK_STREAM, 0);
    if (sockfd < 0) {
        fprintf(stderr, "failed to create unix socket. %s", strerror(errno));
        return -1;
    }

    struct sockaddr_un sockaddr;
    memset(&sockaddr, 0, sizeof(sockaddr));
    sockaddr.sun_family = PF_UNIX;
    snprintf(sockaddr.sun_path, sizeof(sockaddr.sun_path), "%s", unix_sock_path);

    if (connect(sockfd, (struct sockaddr *)&sockaddr, sizeof(sockaddr)) < 0) {
        fprintf(stderr, "failed to connect to server. unix socket path '%s'. error %s", sockaddr.sun_path,
                strerror(errno));
        close(sockfd);
        return -1;
    }
    return sockfd;
}
void* thread_main(void* arg)
{
    thread_arg* threadArg = (thread_arg*)arg;  // 将参数转换为正确的类型
    int t_num = threadArg->number;
    printf("[THREAD %d] thread_main started\n", t_num);
    fflush(stdout);
    int r, i;
    
    // 添加线程退出日志
    auto thread_exit = [t_num](const char* reason) {
        printf("[THREAD %d] 线程退出: %s\n", t_num, reason);
        fflush(stdout);
        return (void*)1;
    };
    const char *unix_socket_path = NULL;
    const char *server_host = "127.0.0.1";
    int server_port = PORT_DEFAULT;
    int opt;
    int sockfd;
    if (unix_socket_path != NULL) {
        sockfd = init_unix_sock(unix_socket_path);
    } else {
        sockfd = init_tcp_sock(server_host, server_port);
    }
    if (sockfd < 0) {
        return thread_exit("数据库连接失败");
    }
    // stmt[t_num]->sockfd = sockfd;
    INITIALIZE_TIMERS();

    time_start = clock();
    atomic_array[t_num] = sockfd;
  SqlResult res;
    for (i = 0; i < num_trans; i++) {
        printf("[THREAD %d] 开始事务 %d/%d\n", t_num, i+1, num_trans);
        fflush(stdout);
        
        send_command(sockfd, "begin;", &res);
        printf("[THREAD %d] begin transaction %d\n", t_num, i);
        fflush(stdout);
        
        r = driver(t_num);
        printf("[THREAD %d] driver返回: %d\n", t_num, r);
        fflush(stdout);
        
        printf("[THREAD %d] end transaction %d\n", t_num, i);
        send_command(sockfd, "commit;", &res);
        printf("[THREAD %d] commit transaction %d\n", t_num, i);
        fflush(stdout);
    }

    close(sockfd);
    printf("[THREAD %d] finished all transactions and exiting\n", t_num);
    PRINT_TIME();
    time_end = clock();
    printf("[THREAD %d] thread_main exiting\n", t_num);
    fflush(stdout);

    return NULL;  // 正常结束时返回NULL

sqlerr:
    fprintf(stdout, "error at thread_main\n");
    return (void*)0;  // 出错时返回0
}

