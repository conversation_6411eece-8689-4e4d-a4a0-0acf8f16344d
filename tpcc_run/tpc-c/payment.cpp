/*
 * -*-C-*-  
 * payment.pc 
 * corresponds to A.2 in appendix A
 */

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <thread>
#include <time.h>
#include "iostream"

// #include <sqlite3.h>

#include "spt_proc.h"
#include "tpc.h"
#include "sstream"
#include "fstream"

inline int safe_stoi(const std::string& str, int default_value = 0) {
    if (str.empty()) return default_value;
    try { return std::stoi(str); }
    catch (...) { return default_value; }
}
inline float safe_stof(const std::string& str, float default_value = 0.0f) {
    if (str.empty()) return default_value;
    try { return std::stof(str); }
    catch (...) { return default_value; }
}
// extern sqlite3 **ctx;
// extern sqlite3_stmt ***stmt;
extern SqlResult **stmt;
extern void send_command(int sockfd, const char* command, SqlResult* result);
extern std::atomic<int> atomic_array[20];


#define NNULL ((void *)0)

/*
 * the payment transaction
 */
int payment( int t_num,
	     int w_id_arg,		/* warehouse id */
	     int d_id_arg,		/* district id */
	     int byname,		/* select by c_id or c_last? */
	     int c_w_id_arg,
	     int c_d_id_arg,
	     int c_id_arg,		/* customer id */
	     char c_last_arg[],	        /* customer last name */
	     float h_amount_arg	        /* payment amount */
)
{
	int ret;
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	char            w_name[11];
	char            w_street_1[21];
	char            w_street_2[21];
	char            w_city[21];
	char            w_state[3];
	char            w_zip[10];
	int            c_d_id = c_d_id_arg;
	int            c_w_id = c_w_id_arg;
	char            c_first[17];
	char            c_middle[3];
	char            c_last[17];
	char            c_street_1[21];
	char            c_street_2[21];
	char            c_city[21];
	char            c_state[3];
	char            c_zip[10];
	char            c_phone[17];
	char            c_since[20];
	char            c_credit[4];
	int            c_credit_lim;
	float           c_discount;
	float           c_balance = -10.0;
	char            c_data[51];
	char            c_new_data[51];
	float           h_amount = h_amount_arg;
	char            h_data[26];
	char            d_name[11];
	char            d_street_1[21];
	char            d_street_2[21];
	char            d_city[21];
	char            d_state[3];
	char            d_zip[10];
	int            namecnt = 0;
	char            datetime[20];

	int             n;
	int             proceed = 0;
	int bytes;
	// 获取当前线程id
	std::thread::id threadId = std::this_thread::get_id();
	std::stringstream ss;
	ss << threadId;
	std::string threadIdStr = ss.str();

	std::string file_path = "payment_" + threadIdStr + ".txt";
	std::fstream outfile;
	SqlResult *sqlite_stmt = new SqlResult();
	int sockfd = atomic_array[t_num];

	char send_str[500];
	int num_cols;
	
	/* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr; */
	/* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */

	gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);
	datetime[19] = '\0';
	proceed = 1;
	/*EXEC_SQL UPDATE warehouse SET w_ytd = w_ytd + :h_amount
	  WHERE w_id =:w_id;*/

	// sqlite_stmt = stmt[t_num][9];
	// sqlite_stmt = stmt[t_num];
	ResetSqlResult(sqlite_stmt);
	// sqlite3_bind_double(sqlite_stmt, 1, h_amount);
	// sqlite3_bind_int64(sqlite_stmt, 2, w_id);

	// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "UPDATE warehouse SET w_ytd = w_ytd + %f WHERE w_id = %d;", h_amount, w_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

	if(!sqlite_stmt->success) goto sqlerr;

	ResetSqlResult(sqlite_stmt);
	
	proceed = 2;
	/*EXEC_SQL SELECT w_street_1, w_street_2, w_city, w_state, w_zip,
	                w_name
	                INTO :w_street_1, :w_street_2, :w_city, :w_state,
				:w_zip, :w_name
	                FROM warehouse
	                WHERE w_id = :w_id;*/

	// sqlite_stmt = stmt[t_num][10];

	// sqlite3_bind_int64(sqlite_stmt, 1, w_id);

	memset(send_str, 0, 500);
	sprintf(send_str, "SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = %d;", w_id);

	// ret = sqlite3_step(sqlite_stmt);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 6) goto sqlerr;

	// 	strcpy(w_street_1, sqlite3_column_text(sqlite_stmt, 0));
	// 	strcpy(w_street_2, sqlite3_column_text(sqlite_stmt, 1));
	// 	strcpy(w_city, sqlite3_column_text(sqlite_stmt, 2));
	// 	strcpy(w_state, sqlite3_column_text(sqlite_stmt, 3));
	// 	strcpy(w_zip, sqlite3_column_text(sqlite_stmt, 4));
	// 	strcpy(w_name, sqlite3_column_text(sqlite_stmt, 5));
	// }

	if(sqlite_stmt->success){
		if(sqlite_stmt->row_count < 1) goto sqlerr;
		num_cols = sqlite_stmt->field_count;
		if(num_cols < 6) goto sqlerr;

		strcpy(w_street_1, sqlite_stmt->field_values[0][0].c_str());
		strcpy(w_street_2, sqlite_stmt->field_values[0][1].c_str());
		strcpy(w_city, sqlite_stmt->field_values[0][2].c_str());
		strcpy(w_state, sqlite_stmt->field_values[0][3].c_str());
		strcpy(w_zip, sqlite_stmt->field_values[0][4].c_str());
		strcpy(w_name, sqlite_stmt->field_values[0][5].c_str());

	}
	
	// sqlite3_reset(sqlite_stmt);

	ResetSqlResult(sqlite_stmt);

	proceed = 3;
	/*EXEC_SQL UPDATE district SET d_ytd = d_ytd + :h_amount
			WHERE d_w_id = :w_id 
			AND d_id = :d_id;*/

	// sqlite_stmt = stmt[t_num][11];

	// sqlite3_bind_double(sqlite_stmt, 1, h_amount);
	// sqlite3_bind_int64(sqlite_stmt, 2, w_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, d_id);

	// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "UPDATE district SET d_ytd = d_ytd + %f WHERE d_w_id = %d AND d_id = %d;", h_amount, w_id, d_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

	if(!sqlite_stmt->success) goto sqlerr;

	ResetSqlResult(sqlite_stmt);


	proceed = 4;
	/*EXEC_SQL SELECT d_street_1, d_street_2, d_city, d_state, d_zip,
	                d_name
	                INTO :d_street_1, :d_street_2, :d_city, :d_state,
				:d_zip, :d_name
	                FROM district
	                WHERE d_w_id = :w_id 
			AND d_id = :d_id;*/


	// sqlite_stmt = stmt[t_num][12];

	// sqlite3_bind_int64(sqlite_stmt, 1, w_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, d_id);

	// ret = sqlite3_step(sqlite_stmt);
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 6) goto sqlerr;

	// 	strcpy(d_street_1, sqlite3_column_text(sqlite_stmt, 0));
	// 	strcpy(d_street_2, sqlite3_column_text(sqlite_stmt, 1));
	// 	strcpy(d_city, sqlite3_column_text(sqlite_stmt, 2));
	// 	strcpy(d_state, sqlite3_column_text(sqlite_stmt, 3));
	// 	strcpy(d_zip, sqlite3_column_text(sqlite_stmt, 4));
	// 	strcpy(d_name, sqlite3_column_text(sqlite_stmt, 5));
	// }

	// sqlite3_reset(sqlite_stmt);


	memset(send_str, 0, 500);
	sprintf(send_str, "SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = %d AND d_id = %d;", w_id, d_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

	if(sqlite_stmt->success){
		if(sqlite_stmt->row_count < 1) goto sqlerr;
		num_cols = sqlite_stmt->field_count;
		if(num_cols < 6) goto sqlerr;

		strcpy(d_street_1, sqlite_stmt->field_values[0][0].c_str());
		strcpy(d_street_2, sqlite_stmt->field_values[0][1].c_str());
		strcpy(d_city, sqlite_stmt->field_values[0][2].c_str());
		strcpy(d_state, sqlite_stmt->field_values[0][3].c_str());
		strcpy(d_zip, sqlite_stmt->field_values[0][4].c_str());
		strcpy(d_name, sqlite_stmt->field_values[0][5].c_str());
	}

	ResetSqlResult(sqlite_stmt);

	if (byname) {
		strcpy(c_last, c_last_arg);

		proceed = 5;
		/*EXEC_SQL SELECT count(c_id) 
			INTO :namecnt
		        FROM customer
			WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
		        AND c_last = :c_last;*/


		// sqlite_stmt = stmt[t_num][13];

		// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		// sqlite3_bind_text(sqlite_stmt, 3, c_last, -1, SQLITE_STATIC);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 1) goto sqlerr;

		// 	namecnt = sqlite3_column_int64(sqlite_stmt, 0);
		// }
		
		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s';", c_w_id, c_d_id, c_last);

		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif



		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			if(num_cols < 1) goto sqlerr;
			// Get the count value from the result
			if(sqlite_stmt->field_values[0][0].empty()) {
				namecnt = 0;
			} else {
				try {
					namecnt = std::stoi(sqlite_stmt->field_values[0][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting string to int: %s\n", sqlite_stmt->field_values[0][0].c_str());
					goto sqlerr;
				}
			}
		}

		ResetSqlResult(sqlite_stmt);

		/*EXEC_SQL DECLARE c_byname_p CURSOR FOR
		        SELECT c_id
		        FROM customer
		        WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_last = :c_last
			ORDER BY c_first;

			EXEC_SQL OPEN c_byname_p;*/

		// sqlite_stmt = stmt[t_num][14];

		// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		// sqlite3_bind_text(sqlite_stmt, 3, c_last, -1, SQLITE_STATIC);

		memset(send_str, 0, 500);
		sprintf(send_str, "SELECT c_id FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s' ORDER BY c_first;", c_w_id, c_d_id, c_last);

		if (namecnt % 2)
			namecnt++;
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		for (n = 0; n < namecnt / 2 && n < sqlite_stmt->row_count; n++) {
			// ret = sqlite3_step(sqlite_stmt);
			// if (ret != SQLITE_DONE) {
			// 	if (ret != SQLITE_ROW) goto sqlerr;
			// 	num_cols = sqlite3_column_count(sqlite_stmt);
			// 	if (num_cols != 1) goto sqlerr;
		
			// 	c_id = sqlite3_column_int64(sqlite_stmt, 0);
			// }

			if(sqlite_stmt->success){
				if(sqlite_stmt->row_count < 1) goto sqlerr;
				num_cols = sqlite_stmt->field_count;
				if(num_cols < 1) goto sqlerr;

				// 添加错误处理，防止空字符串导致的stoi异常
				if(sqlite_stmt->field_values[n][0].empty()) {
					c_id = 0;
				} else {
					try {
						c_id = std::stoi(sqlite_stmt->field_values[n][0].c_str());
					} catch (const std::exception& e) {
						printf("Error converting string to int: %s\n", sqlite_stmt->field_values[n][0].c_str());
						goto sqlerr;
					}
				}
			}
		}

		// sqlite3_reset(sqlite_stmt);

		ResetSqlResult(sqlite_stmt);

	}

	proceed = 6;
	/*EXEC_SQL SELECT c_first, c_middle, c_last, c_street_1,
		        c_street_2, c_city, c_state, c_zip, c_phone,
		        c_credit, c_credit_lim, c_discount, c_balance,
		        c_since
		INTO :c_first, :c_middle, :c_last, :c_street_1,
		     :c_street_2, :c_city, :c_state, :c_zip, :c_phone,
		     :c_credit, :c_credit_lim, :c_discount, :c_balance,
		     :c_since
		FROM customer
	        WHERE c_w_id = :c_w_id 
	        AND c_d_id = :c_d_id 
		AND c_id = :c_id
		FOR UPDATE;*/

	// sqlite_stmt = stmt[t_num][15];

	// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, c_id);

	// ret = sqlite3_step(sqlite_stmt);
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 14) goto sqlerr;

	// 	strcpy(c_first, sqlite3_column_text(sqlite_stmt, 0));
	// 	strcpy(c_middle, sqlite3_column_text(sqlite_stmt, 1));
	// 	strcpy(c_last, sqlite3_column_text(sqlite_stmt, 2));
	// 	strcpy(c_street_1, sqlite3_column_text(sqlite_stmt, 3));
	// 	strcpy(c_street_2, sqlite3_column_text(sqlite_stmt, 4));
	// 	strcpy(c_city, sqlite3_column_text(sqlite_stmt, 5));
	// 	strcpy(c_state, sqlite3_column_text(sqlite_stmt, 6));
	// 	strcpy(c_zip, sqlite3_column_text(sqlite_stmt, 7));
	// 	strcpy(c_phone, sqlite3_column_text(sqlite_stmt, 8));
	// 	strcpy(c_credit, sqlite3_column_text(sqlite_stmt, 9));
	// 	c_credit_lim = sqlite3_column_int64(sqlite_stmt, 10);
	// 	c_discount = sqlite3_column_double(sqlite_stmt, 11);
	// 	c_balance = sqlite3_column_double(sqlite_stmt, 12);
	// 	strcpy(c_since, sqlite3_column_text(sqlite_stmt, 13));
	// }

	// sqlite3_reset(sqlite_stmt);

	memset(send_str, 0, 500);
	sprintf(send_str, "SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);

	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

	if(sqlite_stmt->success){
		if(sqlite_stmt->row_count < 1) goto sqlerr;
		num_cols = sqlite_stmt->field_count;
		if(num_cols < 14) goto sqlerr;

		strcpy(c_first, sqlite_stmt->field_values[0][0].c_str());
		strcpy(c_middle, sqlite_stmt->field_values[0][1].c_str());
		strcpy(c_last, sqlite_stmt->field_values[0][2].c_str());
		strcpy(c_street_1, sqlite_stmt->field_values[0][3].c_str());
		strcpy(c_street_2, sqlite_stmt->field_values[0][4].c_str());
		strcpy(c_city, sqlite_stmt->field_values[0][5].c_str());
		strcpy(c_state, sqlite_stmt->field_values[0][6].c_str());
		strcpy(c_zip, sqlite_stmt->field_values[0][7].c_str());
		strcpy(c_phone, sqlite_stmt->field_values[0][8].c_str());
		strcpy(c_credit, sqlite_stmt->field_values[0][9].c_str());
		c_credit_lim = safe_stoi(sqlite_stmt->field_values[0][10].c_str());
		c_discount = safe_stof(sqlite_stmt->field_values[0][11].c_str());
		c_balance = safe_stof(sqlite_stmt->field_values[0][12].c_str());
		strcpy(c_since, sqlite_stmt->field_values[0][13].c_str());
	}

	ResetSqlResult(sqlite_stmt);

	c_balance = c_balance - h_amount;
	c_credit[2] = '\0';
	if (strstr(c_credit, "BC")) {
		proceed = 7;
		/*EXEC_SQL SELECT c_data 
			INTO :c_data
		        FROM customer
		        WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id; */

		// sqlite_stmt = stmt[t_num][16];

		// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, c_id);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 1) goto sqlerr;

		// 	strcpy(c_data, sqlite3_column_text(sqlite_stmt, 0));
		// }

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "SELECT c_data FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);

		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			if(num_cols < 1) goto sqlerr;

			strcpy(c_data, sqlite_stmt->field_values[0][0].c_str());
		}

		ResetSqlResult(sqlite_stmt);

		// sprintf(c_new_data, 
		// 	"| %4d %2d %4d %2d %4d $%7.2f %12c %24c",
		// 	c_id, c_d_id, c_w_id, d_id,
		// 	w_id, h_amount,
		// 	datetime, c_data);

		strncat(c_new_data, c_data, 
			50 - strlen(c_new_data));

		memcpy(c_new_data, c_data, sizeof(c_new_data)-1);
		c_new_data[sizeof(c_new_data)-1] = '\0';

		proceed = 8;
		/*EXEC_SQL UPDATE customer
			SET c_balance = :c_balance, c_data = :c_new_data
			WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id;*/

		// sqlite_stmt = stmt[t_num][17];

		// sqlite3_bind_double(sqlite_stmt, 1, c_balance);
		// sqlite3_bind_text(sqlite_stmt, 2, c_data, -1, SQLITE_STATIC);
		// sqlite3_bind_int64(sqlite_stmt, 3, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 4, c_d_id);
		// sqlite3_bind_int64(sqlite_stmt, 5, c_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);


		memset(send_str, 0, 500);
		sprintf(send_str, "UPDATE customer SET c_balance = %f, c_data = '%s' WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_balance, c_data, c_w_id, c_d_id, c_id);
		
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

		if(!sqlite_stmt->success) goto sqlerr;

		ResetSqlResult(sqlite_stmt);

	} else {
		proceed = 9;
		/*EXEC_SQL UPDATE customer 
			SET c_balance = :c_balance
			WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id;*/

		// sqlite_stmt = stmt[t_num][18];

		// sqlite3_bind_double(sqlite_stmt, 1, c_balance);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 3, c_d_id);
		// sqlite3_bind_int64(sqlite_stmt, 4, c_id);

		// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "UPDATE customer SET c_balance = %f WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_balance, c_w_id, c_d_id, c_id);

		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(!sqlite_stmt->success) goto sqlerr;

		ResetSqlResult(sqlite_stmt);

	}

	strncpy(h_data, w_name, 10);
	h_data[10] = '\0';
	strncat(h_data, d_name, 10);
	h_data[20] = ' ';
	h_data[21] = ' ';
	h_data[22] = ' ';
	h_data[23] = ' ';
	h_data[24] = '\0';

	proceed = 10;
	/*EXEC_SQL INSERT INTO history(h_c_d_id, h_c_w_id, h_c_id, h_d_id,
			                   h_w_id, h_date, h_amount, h_data)
	                VALUES(:c_d_id, :c_w_id, :c_id, :d_id,
		               :w_id, 
			       :datetime,
			       :h_amount, :h_data);*/

	// sqlite_stmt = stmt[t_num][19];

	// sqlite3_bind_int64(sqlite_stmt, 1, c_d_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, c_w_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, c_id);
	// sqlite3_bind_int64(sqlite_stmt, 4, d_id);
	// sqlite3_bind_int64(sqlite_stmt, 5, w_id);
	// sqlite3_bind_text(sqlite_stmt, 6, datetime, -1, SQLITE_STATIC);
	// sqlite3_bind_double(sqlite_stmt, 7, h_amount);
	// sqlite3_bind_text(sqlite_stmt, 8, h_data, -1, SQLITE_STATIC);

	// if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;

	// sqlite3_reset(sqlite_stmt);
	memset(send_str, 0, 500);
	sprintf(send_str, "INSERT INTO history VALUES(%d, %d, %d, %d, %d, '%s', %f, '%s');", c_id, c_d_id, c_w_id, d_id, w_id, datetime, h_amount, h_data);

	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;

	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif

	if(!sqlite_stmt->success) goto sqlerr;

	ResetSqlResult(sqlite_stmt);


	/*EXEC_SQL COMMIT WORK;*/
	//if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	delete sqlite_stmt;  // 删除分配的内存
	return (1);

sqlerr:
	fprintf(stderr, "payment %d:%d\n",t_num,proceed);
	// printf("%s: error: %s\n", __func__, sqlite3_errmsg(ctx[t_num]));
	printf("failed in payment\n");
	if(!sqlite_stmt->is_abort){
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << "send_str " << send_str<<" exec failed" << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
	}
	ResetSqlResult(sqlite_stmt);
	send_command(sockfd, "abort;", sqlite_stmt);
	send_command(sockfd, "begin;", sqlite_stmt);
	#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << "begin;" << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
	ResetSqlResult(sqlite_stmt);
	
sqlerrerr:
	delete sqlite_stmt;  // 删除分配的内存
	return (0);
}


