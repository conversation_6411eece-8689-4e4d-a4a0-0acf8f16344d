cmake_minimum_required(VERSION 3.10)
project(TPCC CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS TRUE)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_FLAGS "-Wall -O0 -g -ggdb3 -DOUTPUT_TO_FILE")
# set(CMAKE_CXX_FLAGS "-Wall -O3")

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -O0 -g")

# 包含目录
include_directories(.)

# 链接库
set(LIBS rt pthread m readline)

# 源文件列表
set(TRANSACTIONS neword.cpp payment.cpp ordstat.cpp delivery.cpp slev.cpp)
set(SOURCES start.cpp spt_proc.cpp driver.cpp support.cpp sequence.cpp rthist.cpp sb_percentile.cpp ${TRANSACTIONS})

# 创建可执行文件
add_executable(tpcc_load  load.cpp support.cpp)
add_executable(tpcc_start  ${SOURCES})
add_executable(test_db2025_compatibility test_db2025_compatibility.cpp)
add_executable(simple_consistency_check simple_consistency_check.cpp support.cpp) 