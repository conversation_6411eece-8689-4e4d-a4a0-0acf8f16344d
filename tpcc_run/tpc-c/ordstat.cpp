/*
 * -*-C-*- 
 * ordstat.pc 
 * corresponds to A.3 in appendix A
 */

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <thread>


#include "spt_proc.h"
#include "tpc.h"
#include "sstream"
#include "fstream"
// extern sqlite3 **ctx;
// extern sqlite3_stmt ***stmt;
extern SqlResult **stmt;
extern void send_command(int sockfd, const char* command, SqlResult* result);
extern std::atomic<int> atomic_array[20];

inline int safe_stoi(const std::string& str, int default_value = 0) {
    if (str.empty()) return default_value;
    try { return std::stoi(str); }
    catch (...) { return default_value; }
}
inline float safe_stof(const std::string& str, float default_value = 0.0f) {
    if (str.empty()) return default_value;
    try { return std::stof(str); }
    catch (...) { return default_value; }
}

/*
 * the order status transaction
 */
int ordstat( int t_num,
	     int w_id_arg,		/* warehouse id */
	     int d_id_arg,		/* district id */
	     int byname,		/* select by c_id or c_last? */
	     int c_id_arg,		/* customer id */
	     char c_last_arg[]	        /* customer last name, format? */
)
{
	int ret;
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	int            c_d_id = d_id;
	int            c_w_id = w_id;
	char            c_first[17];
	char            c_middle[3];
	char            c_last[17];
	float           c_balance;
	int            o_id = 0;
	char            o_entry_d[25];
	int            o_carrier_id;
	int            ol_i_id;
	int            ol_supply_w_id;
	int            ol_quantity;
	float           ol_amount;
	char            ol_delivery_d[25];
	int            namecnt = 0;
	int            max_o_id = 0;

	int             n;
	int             proceed = 0;
	int i = 0;
	// 获取当前线程id
	std::thread::id threadId = std::this_thread::get_id();
	std::stringstream ss;
	ss << threadId;
	std::string threadIdStr = ss.str();

	std::string file_path = "ordstat_" + threadIdStr + ".txt";
	std::fstream outfile;
	SqlResult *sqlite_stmt = new SqlResult();
	int sockfd = atomic_array[t_num];
	int num_cols;
	int bytes;
	
	/*EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
	char send_str[500];
	ResetSqlResult(sqlite_stmt);
	if (byname) {
		strcpy(c_last, c_last_arg);
		proceed = 1;
		/*EXEC_SQL SELECT count(c_id)
			INTO :namecnt
		        FROM customer
			WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
		        AND c_last = :c_last;*/

		// sqlite_stmt = stmt[t_num][20];

		// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		// sqlite3_bind_text(sqlite_stmt, 3, c_last, -1, SQLITE_STATIC);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 1) goto sqlerr;

		// 	namecnt = sqlite3_column_int64(sqlite_stmt, 0);
		// }

		// sqlite3_reset(sqlite_stmt);
		
		sprintf(send_str, "SELECT count(c_id) as count_c_id FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s';", c_w_id, c_d_id, c_last);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;


		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			// if(num_cols != 1) goto sqlerr;
			// Get the count value from the result
			if(sqlite_stmt->field_values[0][0].empty()) {
				namecnt = 0;
			} else {
				try {
					namecnt = std::stoi(sqlite_stmt->field_values[0][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting namecnt: %s\n", sqlite_stmt->field_values[0][0].c_str());
					goto sqlerr;
				}
			}
		}

		ResetSqlResult(sqlite_stmt);

		proceed = 2;
		/*EXEC_SQL DECLARE c_byname_o CURSOR FOR
		        SELECT c_balance, c_first, c_middle, c_last
		        FROM customer
		        WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
			AND c_last = :c_last
			ORDER BY c_first;
		proceed = 3;
		EXEC_SQL OPEN c_byname_o;*/

		// sqlite_stmt = stmt[t_num][21];

		// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		// sqlite3_bind_text(sqlite_stmt, 3, c_last, -1, SQLITE_STATIC);

		memset(send_str, 0, 500);
		sprintf(send_str, "select c_balance, c_first, c_middle, c_last from customer where c_w_id = %d and c_d_id = %d and c_last = '%s' order by c_first;", c_w_id, c_d_id, c_last);


		if (namecnt % 2)
			namecnt++;	/* Locate midpoint customer; */
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		
		for (n = 0; n < namecnt / 2 && n < sqlite_stmt->row_count; n++) {
			// ret = sqlite3_step(sqlite_stmt);
			// if (ret != SQLITE_DONE) {
			// 	if (ret != SQLITE_ROW) goto sqlerr;
			// 	num_cols = sqlite3_column_count(sqlite_stmt);
			// 	if (num_cols != 4) goto sqlerr;
			// 	c_balance = sqlite3_column_double(sqlite_stmt, 0);
			// 	strcpy(c_first, sqlite3_column_text(sqlite_stmt, 1));
			// 	strcpy(c_middle, sqlite3_column_text(sqlite_stmt, 2));
			// 	strcpy(c_last, sqlite3_column_text(sqlite_stmt, 3));
			// }
			if(sqlite_stmt->success){
				if(sqlite_stmt->row_count < 1) goto sqlerr;
				num_cols = sqlite_stmt->field_count;
				// if(num_cols != 4) goto sqlerr;
				c_balance = safe_stof(sqlite_stmt->field_values[n][0].c_str());
				strcpy(c_first, sqlite_stmt->field_values[n][1].c_str());
				strcpy(c_middle, sqlite_stmt->field_values[n][2].c_str());
				strcpy(c_last, sqlite_stmt->field_values[n][3].c_str());
			}
			
		}

		// sqlite3_reset(sqlite_stmt);
		ResetSqlResult(sqlite_stmt);

		proceed = 5;
		/*EXEC_SQL CLOSE  c_byname_o;*/

	} else {		/* by number */
		proceed = 6;
		/*EXEC_SQL SELECT c_balance, c_first, c_middle, c_last
			INTO :c_balance, :c_first, :c_middle, :c_last
		        FROM customer
		        WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
			AND c_id = :c_id;*/

		// sqlite_stmt = stmt[t_num][22];

		// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
		// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		// sqlite3_bind_text(sqlite_stmt, 3, c_last, -1, SQLITE_STATIC);

		// ret = sqlite3_step(sqlite_stmt);
		// if (ret != SQLITE_DONE) {
		// 	if (ret != SQLITE_ROW) goto sqlerr;
		// 	num_cols = sqlite3_column_count(sqlite_stmt);
		// 	if (num_cols != 4) goto sqlerr;

		// 	c_balance = sqlite3_column_double(sqlite_stmt, 0);
		// 	strcpy(c_first, sqlite3_column_text(sqlite_stmt, 1));
		// 	strcpy(c_middle, sqlite3_column_text(sqlite_stmt, 2));
		// 	strcpy(c_last, sqlite3_column_text(sqlite_stmt, 3));
		// }

		// sqlite3_reset(sqlite_stmt);

		memset(send_str, 0, 500);
		sprintf(send_str, "select c_balance, c_first, c_middle, c_last from customer where c_w_id = %d and c_d_id = %d and c_id = %d;", c_w_id, c_d_id, c_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			// if(num_cols != 4) goto sqlerr;
			c_balance = safe_stof(sqlite_stmt->field_values[0][0].c_str());
			strcpy(c_first, sqlite_stmt->field_values[0][1].c_str());
			strcpy(c_middle, sqlite_stmt->field_values[0][2].c_str());
			strcpy(c_last, sqlite_stmt->field_values[0][3].c_str());
		}

		ResetSqlResult(sqlite_stmt);
	}

	/* find the most recent order for this customer */

	proceed = 7;
	/*EXEC_SQL SELECT o_id, o_entry_d, COALESCE(o_carrier_id,0)
		INTO :o_id, :o_entry_d, :o_carrier_id
	        FROM orders
	        WHERE o_w_id = :c_w_id
		AND o_d_id = :c_d_id
		AND o_c_id = :c_id
		AND o_id = (SELECT MAX(o_id)
		    	    FROM orders
		    	    WHERE o_w_id = :c_w_id
		  	    AND o_d_id = :c_d_id
		    	    AND o_c_id = :c_id);*/

	// sqlite_stmt = stmt[t_num][23];

	// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, c_id);
	// sqlite3_bind_int64(sqlite_stmt, 4, c_w_id);
	// sqlite3_bind_int64(sqlite_stmt, 5, c_d_id);
	// sqlite3_bind_int64(sqlite_stmt, 6, c_id);

	// ret = sqlite3_step(sqlite_stmt);
	// if (ret != SQLITE_DONE) {
	// 	if (ret != SQLITE_ROW) goto sqlerr;
	// 	num_cols = sqlite3_column_count(sqlite_stmt);
	// 	if (num_cols != 3) goto sqlerr;

	// 	o_id = sqlite3_column_int64(sqlite_stmt, 0);
	// 	strcpy(o_entry_d, sqlite3_column_text(sqlite_stmt, 1));
	// 	o_carrier_id = sqlite3_column_int64(sqlite_stmt, 2);
	// }
	// /* find all the items in this order */

	// sqlite3_reset(sqlite_stmt);

	// First, get all o_id values for this customer and find the max
	memset(send_str, 0, 500);
			sprintf(send_str, "select max(o_id) as max_o_id from orders where o_w_id = %d and o_d_id = %d and o_c_id = %d;", c_w_id, c_d_id, c_id);
	send_command(sockfd, send_str, sqlite_stmt);
	if(!sqlite_stmt->success) goto sqlerr;
	
	// Get the max o_id value
	if(sqlite_stmt->success && sqlite_stmt->row_count > 0) {
		if(sqlite_stmt->field_values[0][0].empty()) {
			max_o_id = 0;
		} else {
			try {
				max_o_id = std::stoi(sqlite_stmt->field_values[0][0].c_str());
			} catch (const std::exception& e) {
				printf("Error converting max_o_id: %s\n", sqlite_stmt->field_values[0][0].c_str());
				goto sqlerr;
			}
		}
	}
	
	ResetSqlResult(sqlite_stmt);
	
	// Check if customer has any orders
	if (max_o_id == 0) {
		// Customer has no orders, set default values
		o_id = 0;
		strcpy(o_entry_d, "");
		o_carrier_id = 0;
	} else {
		// Then, get the order details using the max o_id
		memset(send_str, 0, 500);
		sprintf(send_str, "select o_id, o_entry_d, o_carrier_id from orders where o_w_id = %d and o_d_id = %d and o_c_id = %d and o_id = %d;", c_w_id, c_d_id, c_id, max_o_id);
		send_command(sockfd, send_str, sqlite_stmt);
		if(!sqlite_stmt->success) goto sqlerr;

		#if OUTPUT_TO_FILE
			outfile.open(file_path, std::ios::out | std::ios::app);
			outfile << "sql: " << send_str << std::endl;
			outfile << "recv: \n" << sqlite_stmt->rec_msg;
			outfile.close();
		#endif
		if(sqlite_stmt->success){
			if(sqlite_stmt->row_count < 1) goto sqlerr;
			num_cols = sqlite_stmt->field_count;
			// if(num_cols != 3) goto sqlerr;
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[0][0].empty()) {
				o_id = 0;
			} else {
				try {
					o_id = std::stoi(sqlite_stmt->field_values[0][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting o_id: %s\n", sqlite_stmt->field_values[0][0].c_str());
					goto sqlerr;
				}
			}
			strcpy(o_entry_d, sqlite_stmt->field_values[0][1].c_str());
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[0][2].empty()) {
				o_carrier_id = 0;
			} else {
				try {
					o_carrier_id = std::stoi(sqlite_stmt->field_values[0][2].c_str());
				} catch (const std::exception& e) {
					printf("Error converting o_carrier_id: %s\n", sqlite_stmt->field_values[0][2].c_str());
					goto sqlerr;
				}
			}
			if(o_carrier_id == -1){
				o_carrier_id = 0;
			}
		}

		ResetSqlResult(sqlite_stmt);
	}

	ResetSqlResult(sqlite_stmt);

	proceed = 8;
	/*EXEC_SQL DECLARE c_items CURSOR FOR
		SELECT ol_i_id, ol_supply_w_id, ol_quantity, ol_amount,
                       ol_delivery_d
		FROM order_line
	        WHERE ol_w_id = :c_w_id
		AND ol_d_id = :c_d_id
		AND ol_o_id = :o_id;*/

	// sqlite_stmt = stmt[t_num][24];

	// sqlite3_bind_int64(sqlite_stmt, 1, c_w_id);
	// sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
	// sqlite3_bind_int64(sqlite_stmt, 3, o_id);
	
	// Only query order lines if customer has orders
	if (o_id > 0) {
		memset(send_str, 0, 500);
		sprintf(send_str, "select ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_delivery_d from order_line where ol_w_id = %d and ol_d_id = %d and ol_o_id = %d;", c_w_id, c_d_id, o_id);

		send_command(sockfd, send_str, sqlite_stmt);
		if(sqlite_stmt->is_abort) goto sqlerr;

		#if OUTPUT_TO_FILE
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << send_str << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
		#endif
		i = 0;
		for(;;) {
			// ret = sqlite3_step(sqlite_stmt);
			
			if(i >= sqlite_stmt->row_count)
				break;
			// if (ret == SQLITE_DONE)
			// 	break;

			// if (ret == SQLITE_ROW) {
			// 	proceed = 10;
			// 	num_cols = sqlite3_column_count(sqlite_stmt);
			// 	if (num_cols != 5) goto sqlerr;
				
			// 	ol_i_id = sqlite3_column_int64(sqlite_stmt, 0);
			// 	ol_supply_w_id = sqlite3_column_int64(sqlite_stmt, 1);
			// 	ol_quantity = sqlite3_column_int64(sqlite_stmt, 2);
			// 	ol_amount = sqlite3_column_double(sqlite_stmt, 3);
			// 	bytes = sqlite3_column_bytes(sqlite_stmt, 4);
			// 	if (bytes)
			// 		strcpy(ol_delivery_d, sqlite3_column_text(sqlite_stmt, 4));
			// }
			// else
			// 	goto sqlerr;

			// if(sqlite_stmt->row_count >= 1){
			num_cols = sqlite_stmt->field_count;
			if(num_cols < 5) goto sqlerr;
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[i][0].empty()) {
				ol_i_id = 0;
			} else {
				try {
					ol_i_id = std::stoi(sqlite_stmt->field_values[i][0].c_str());
				} catch (const std::exception& e) {
					printf("Error converting ol_i_id: %s\n", sqlite_stmt->field_values[i][0].c_str());
					goto sqlerr;
				}
			}
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[i][1].empty()) {
				ol_supply_w_id = 0;
			} else {
				try {
					ol_supply_w_id = std::stoi(sqlite_stmt->field_values[i][1].c_str());
				} catch (const std::exception& e) {
					printf("Error converting ol_supply_w_id: %s\n", sqlite_stmt->field_values[i][1].c_str());
					goto sqlerr;
				}
			}
			// 添加错误处理，防止空字符串导致的stoi异常
			if(sqlite_stmt->field_values[i][2].empty()) {
				ol_quantity = 0;
			} else {
				try {
					ol_quantity = std::stoi(sqlite_stmt->field_values[i][2].c_str());
				} catch (const std::exception& e) {
					printf("Error converting ol_quantity: %s\n", sqlite_stmt->field_values[i][2].c_str());
					goto sqlerr;
				}
			}
			ol_amount = safe_stof(sqlite_stmt->field_values[i][3].c_str());
			bytes = strlen(sqlite_stmt->field_values[i][4].c_str());
			if(bytes){
				strcpy(ol_delivery_d, sqlite_stmt->field_values[i][4].c_str());
			}
			else{
				goto sqlerr;
			}
			// }
			i++;
		}

		// sqlite3_reset(sqlite_stmt);
		ResetSqlResult(sqlite_stmt);
	}

	// sqlite3_reset(sqlite_stmt);
	ResetSqlResult(sqlite_stmt);

	/*proceed = 9;
	EXEC_SQL OPEN c_items;

	EXEC SQL WHENEVER NOT FOUND GOTO done;*/
	
done:
	/*EXEC_SQL CLOSE c_items;*/
        /*EXEC_SQL COMMIT WORK;*/
	//if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;

	delete sqlite_stmt;  // 删除分配的内存
	return (1);

sqlerr:
        fprintf(stderr, "ordstat %d:%d\n",t_num,proceed);
	// printf("%s: error: %s\n", __func__, sqlite3_errmsg(ctx[t_num]));
	printf("failed in ordstat\n");

	if(!sqlite_stmt->is_abort){
		outfile.open(file_path, std::ios::out | std::ios::app);
		outfile << "sql: " << "send_str " << send_str<<" exec failed" << std::endl;
		outfile << "recv: \n" << sqlite_stmt->rec_msg;
		outfile.close();
	}
	ResetSqlResult(sqlite_stmt);
	send_command(sockfd, "abort;", sqlite_stmt);
	send_command(sockfd, "begin;", sqlite_stmt);
	#if OUTPUT_TO_FILE
	outfile.open(file_path, std::ios::out | std::ios::app);
	outfile << "sql: " << "begin;" << std::endl;
	outfile << "recv: \n" << sqlite_stmt->rec_msg;
	outfile.close();
	#endif
	ResetSqlResult(sqlite_stmt);
sqlerrerr:
	delete sqlite_stmt;  // 删除分配的内存
	return (0);
}

