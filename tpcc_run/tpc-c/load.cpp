/*
 * corresponds to A.6 in appendix A
 */

/*
 * ==================================================================+ | Load
 * TPCC tables
 * +==================================================================
 */
 #include <errno.h>
 #include <netdb.h>
 #include <stdio.h>
 #include <string.h>
 #include <sys/select.h>
 #include <sys/socket.h>
 #include <unistd.h>
 #include <stdlib.h>
 #include <fcntl.h>
 #include <string>
 #include <iostream>
 #include <sys/time.h>
 #include <signal.h>
 #include "timers.h"
 #include "load.h"
 #include <sys/un.h>
 
 
 #include "spt_proc.h"
 #include "tpc.h"
 
 #define NNULL ((void *)0)
 //#undef NULL
 #define MAX_MEM_BUFFER_SIZE 8192
 #define PORT_DEFAULT 8765
 
 int global_sockfd;
 
 // 信号处理函数声明
 void signal_handler(int sig);
 
 bool is_exit_command(std::string &cmd) { return cmd == "exit" || cmd == "exit;" || cmd == "bye" || cmd == "bye;"; }
 
 int init_unix_sock(const char *unix_sock_path) {
	 int sockfd = socket(PF_UNIX, SOCK_STREAM, 0);
	 if (sockfd < 0) {
		 fprintf(stderr, "failed to create unix socket. %s", strerror(errno));
		 return -1;
	 }
 
	 struct sockaddr_un sockaddr;
	 memset(&sockaddr, 0, sizeof(sockaddr));
	 sockaddr.sun_family = PF_UNIX;
	 snprintf(sockaddr.sun_path, sizeof(sockaddr.sun_path), "%s", unix_sock_path);
 
	 if (connect(sockfd, (struct sockaddr *)&sockaddr, sizeof(sockaddr)) < 0) {
		 fprintf(stderr, "failed to connect to server. unix socket path '%s'. error %s", sockaddr.sun_path,
				 strerror(errno));
		 close(sockfd);
		 return -1;
	 }
	 return sockfd;
 }
 
 int init_tcp_sock(const char *server_host, int server_port) {
	 struct hostent *host;
	 struct sockaddr_in serv_addr;
 
	 if ((host = gethostbyname(server_host)) == NULL) {
		 fprintf(stderr, "gethostbyname failed. errmsg=%d:%s\n", errno, strerror(errno));
		 return -1;
	 }
 
	 int sockfd;
	 if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
		 fprintf(stderr, "create socket error. errmsg=%d:%s\n", errno, strerror(errno));
		 return -1;
	 }
 
	 serv_addr.sin_family = AF_INET;
	 serv_addr.sin_port = htons(server_port);
	 serv_addr.sin_addr = *((struct in_addr *)host->h_addr);
	 bzero(&(serv_addr.sin_zero), 8);
 
	 if (connect(sockfd, (struct sockaddr *)&serv_addr, sizeof(struct sockaddr)) == -1) {
		 fprintf(stderr, "Failed to connect. errmsg=%d:%s\n", errno, strerror(errno));
		 close(sockfd);
		 return -1;
	 }
	 return sockfd;
 }
 void send_command(int sockfd, const char* command) {
   // printf("command : %s\n", command);
	 #if PRINT_SQL_TOFILE
	   std::fstream output;
	   output.open("sql.txt", std::ios::out | std::ios::app);
	   output << command << std::endl;
	   output.close();
	 #endif
	 
	 // 检查socket是否有效
	 if (sockfd < 0) {
		 printf("Invalid socket descriptor\n");
		 return;
	 }
	 
	 if (write(sockfd, command, strlen(command) + 1) == -1) {
		 printf("send error: %d:%s \n", errno, strerror(errno));
		 return;
	 }

	 char recv_buf[MAX_MEM_BUFFER_SIZE];
	 int len = recv(sockfd, recv_buf, MAX_MEM_BUFFER_SIZE, 0);
	 if (len < 0) {
		 fprintf(stderr, "Connection was broken: %s\n", strerror(errno));
		 return;
	 } else if (len == 0) {
		 printf("Connection has been closed\n");
		 return;
	 } else {
		 // 检查db2025数据库响应是否成功
		 std::string response(recv_buf, len);
		 if (response.find("Error") != std::string::npos || 
			 response.find("error") != std::string::npos) {
			 fprintf(stderr, "Database error: %s\n", response.c_str());
			 return;
		 }
		 // 成功响应，可以打印或忽略
		 // printf("Response: %s\n", response.c_str());
	 }
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      MakeAddress() | DESCRIPTION |      Build an Address |
  * ARGUMENTS
  * +==================================================================
  */
 void 
 MakeAddress(char* str1, char* str2, char* city,char* state,char* zip)
 {
	 str1[ MakeAlphaString(10, 20, str1) ] = 0;	/* Street 1 */
	 str2[ MakeAlphaString(10, 20, str2) ] = 0;	/* Street 2 */
	 city[ MakeAlphaString(10, 20, city) ] = 0;	/* City */
	 state[ MakeAlphaString(2, 2, state) ] = 0;	/* State */
	 zip[ MakeNumberString(9, 9, zip) ] = 0;	/* Zip */
 }
 /* Global SQL Variables */
 char            timestamp[81];
 long            count_ware;
 int             fd, seed;
 
 int             particle_flg = 0; /* "1" means particle mode */
 int             part_no = 0; /* 1:items 2:warehouse 3:customer 4:orders */
 long            min_ware = 1;
 long            max_ware;
 
 /* Global Variables */
 int             i;
 int             option_debug = 0;	/* 1 if generating debug output    */
 int             is_local = 1;           /* "1" mean local */
 
 #define DB_STRING_MAX 51
 
 // int
 // try_stmt_execute(sqlite3_stmt *sqlite_stmt)
 // {
 //     int ret = sqlite3_step(sqlite_stmt);
 //     if (ret != SQLITE_DONE) {
 // 	    //printf("\n%d, %s, %s\n", mysql_errno(mysql), mysql_sqlstate(mysql), mysql_error(mysql) );
 // 	printf("%s: error in executing statement\n", __func__);
 //         //mysql_rollback(mysql);
 // 	sqlite3_exec(sqlite, "ROLLBACK;", NULL, NULL, NULL);
 
 //     }
 //     return ret;
 // }
 
 /*
  * ==================================================================+ |
  * main() | ARGUMENTS |      Warehouses n [Debug] [Help]
  * +==================================================================
  */
 int  main()
 {
	 char            arg[2];
	 char           *ptr;
 
	 int i,c;
	 
	 // 设置信号处理，确保程序能够优雅退出
	 signal(SIGINT, signal_handler);
	 signal(SIGTERM, signal_handler);
 
	 /* initialize */
	 count_ware = 0;
 
	 printf("*************************************\n");
	 printf("*** TPCC-sqlite3 Data Loader        ***\n");
	 printf("*************************************\n");
 
	 const char *unix_socket_path = nullptr;
	 const char *server_host = "127.0.0.1";
	 int server_port = PORT_DEFAULT;
	 int opt;
	 int sockfd;
	 if (unix_socket_path != nullptr) {
		 sockfd = init_unix_sock(unix_socket_path);
	 } else {
		 sockfd = init_tcp_sock(server_host, server_port);
	 }
	 if (sockfd < 0) {
		 return 1;
	 }
	 count_ware = 2;// 仓库数量
	 if(particle_flg==0){
		 min_ware = 1;
		 max_ware = count_ware;
	 }
 
	 if(particle_flg==1){
		 printf("  [part(1-4)]: %d\n", part_no);
		 printf("     [MIN WH]: %d\n", min_ware);
		 printf("     [MAX WH]: %d\n", max_ware);
	 }
 
	 fd = open("/dev/urandom", O_RDONLY);
	 if (fd == -1) {
		 fd = open("/dev/random", O_RDONLY);
		 if (fd == -1) {
			 struct timeval  tv;
			 gettimeofday(&tv, NNULL);
			 seed = (tv.tv_sec ^ tv.tv_usec) * tv.tv_sec * tv.tv_usec ^ tv.tv_sec;
		 }else{
			 read(fd, &seed, sizeof(seed));
			 close(fd);
		 }
	 }else{
		 read(fd, &seed, sizeof(seed));
		 close(fd);
	 }
	 SetSeed(seed);
 
	 /* Initialize timestamp (for date columns) */
	 gettimestamp(timestamp, STRFTIME_FORMAT, TIMESTAMP_LEN);
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO Error_SqlCall; */
 
	 
 
	 /*
	 for( i=0; i<11; i++ ){
		 stmt[i] = mysql_stmt_init(mysql);
		 if(!stmt[i]) goto Error_SqlCall_close;
	 }
	 */
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO item values(?,?,?,?,?)",
	 // 		   -1, &stmt[0], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO warehouse values(?,?,?,?,?,?,?,?,?)",
	 // 		   -1, &stmt[1], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO stock values(?,?,?,?,?,?,?,?,?,?,?,?,?,0,0,0,?)",
	 // 		   -1, &stmt[2], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO district values(?,?,?,?,?,?,?,?,?,?,?)",
	 // 		   -1, &stmt[3], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO customer values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, 10.0, 1, 0,?)",
	 // 		   -1, &stmt[4], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO history values(?,?,?,?,?,?,?,?)",
	 // 		   -1, &stmt[5], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO orders values(?,?,?,?,?,NULL,?, 1)",
	 // 		   -1, &stmt[6], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO new_orders values(?,?,?)",
	 // 		   -1, &stmt[7], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO orders values(?,?,?,?,?,?,?, 1)",
	 // 		   -1, &stmt[8], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO order_line values(?,?,?,?,?,?, NULL,?,?,?)",
	 // 		   -1, &stmt[9], NULL) != SQLITE_OK) goto Error_SqlCall_close;
	 // if( sqlite3_prepare_v2(sqlite,
	 // 		   "INSERT INTO order_line values(?,?,?,?,?,?,?,?,?,?)",
	 // 		   -1, &stmt[10], NULL) != SQLITE_OK) goto Error_SqlCall_close;
 
 
	 /* exec sql begin transaction; */
 
	 printf("TPCC Data Load Started...\n");
		 // 创建表
	 send_command(sockfd, "create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));");
	 send_command(sockfd, "create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);");
	 send_command(sockfd, "create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(19), h_amount float, h_data char(24));");
	 send_command(sockfd, "create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));");
	 send_command(sockfd, "create table new_orders (no_o_id int, no_d_id int, no_w_id int);");
	 send_command(sockfd, "create table order_line (ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));");
	 send_command(sockfd, "create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);");
	 send_command(sockfd, "create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));");
	 send_command(sockfd, "create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);");
 
	 // // 创建索引
	 /*
	 create index warehouse(w_id);
	 create index district(d_w_id, d_id);
	 create index customer(c_w_id, c_d_id, c_id);
	 create index new_orders(no_w_id, no_d_id, no_o_id);
	 create index orders(o_w_id, o_d_id, o_id);
	 create index order_line(ol_w_id, ol_d_id, ol_o_id, ol_number);
	 create index item(i_id);
	 create index stock(s_w_id, s_i_id);    
	 */
	 send_command(sockfd, "create index warehouse(w_id);");
	 send_command(sockfd, "create index district(d_w_id, d_id);");
	 send_command(sockfd, "create index customer(c_w_id, c_d_id, c_id);");
	 send_command(sockfd, "create index new_orders(no_w_id, no_d_id, no_o_id);");
	 send_command(sockfd, "create index orders(o_w_id, o_d_id, o_id);");
	 send_command(sockfd, "create index order_line(ol_w_id, ol_d_id, ol_o_id, ol_number);");
	 send_command(sockfd, "create index item(i_id);");
	 send_command(sockfd, "create index stock(s_w_id, s_i_id);");
	 send_command(sockfd, "set output_file off");
 
 
	 global_sockfd = sockfd;
 
 
	 send_command(global_sockfd, "begin;");
	 //if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto Error_SqlCall;
 
	 // if(particle_flg==0){
		 LoadItems();
		 LoadWare();
		 LoadCust();
		 LoadOrd();
	 // }else if(particle_flg==1){
	 //     switch(part_no){
	 // 	case 1:
	 // 	    LoadItems();
	 // 	    break;
	 // 	case 2:
	 // 	    LoadWare();
	 // 	    break;
	 // 	case 3:
	 // 	    LoadCust();
	 // 	    break;
	 // 	case 4:
	 // 	    LoadOrd();
	 // 	    break;
	 // 	default:
	 // 	    printf("Unknown part_no\n");
	 // 	    printf("1:ITEMS 2:WAREHOUSE 3:CUSTOMER 4:ORDERS\n");
	 //     }
	 // }
 
	 /* EXEC SQL COMMIT WORK; */
 
	 //if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto Error_SqlCall;
 
	 // for( i=0; i<11; i++ ){
	 // 	sqlite3_reset(stmt[i]);
	 // }
 
	 /* EXEC SQL DISCONNECT; */
 
	 // sqlite3_close(sqlite);
	 
	 printf("\n...DATA LOADING COMPLETED SUCCESSFULLY.\n");
	 
	 // 先提交事务
	 send_command(global_sockfd, "commit;");
	 
	 // 等待一下确保事务提交完成
	 usleep(100000); // 等待100ms
	 
	 // 关闭socket连接
	 if (global_sockfd >= 0) {
		 close(global_sockfd);
	 }
	 
	 printf("Database connection closed successfully.\n");
	 exit(0);
 Error_SqlCall_close:
 Error_SqlCall:
	 // Error(0);
	 Error();
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      LoadItems | DESCRIPTION |      Loads the Item table |
  * ARGUMENTS |      none
  * +==================================================================
  */
 void 
 LoadItems()
 {
 
	 int ret;
	 int             i_id;
	 int             i_im_id;
		 char            i_name[25];
	 float           i_price;
	 char            i_data[51];
 
	 int             idatasiz;
	 int             orig[MAXITEMS+1];
	 int             pos;
	 int             i;
	 int             retried = 0;
	 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */
 
	 printf("Loading Item \n");
 
	 for (i = 0; i < MAXITEMS / 10; i++)
		 orig[i] = 0;
	 for (i = 0; i < MAXITEMS / 10; i++) {
		 do {
			 pos = RandomNumber(0L, MAXITEMS);
		 } while (orig[pos]);
		 orig[pos] = 1;
	 }
 retry:
	 if (retried)
		 printf("Retrying ...\n");
	 retried = 1;
 
	 // if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 // send_command(global_sockfd, "begin;");
 
	 for (i_id = 1; i_id <= MAXITEMS; i_id++) {
 
		 /* Generate Item Data */
		 i_im_id = RandomNumber(1L, 10000L);
 
				 i_name[ MakeAlphaString(14, 24, i_name) ] = 0;
 
		 i_price = ((int) RandomNumber(100L, 10000L)) / 100.0;
 
		 idatasiz = MakeAlphaString(26, 50, i_data);
		 i_data[idatasiz] = 0;
 
		 if (orig[i_id]) {
			 pos = RandomNumber(0L, idatasiz - 8);
			 i_data[pos] = 'o';
			 i_data[pos + 1] = 'r';
			 i_data[pos + 2] = 'i';
			 i_data[pos + 3] = 'g';
			 i_data[pos + 4] = 'i';
			 i_data[pos + 5] = 'n';
			 i_data[pos + 6] = 'a';
			 i_data[pos + 7] = 'l';
		 }
		 if (option_debug)
			 printf("IID = %ld, Name= %16s, Price = %5.2f\n",
					i_id, i_name, i_price);
 
 #if 0
		 printf("about to exec sql\n");
		 fflush(stdout);
 #endif
 
		 /* EXEC SQL INSERT INTO
						 item
						 values(:i_id,:i_im_id,:i_name,:i_price,:i_data); */
 
		 // sqlite_stmt = stmt[0];
		 
		 // //printf("%s: inserting item id = %d\n", __func__, i_id);
		 // sqlite3_bind_int64(sqlite_stmt, 1, i_id);
		 // sqlite3_bind_int64(sqlite_stmt, 2, i_im_id);
		 // sqlite3_bind_text(sqlite_stmt, 3, i_name, -1, SQLITE_STATIC);
		 // sqlite3_bind_double(sqlite_stmt, 4, i_price);
		 // sqlite3_bind_text(sqlite_stmt, 5, i_data, -1,  SQLITE_STATIC);
		 // std::string send_str = "insert into item values(" + std::to_string(i_id) + "," + std::to_string(i_im_id) + ",'" + i_name + "'," + std::to_string(i_price) + ",'" + i_data + "');";
		 char send_str[1024];
		 sprintf(send_str, "insert into item values(%d,%d,'%s',%f,'%s');", i_id, i_im_id, i_name, i_price, i_data);
		 send_command(global_sockfd, send_str);
 
		 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
		 // sqlite3_reset(sqlite_stmt);
 
 #if 0
		 printf("done executing sql\n");
		 fflush(stdout);
 #endif
 
		 if (!(i_id % 100)) {
			 printf(".");
			 fflush(stdout);
 
			 if (!(i_id % 5000))
				 printf(" %ld\n", i_id);
		 }
	 }
 
	 /* EXEC SQL COMMIT WORK; */
	 // if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 // send_command(global_sockfd, "commit;");
 
	 printf("Item Done. \n");
	 return;
 sqlerr:
	 // Error(stmt[0]);
	 Error();
	 // std::cout << "Error in LoadItems\n";
	 printf("Error in LoadItems\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      LoadWare | DESCRIPTION |      Loads the Warehouse
  * table |      Loads Stock, District as Warehouses are created | ARGUMENTS |
  * none +==================================================================
  */
 void 
 LoadWare()
 {
 
	 int             w_id;
		 char            w_name[11];
		 char            w_street_1[21];
		 char            w_street_2[21];
		 char            w_city[21];
		 char            w_state[3];
		 char            w_zip[10];
	 float           w_tax;
	 float           w_ytd;
 
	 int             tmp;
	 int             retried = 0;
	 // sqlite3_stmt* sqlite_stmt;
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */
 
	 printf("Loading Warehouse \n");
	 w_id = min_ware;
  retry:
	 if (retried)
		 printf("Retrying ....\n");
	 retried = 1;
 
	 for (; w_id <= max_ware; w_id++) {
 
		 // if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
		 // send_command(global_sockfd, "begin;");
		 /* Generate Warehouse Data */
 
				 w_name[ MakeAlphaString(6, 10, w_name) ] = 0;
 
		 MakeAddress(w_street_1, w_street_2, w_city, w_state, w_zip);
 
		 w_tax = ((float) RandomNumber(10L, 20L)) / 100.0;
		 w_ytd = 300000.00;
 
		 if (option_debug)
			 printf("WID = %ld, Name= %16s, Tax = %5.2f\n",
					w_id, w_name, w_tax);
 
		 /*EXEC SQL INSERT INTO
						 warehouse
						 values(:w_id,:w_name,
						:w_street_1,:w_street_2,:w_city,:w_state,
						:w_zip,:w_tax,:w_ytd);*/
 
		 // sqlite_stmt = stmt[1];
 
		 // sqlite3_bind_int64(sqlite_stmt, 1, w_id);
		 // sqlite3_bind_text(sqlite_stmt, 2, w_name, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 3, w_street_1, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 4, w_street_2, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 5, w_city, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 6, w_state, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 7, w_zip, -1, SQLITE_STATIC);
		 // sqlite3_bind_double(sqlite_stmt, 8, w_tax);
		 // sqlite3_bind_double(sqlite_stmt, 9, w_ytd);
 
		 // std::string send_str = "insert into warehouse values(" + std::to_string(w_id) + ",'" + w_name + "','" + w_street_1 + "','" + w_street_2 + "','" + w_city + "','" + w_state + "','" + w_zip + "'," + std::to_string(w_tax) + "," + std::to_string(w_ytd) + ");";
		 // send_command(global_sockfd, send_str);
		 char send_str[1024];
		 sprintf(send_str, "insert into warehouse values(%d,'%s','%s','%s','%s','%s','%s',%f,%f);",
			 w_id, w_name, w_street_1, w_street_2, w_city, w_state, w_zip, w_tax, w_ytd);
		 send_command(global_sockfd, send_str);
 
		 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
		 
		 /** Make Rows associated with Warehouse **/
		 if( Stock(w_id) ) goto retry;
		 if( District(w_id) ) goto retry;
 
		 // sqlite3_reset(sqlite_stmt);
 
		 /* EXEC SQL COMMIT WORK; */
		 // if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
		 
		 // send_command(global_sockfd, "commit;");
 
	 }
 
	 return;
 sqlerr:
	 // Error(0);
	 Error();
	 printf("Error in LoadWare\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      LoadCust | DESCRIPTION |      Loads the Customer Table
  * | ARGUMENTS |      none
  * +==================================================================
  */
 void 
 LoadCust()
 {
 
	 int             w_id;
	 int             d_id;
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */
 
	 // if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 // send_command(global_sockfd, "begin;");
 
	 for (w_id = min_ware; w_id <= max_ware; w_id++)
		 for (d_id = 1L; d_id <= DIST_PER_WARE; d_id++)
			 Customer(d_id, w_id);
 
	 /* EXEC SQL COMMIT WORK;*/	/* Just in case */
	 // if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 // send_command(global_sockfd, "commit;");
 
	 return;
 sqlerr:
	 // Error(0);
	 Error();
	 printf("Error in LoadCust\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      LoadOrd | DESCRIPTION |      Loads the Orders and
  * Order_Line Tables | ARGUMENTS |      none
  * +==================================================================
  */
 void 
 LoadOrd()
 {
 
	 int             w_id;
	 float           w_tax;
	 int             d_id;
	 float           d_tax;
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
	 // if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 // send_command(global_sockfd, "begin;");
 
	 for (w_id = min_ware; w_id <= max_ware; w_id++)
		 for (d_id = 1L; d_id <= DIST_PER_WARE; d_id++)
			 Orders(d_id, w_id);
 
	 /* EXEC SQL COMMIT WORK; */	/* Just in case */
	 // if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 // send_command(global_sockfd, "commit;");
 
	 return;
 sqlerr:
	 // Error(0);
	 Error();
	 printf("Error in LoadOrd\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      Stock | DESCRIPTION |      Loads the Stock table |
  * ARGUMENTS |      w_id - warehouse id
  * +==================================================================
  */
 int Stock(int w_id)
 {
 
	 int             s_i_id;
	 int             s_w_id;
	 int             s_quantity;
 
	 char            s_dist_01[25];
	 char            s_dist_02[25];
	 char            s_dist_03[25];
	 char            s_dist_04[25];
	 char            s_dist_05[25];
	 char            s_dist_06[25];
	 char            s_dist_07[25];
	 char            s_dist_08[25];
	 char            s_dist_09[25];
	 char            s_dist_10[25];
	 char            s_data[51];
 
	 int             sdatasiz;
	 int             orig[MAXITEMS+1];
	 int             pos;
	 int             i;
	 int             error;
	 // sqlite3_stmt* sqlite_stmt;
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
	 printf("Loading Stock Wid=%ld\n", w_id);
	 s_w_id = w_id;
 
	 for (i = 0; i < MAXITEMS / 10; i++)
		 orig[i] = 0;
	 for (i = 0; i < MAXITEMS / 10; i++) {
		 do {
			 pos = RandomNumber(0L, MAXITEMS);
		 } while (orig[pos]);
		 orig[pos] = 1;
	 }
 
 retry:
	 for (s_i_id = 1; s_i_id <= MAXITEMS; s_i_id++) {
 
		 /* Generate Stock Data */
		 s_quantity = RandomNumber(10L, 100L);
 
		 s_dist_01[ MakeAlphaString(24, 24, s_dist_01) ] = 0;
		 s_dist_02[ MakeAlphaString(24, 24, s_dist_02) ] = 0;
		 s_dist_03[ MakeAlphaString(24, 24, s_dist_03) ] = 0;
		 s_dist_04[ MakeAlphaString(24, 24, s_dist_04) ] = 0;
		 s_dist_05[ MakeAlphaString(24, 24, s_dist_05) ] = 0;
		 s_dist_06[ MakeAlphaString(24, 24, s_dist_06) ] = 0;
		 s_dist_07[ MakeAlphaString(24, 24, s_dist_07) ] = 0;
		 s_dist_08[ MakeAlphaString(24, 24, s_dist_08) ] = 0;
		 s_dist_09[ MakeAlphaString(24, 24, s_dist_09) ] = 0;
		 s_dist_10[ MakeAlphaString(24, 24, s_dist_10) ] = 0;
		 sdatasiz = MakeAlphaString(26, 50, s_data);
		 s_data[sdatasiz] = 0;
 
		 if (orig[s_i_id]) {
			 pos = RandomNumber(0L, sdatasiz - 8);
 
			 s_data[pos] = 'o';
			 s_data[pos + 1] = 'r';
			 s_data[pos + 2] = 'i';
			 s_data[pos + 3] = 'g';
			 s_data[pos + 4] = 'i';
			 s_data[pos + 5] = 'n';
			 s_data[pos + 6] = 'a';
			 s_data[pos + 7] = 'l';
 
		 }
		 /*EXEC SQL INSERT INTO
						 stock
						 values(:s_i_id,:s_w_id,:s_quantity,
						:s_dist_01,:s_dist_02,:s_dist_03,:s_dist_04,:s_dist_05,
						:s_dist_06,:s_dist_07,:s_dist_08,:s_dist_09,:s_dist_10,
						0, 0, 0,:s_data);*/
 
		 // sqlite_stmt = stmt[2];
 
		 // sqlite3_bind_int64(sqlite_stmt, 1, s_i_id);
		 // sqlite3_bind_int64(sqlite_stmt, 2, s_w_id);
		 // sqlite3_bind_int64(sqlite_stmt, 3, s_quantity);
		 // sqlite3_bind_text(sqlite_stmt, 4, s_dist_01, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 5, s_dist_02, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 6, s_dist_03, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 7, s_dist_04, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 8, s_dist_05, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 9, s_dist_06, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 10, s_dist_07, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 11, s_dist_08, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 12, s_dist_09, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 13, s_dist_10, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 14, s_data, -1, SQLITE_STATIC);
 
		 // std::string send_str = "insert into stock values(" + std::to_string(s_i_id) + "," + std::to_string(s_w_id) + "," + std::to_string(s_quantity) + ",'" + s_dist_01 + "','" + s_dist_02 + "','" + s_dist_03 + "','" + s_dist_04 + "','" + s_dist_05 + "','" + s_dist_06 + "','" + s_dist_07 + "','" + s_dist_08 + "','" + s_dist_09 + "','" + s_dist_10 + "','" + s_data + "');";
		 // send_command(global_sockfd, send_str);
		 char send_str[1024];
		 
		 sprintf(send_str, "insert into stock values(%d,%d,%d,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',0,0,0,'%s');",
			 s_i_id, s_w_id, s_quantity, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10, s_data);
		 send_command(global_sockfd, send_str);
		 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
		 // sqlite3_reset(sqlite_stmt);
		 
		 if (option_debug)
			 printf("SID = %ld, WID = %ld, Quan = %ld\n",
					s_i_id, s_w_id, s_quantity);
 
		 if (!(s_i_id % 100)) {
			 printf(".");
			 fflush(stdout);
			 if (!(s_i_id % 5000))
				 printf(" %ld\n", s_i_id);
		 }
	 }
 
	 printf(" Stock Done.\n");
 out:
	 return 0;
 sqlerr:
	 Error();
	 printf("Error in Stock\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      District | DESCRIPTION |      Loads the District table
  * | ARGUMENTS |      w_id - warehouse id
  * +==================================================================
  */
 int 
 District(int w_id)
 {
 
	 int             d_id;
	 int             d_w_id;
 
	 char            d_name[11];
	 char            d_street_1[21];
	 char            d_street_2[21];
	 char            d_city[21];
	 char            d_state[3];
	 char            d_zip[10];
 
	 float           d_tax;
	 float           d_ytd;
	 int             d_next_o_id;
	 int             error;
	 // sqlite3_stmt* sqlite_stmt;
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
 
	 printf("Loading District\n");
	 d_w_id = w_id;
	 d_ytd = 30000.0;
	 d_next_o_id = ORD_PER_DIST + 1;
 retry:
	 for (d_id = 1; d_id <= DIST_PER_WARE; d_id++) {
 
		 /* Generate District Data */
 
		 d_name[ MakeAlphaString(6L, 10L, d_name) ] = 0;
		 MakeAddress(d_street_1, d_street_2, d_city, d_state, d_zip);
 
		 d_tax = ((float) RandomNumber(10L, 20L)) / 100.0;
 
		 /*EXEC SQL INSERT INTO
						 district
						 values(:d_id,:d_w_id,:d_name,
						:d_street_1,:d_street_2,:d_city,:d_state,:d_zip,
						:d_tax,:d_ytd,:d_next_o_id);*/
 
		 // sqlite_stmt = stmt[3];
 
		 // sqlite3_bind_int64(sqlite_stmt, 1, d_id);
		 // sqlite3_bind_int64(sqlite_stmt, 2, d_w_id);
		 // sqlite3_bind_text(sqlite_stmt, 3, d_name, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 4, d_street_1, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 5, d_street_2, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 6, d_city, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 7, d_state, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 8, d_zip, -1, SQLITE_STATIC);
		 // sqlite3_bind_double(sqlite_stmt, 9, d_tax);
		 // sqlite3_bind_double(sqlite_stmt, 10, d_ytd);
		 // sqlite3_bind_int64(sqlite_stmt, 11, d_next_o_id);
 
		 // send_command(global_sockfd, "insert into district values(" + std::to_string(d_id) + "," + std::to_string(d_w_id) + ",'" + d_name + "','" + d_street_1 + "','" + d_street_2 + "','" + d_city + "','" + d_state + "','" + d_zip + "'," + std::to_string(d_tax) + "," + std::to_string(d_ytd) + "," + std::to_string(d_next_o_id) + ");");
		 char send_str[1024];
		 sprintf(send_str, "insert into district values(%d,%d,'%s','%s','%s','%s','%s','%s',%f,%f,%d);", d_id, d_w_id, d_name, d_street_1, d_street_2, d_city, d_state, d_zip, d_tax, d_ytd, d_next_o_id);
		 send_command(global_sockfd, send_str);
		 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
		 // sqlite3_reset(sqlite_stmt);
		 
		 if (option_debug)
			 printf("DID = %ld, WID = %ld, Name = %10s, Tax = %5.2f\n",
					d_id, d_w_id, d_name, d_tax);
 
	 }
	 
	 printf(" Stock Done.\n");
 out:
	 return 0;
 sqlerr:
	 // Error(0);
	 Error();
	 printf("Error in District\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      Customer | DESCRIPTION |      Loads Customer Table |
  * Also inserts corresponding history record | ARGUMENTS |      id   -
  * customer id |      d_id - district id |      w_id - warehouse id
  * +==================================================================
  */
 void 
 Customer(int d_id, int w_id)
 {
	 int             c_id;
	 int             c_d_id;
	 int             c_w_id;
 
	 char            c_first[17];
	 char            c_middle[3];
	 char            c_last[17];
	 char            c_street_1[21];
	 char            c_street_2[21];
	 char            c_city[21];
	 char            c_state[3];
	 char            c_zip[10];
	 char            c_phone[17];
	 char            c_since[12];
	 char            c_credit[3];
 
	 int             c_credit_lim;
	 float           c_discount;
	 float           c_balance;
	 // char            c_data[501];
	 char            c_data[51];
 
	 float           h_amount;
 
	 char            h_data[25];
	 int             retried = 0;
 
	 /*EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
 
	 printf("Loading Customer for DID=%ld, WID=%ld\n", d_id, w_id);
 
 retry:
	 if (retried)
		 printf("Retrying ...\n");
	 retried = 1;
 
	 //if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
 
	 for (c_id = 1; c_id <= CUST_PER_DIST; c_id++) {
 
		 /* Generate Customer Data */
		 c_d_id = d_id;
		 c_w_id = w_id;
 
		 c_first[ MakeAlphaString(8, 16, c_first) ] = 0;
		 c_middle[0] = 'O';
		 c_middle[1] = 'E';
		 c_middle[2] = 0;
 
		 if (c_id <= 1000) {
			 Lastname(c_id - 1, c_last);
		 } else {
			 Lastname(NURand(255, 0, 999), c_last);
		 }
 
		 MakeAddress(c_street_1, c_street_2, c_city, c_state, c_zip);
		 c_phone[ MakeNumberString(16, 16, c_phone) ] = 0;
 
		 if (RandomNumber(0L, 1L))
			 c_credit[0] = 'G';
		 else
			 c_credit[0] = 'B';
		 c_credit[1] = 'C';
		 c_credit[2] = 0;
 
		 c_credit_lim = 50000;
		 c_discount = ((float) RandomNumber(0L, 50L)) / 100.0;
		 c_balance = -10.0;
 
		 // c_data[ MakeAlphaString(300, 500, c_data) ] = 0;
		 c_data[ MakeAlphaString(30, 50, c_data) ] = 0;
 
		 /*EXEC SQL INSERT INTO
						 customer
						 values(:c_id,:c_d_id,:c_w_id,
				   :c_first,:c_middle,:c_last,
				   :c_street_1,:c_street_2,:c_city,:c_state,
				   :c_zip,
					   :c_phone, :timestamp,
				   :c_credit,
				   :c_credit_lim,:c_discount,:c_balance,
				   10.0, 1, 0,:c_data);*/
 
 
		 // sqlite_stmt = stmt[4];
 
		 // sqlite3_bind_int64(sqlite_stmt, 1, c_id);
		 // sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		 // sqlite3_bind_int64(sqlite_stmt, 3, c_w_id);
		 // sqlite3_bind_text(sqlite_stmt, 4, c_first, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 5, c_middle, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 6, c_last, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 7, c_street_1, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 8, c_street_2, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 9, c_city, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 10, c_state, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 11, c_zip, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 12, c_phone, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 13, timestamp, -1, SQLITE_STATIC);
		 // sqlite3_bind_text(sqlite_stmt, 14, c_credit, -1, SQLITE_STATIC);
		 // sqlite3_bind_int64(sqlite_stmt, 15, c_credit_lim);
		 // sqlite3_bind_double(sqlite_stmt, 16, c_discount);		
		 // sqlite3_bind_double(sqlite_stmt, 17, c_balance);
		 // sqlite3_bind_text(sqlite_stmt, 18, c_data, -1, SQLITE_STATIC);
 
		 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
		 // send_command(global_sockfd, "insert into customer values(" + std::to_string(c_id) + "," + std::to_string(c_d_id) + "," + std::to_string(c_w_id) + ",'" + 
		 // c_first + "','" + c_middle + "','" + c_last + "','" + c_street_1 + "','" + c_street_2 + "','" + c_city + "','" + c_state + "','" + c_zip + "','" + c_phone + "','" + 
		 // timestamp + "','" + c_credit + "'," + std::to_string(c_credit_lim) + "," + std::to_string(c_discount) + "," + std::to_string(c_balance) + ", 10.0, 1 , 0,'" + c_data + "');");
		 // // sqlite3_reset(sqlite_stmt);
		 
		 char send_str[1024];
		 
		 sprintf(send_str, "insert into customer values(%ld,%ld,%ld,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%ld,%f,%f,10.0,1,0,'%s');", c_id, c_d_id, c_w_id, c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, timestamp, c_credit, c_credit_lim, c_discount, c_balance, c_data);
		 send_command(global_sockfd, send_str);
		 h_amount = 10.0;
 
		 h_data[ MakeAlphaString(12, 24, h_data) ] = 0;
 
		 /*EXEC SQL INSERT INTO
						 history
						 values(:c_id,:c_d_id,:c_w_id,
						:c_d_id,:c_w_id, :timestamp,
						:h_amount,:h_data);*/
		 
		 // sqlite_stmt = stmt[5];
 
		 // sqlite3_bind_int64(sqlite_stmt, 1, c_id);
		 // sqlite3_bind_int64(sqlite_stmt, 2, c_d_id);
		 // sqlite3_bind_int64(sqlite_stmt, 3, c_w_id);
		 // sqlite3_bind_int64(sqlite_stmt, 4, c_d_id);
		 // sqlite3_bind_int64(sqlite_stmt, 5, c_w_id);
		 // sqlite3_bind_text(sqlite_stmt, 6, timestamp, -1, SQLITE_STATIC);
		 // sqlite3_bind_double(sqlite_stmt, 7, h_amount);		
		 // sqlite3_bind_text(sqlite_stmt, 8, h_data, -1, SQLITE_STATIC);
		 // send_command(global_sockfd, "insert into history values(" + std::to_string(c_id) + "," + std::to_string(c_d_id) + "," + std::to_string(c_w_id) + "," +	
		 // std::to_string(c_d_id) + "," + std::to_string(c_w_id) + ",'" + timestamp + "'," + std::to_string(h_amount) + ", '" + h_data + "');");
 
		 char send_str2[1024];
		 
		 sprintf(send_str2, "insert into history values(%ld,%ld,%ld,%ld,%ld,'%s',%f,'%s');", c_id, c_d_id, c_w_id, c_d_id, c_w_id, timestamp, h_amount, h_data);
		 send_command(global_sockfd, send_str2);
		 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
		 // sqlite3_reset(sqlite_stmt);
		 
		 if (option_debug)
			 printf("CID = %ld, LST = %s, P# = %s\n",
					c_id, c_last, c_phone);
		 if (!(c_id % 100)) {
			  printf(".");
			 fflush(stdout);
			 if (!(c_id % 1000))
				 printf(" %ld\n", c_id);
		 }
	 }
	 /* EXEC SQL COMMIT WORK; */
	 //if( mysql_commit(mysql) ) goto sqlerr;
	 //if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
	 printf("Customer Done.\n");
 
	 return;
 sqlerr:
	 Error();
	 printf("Error in LoadCust\n");
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      Orders | DESCRIPTION |      Loads the Orders table |
  * Also loads the Order_Line table on the fly | ARGUMENTS |      w_id -
  * warehouse id
  * +==================================================================
  */
 void 
 Orders(int d_id, int w_id)
 {
 
	 int             o_id;
	 int             o_c_id;
	 int             o_d_id;
	 int             o_w_id;
	 int             o_carrier_id;
	 int             o_ol_cnt;
	 int             ol;
	 int             ol_i_id;
	 int             ol_supply_w_id;
	 int             ol_quantity;
	 float           ol_amount;
	 char            ol_dist_info[25];
	 float           i_price;
	 float           c_discount;
	 float           tmp_float;
	 int             retried = 0;
 
	 /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */
 
	 printf("Loading Orders for D=%ld, W= %ld\n", d_id, w_id);
	 o_d_id = d_id;
	 o_w_id = w_id;
 retry:
	 if (retried)
		 printf("Retrying ...\n");
	 retried = 1;
	 InitPermutation();	/* initialize permutation of customer numbers */
 
	 //if( sqlite3_exec(sqlite, "BEGIN TRANSACTION;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
 
	 for (o_id = 1; o_id <= ORD_PER_DIST; o_id++) {
 
		 /* Generate Order Data */
		 o_c_id = GetPermutation();
		 o_carrier_id = RandomNumber(1L, 10L);
		 o_ol_cnt = RandomNumber(5L, 15L);
 
		 if (o_id > 2100) {	/* the last 900 orders have not been
					  * delivered) */
			 /*EXEC SQL INSERT INTO
							 orders
							 values(:o_id,:o_d_id,:o_w_id,:o_c_id,
							:timestamp,
							NULL,:o_ol_cnt, 1);*/
 
			 // sqlite_stmt = stmt[6];
 
			 // sqlite3_bind_int64(sqlite_stmt, 1, o_id);
			 // sqlite3_bind_int64(sqlite_stmt, 2, o_d_id);
			 // sqlite3_bind_int64(sqlite_stmt, 3, o_w_id);
			 // sqlite3_bind_int64(sqlite_stmt, 4, o_c_id);
			 // sqlite3_bind_text(sqlite_stmt, 5, timestamp, -1, SQLITE_STATIC);
			 // sqlite3_bind_int64(sqlite_stmt, 6, o_ol_cnt);
 
			 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
			 // sqlite3_reset(sqlite_stmt);
			 
			 char send_str[1024];
			 
			 sprintf(send_str, "insert into orders values(%ld,%ld,%ld,%ld,'%s',-1,%ld, 1);", o_id, o_d_id, o_w_id, o_c_id, timestamp, o_ol_cnt);		
			 send_command(global_sockfd, send_str);	
			 /*EXEC SQL INSERT INTO
							 new_orders
							 values(:o_id,:o_d_id,:o_w_id);*/
 
			 // sqlite_stmt = stmt[7];
 
			 // sqlite3_bind_int64(sqlite_stmt, 1, o_id);
			 // sqlite3_bind_int64(sqlite_stmt, 2, o_d_id);
			 // sqlite3_bind_int64(sqlite_stmt, 3, o_w_id);
 
			 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
			 // sqlite3_reset(sqlite_stmt);
			 
			 memset(send_str, 0, 1024);
			 sprintf(send_str, "insert into new_orders values(%ld,%ld,%ld);", o_id, o_d_id, o_w_id);			
			 send_command(global_sockfd, send_str);
		 } else {
			 /*EXEC SQL INSERT INTO
				 orders
				 values(:o_id,:o_d_id,:o_w_id,:o_c_id,
					:timestamp,
					:o_carrier_id,:o_ol_cnt, 1);*/
 
 
			 // sqlite_stmt = stmt[8];
 
			 // sqlite3_bind_int64(sqlite_stmt, 1, o_id);
			 // sqlite3_bind_int64(sqlite_stmt, 2, o_d_id);
			 // sqlite3_bind_int64(sqlite_stmt, 3, o_w_id);
			 // sqlite3_bind_int64(sqlite_stmt, 4, o_c_id);
			 // sqlite3_bind_text(sqlite_stmt, 5, timestamp, -1, SQLITE_STATIC);
			 // sqlite3_bind_int64(sqlite_stmt, 6, o_carrier_id);
			 // sqlite3_bind_int64(sqlite_stmt, 7, o_ol_cnt);
 
			 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
 
			 // sqlite3_reset(sqlite_stmt);
			 
			 char send_str[1024];
			 
			 sprintf(send_str, "insert into orders values(%ld,%ld,%ld,%ld,'%s',%ld,%ld, 1);", o_id, o_d_id, o_w_id, o_c_id, timestamp, o_carrier_id, o_ol_cnt);
			 send_command(global_sockfd, send_str);
 
		 }
 
		 if (option_debug)
			 printf("OID = %ld, CID = %ld, DID = %ld, WID = %ld\n",
					o_id, o_c_id, o_d_id, o_w_id);
 
		 for (ol = 1; ol <= o_ol_cnt; ol++) {
			 /* Generate Order Line Data */
			 ol_i_id = RandomNumber(1L, MAXITEMS);
			 ol_supply_w_id = o_w_id;
			 ol_quantity = 5;
			 ol_amount = 0.0;
 
			 ol_dist_info[ MakeAlphaString(24, 24, ol_dist_info) ] = 0;
 
			 tmp_float = (float) (RandomNumber(10L, 10000L)) / 100.0;
 
			 if (o_id > 2100) {
				 /*EXEC SQL INSERT INTO
								 order_line
								 values(:o_id,:o_d_id,:o_w_id,:ol,
								:ol_i_id,:ol_supply_w_id, NULL,
								:ol_quantity,:tmp_float,:ol_dist_info);*/
				 
				 
				 char send_str[1024];
				 sprintf(send_str, "INSERT INTO order_line values(%d, %d, %d, %d, %d, %d, 'NULL', %d, %f, '%s');", o_id, o_d_id, o_w_id, ol, ol_i_id, ol_supply_w_id, ol_quantity, tmp_float, ol_dist_info);
				 send_command(global_sockfd, send_str);
			 } else {
				 /*EXEC SQL INSERT INTO
					 order_line
					 values(:o_id,:o_d_id,:o_w_id,:ol,
						:ol_i_id,:ol_supply_w_id, 
						:timestamp,
						:ol_quantity,:ol_amount,:ol_dist_info);*/
 
 
				 // sqlite_stmt = stmt[10];
 
				 // sqlite3_bind_int64(sqlite_stmt, 1, o_id);
				 // sqlite3_bind_int64(sqlite_stmt, 2, o_d_id);
				 // sqlite3_bind_int64(sqlite_stmt, 3, o_w_id);
				 // sqlite3_bind_int64(sqlite_stmt, 4, ol);
				 // sqlite3_bind_int64(sqlite_stmt, 5, ol_i_id);
				 // sqlite3_bind_int64(sqlite_stmt, 6, ol_supply_w_id);
				 // sqlite3_bind_text(sqlite_stmt, 7, timestamp, -1, SQLITE_STATIC);
				 // sqlite3_bind_int64(sqlite_stmt, 8, ol_quantity);
				 // sqlite3_bind_double(sqlite_stmt, 9, ol_amount);
				 // sqlite3_bind_text(sqlite_stmt, 10, ol_dist_info, -1, SQLITE_STATIC);
 
				 // if (sqlite3_step(sqlite_stmt) != SQLITE_DONE) goto sqlerr;
				 // sqlite3_reset(sqlite_stmt);
				 
				 char send_str[1024];
				 sprintf(send_str, "INSERT INTO order_line values(%d, %d, %d, %d, %d, %d, '%s', %d, %f, '%s');", o_id, o_d_id, o_w_id, ol, ol_i_id, ol_supply_w_id, timestamp, ol_quantity, ol_amount, ol_dist_info);				
				 send_command(global_sockfd, send_str);
 
 
			 }
 
			 if (option_debug)
				 printf("OL = %ld, IID = %ld, QUAN = %ld, AMT = %8.2f\n",
						ol, ol_i_id, ol_quantity, ol_amount);
 
		 }
		 if (!(o_id % 100)) {
			 printf(".");
			 fflush(stdout);
 
			  if (!(o_id % 1000))
				 printf(" %ld\n", o_id);
		 }
	 }
	 /*EXEC SQL COMMIT WORK;*/
	 //if( sqlite3_exec(sqlite, "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;
 
	 printf("Orders Done.\n");
	 return;
 sqlerr:
	 Error();
	 printf( "Error in Orders\n");
 }
 
 
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      Error() | DESCRIPTION |      Handles an error from a
  * SQL call. | ARGUMENTS
  * +==================================================================
  */
 void 
 Error()
 {
	 // if(sqlite_stmt) {
	 //     //printf("\n%d, %s, %s", mysql_stmt_errno(mysql_stmt),
	 //     //mysql_stmt_sqlstate(mysql_stmt), mysql_stmt_error(mysql_stmt) );
	 //     printf("%s: sqlite error: %s\n", __func__, sqlite3_errmsg(sqlite));
	 // }
	 // //printf("\n%d, %s, %s\n", mysql_errno(mysql), mysql_sqlstate(mysql), mysql_error(mysql) );
	 // printf("%s: sqlite error: %s\n", __func__, sqlite3_errmsg(sqlite));

	 // /*EXEC SQL WHENEVER SQLERROR CONTINUE;*/

	 // /*EXEC SQL ROLLBACK WORK;*/
	 // sqlite3_exec(sqlite, "ROLLBACK;", NULL, NULL, NULL);

	 // /*EXEC SQL DISCONNECT;*/
	 // sqlite3_close(sqlite);
	 
	 // exit(-1);
	 
	 printf("Error occurred, rolling back transaction...\n");
	 
	 // 尝试回滚事务
	 if (global_sockfd >= 0) {
		 send_command(global_sockfd, "abort;");
		 
		 // 等待一下确保回滚完成
		 usleep(100000); // 等待100ms
		 
		 // 关闭socket连接
		 close(global_sockfd);
		 printf("Database connection closed after error.\n");
	 }
	 
	 exit(-1);
 }
 
 /*
  * ==================================================================+ |
  * ROUTINE NAME |      signal_handler | DESCRIPTION |      Handles signals for graceful shutdown |
  * ARGUMENTS |      signal number
  * +==================================================================
  */
 void 
 signal_handler(int sig)
 {
	 printf("\nReceived signal %d, cleaning up...\n", sig);
	 
	 // 尝试回滚事务并关闭连接
	 if (global_sockfd >= 0) {
		 printf("Rolling back transaction and closing connection...\n");
		 send_command(global_sockfd, "abort;");
		 
		 // 等待一下确保回滚完成
		 usleep(100000); // 等待100ms
		 
		 // 关闭socket连接
		 close(global_sockfd);
		 printf("Database connection closed after signal.\n");
	 }
	 
	 printf("Exiting gracefully...\n");
	 exit(0);
 }
 