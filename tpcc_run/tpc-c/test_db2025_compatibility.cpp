#include <iostream>
#include <string>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netdb.h>
#include <unistd.h>
#include <cstring>
#include <fstream>

#define MAX_MEM_BUFFER_SIZE 8192
#define PORT_DEFAULT 8765

// 简化的结果结构
struct TestResult {
    bool success;
    std::string message;
};

int init_tcp_sock(const char *server_host, int server_port) {
    struct hostent *host;
    struct sockaddr_in serv_addr;

    if ((host = gethostbyname(server_host)) == NULL) {
        fprintf(stderr, "gethostbyname failed. errmsg=%d:%s\n", errno, strerror(errno));
        return -1;
    }

    int sockfd;
    if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
        fprintf(stderr, "create socket error. errmsg=%d:%s\n", errno, strerror(errno));
        return -1;
    }

    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(server_port);
    serv_addr.sin_addr = *((struct in_addr *)host->h_addr);
    bzero(&(serv_addr.sin_zero), 8);

    if (connect(sockfd, (struct sockaddr *)&serv_addr, sizeof(struct sockaddr)) == -1) {
        fprintf(stderr, "Failed to connect. errmsg=%d:%s\n", errno, strerror(errno));
        close(sockfd);
        return -1;
    }
    return sockfd;
}

void send_command(int sockfd, const char* command, TestResult* result) {
    printf("Sending command: %s\n", command);
    
    // 发送SQL命令到db2025数据库
    if (write(sockfd, command, strlen(command) + 1) == -1) {
        printf("send error: %d:%s \n", errno, strerror(errno));
        result->success = false;
        result->message = "Send failed";
        return;
    }

    // 接收数据库响应
    char recv_buf[MAX_MEM_BUFFER_SIZE];
    memset(recv_buf, 0, MAX_MEM_BUFFER_SIZE);
    int len = recv(sockfd, recv_buf, MAX_MEM_BUFFER_SIZE, 0);
    
    printf("Received %d bytes\n", len);
    
    if (len < 0) {
        fprintf(stderr, "Connection was broken: %s\n", strerror(errno));
        result->success = false;
        result->message = "Connection broken";
        return;
    } else if (len == 0) {
        printf("Connection has been closed\n");
        result->success = false;
        result->message = "Connection closed";
        return;
    } else {
        // 处理db2025数据库的响应格式
        std::string response(recv_buf, len);
        
        printf("Raw response (%d bytes): ", len);
        for (int i = 0; i < len; i++) {
            if (recv_buf[i] >= 32 && recv_buf[i] <= 126) {
                printf("%c", recv_buf[i]);
            } else {
                printf("\\x%02x", (unsigned char)recv_buf[i]);
            }
        }
        printf("\n");
        
        // 检查是否成功
        if (response.find("Error") != std::string::npos || 
            response.find("error") != std::string::npos) {
            result->success = false;
            result->message = response;
            return;
        }
        
        // 成功响应
        result->success = true;
        result->message = response;
        printf("Command: %s\nResponse: %s\n", command, response.c_str());
    }
}

int main() {
    const char *server_host = "127.0.0.1";
    int server_port = PORT_DEFAULT;
    
    int sockfd = init_tcp_sock(server_host, server_port);
    if (sockfd < 0) {
        printf("Failed to connect to database server\n");
        return 1;
    }
    
    printf("Testing db2025 database compatibility...\n");
    
    TestResult result;
    
    // 测试基本SQL命令（使用系统支持的语法）
    send_command(sockfd, "show tables;", &result);
    if (!result.success) {
        printf("Failed to show tables: %s\n", result.message.c_str());
        close(sockfd);
        return 1;
    }
    
    send_command(sockfd, "create table test (id int, name char(20));", &result);
    if (!result.success) {
        printf("Failed to create table: %s\n", result.message.c_str());
        close(sockfd);
        return 1;
    }
    
    send_command(sockfd, "show tables;", &result);
    if (!result.success) {
        printf("Failed to show tables: %s\n", result.message.c_str());
        close(sockfd);
        return 1;
    }
    
    send_command(sockfd, "insert into test values (1, 'test1');", &result);
    if (!result.success) {
        printf("Failed to insert data: %s\n", result.message.c_str());
        close(sockfd);
        return 1;
    }
    
    send_command(sockfd, "select * from test;", &result);
    if (!result.success) {
        printf("Failed to select data: %s\n", result.message.c_str());
        close(sockfd);
        return 1;
    }
    
    send_command(sockfd, "drop table test;", &result);
    if (!result.success) {
        printf("Failed to drop table: %s\n", result.message.c_str());
        close(sockfd);
        return 1;
    }
    
    close(sockfd);
    printf("Test completed successfully!\n");
    return 0;
} 