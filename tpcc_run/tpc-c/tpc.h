/*
 * tpc.h
 * definitions for tpcc loading program && transactions
 */
#pragma once
#include <atomic>
#include <stdio.h>
#include <string>
#include <vector>
#include "pthread.h"
struct SqlResult {
    std::vector<std::string> field_names; // 字段名
    std::vector<std::vector<std::string>> field_values; // 字段值
    int field_count = 0; // 字段数
    int row_count = 0; // 记录数
    int sockfd;     // socket
    int success = 0; // 是否成功执行
    int is_abort = 0; // 是否回滚
    std::string rec_msg;
};

// typedef struct {
//     char **field_names; // 字段名
//     char ***field_values; // 字段值
//     int field_count; // 字段数
//     int row_count; // 记录数
//     int sockfd;     // socket
//     int success; //是否成功执行
//     int is_abort; //是否回滚
// } SqlResult;
/*
 * correct values
 */
#define MAXITEMS      100000 
#define CUST_PER_DIST 3000 
#define DIST_PER_WARE 10 
#define ORD_PER_DIST  3000


#define MAX_LINE_LENGTH 8192
#define MAX_FIELDS 21
#define MAX_RECORDS 200
#define OUTPUT_TO_FILE 0
/*
 */

//  * small values

// #define MAXITEMS 	1000
// #define CUST_PER_DIST 	30
// #define DIST_PER_WARE	3
// #define ORD_PER_DIST	30

//  */ 

/* definitions for new order transaction */
#define MAX_NUM_ITEMS 15
#define MAX_ITEM_LEN  24

#define swap_int(a,b) {int tmp; tmp=a; a=b; b=tmp;}

/*
 * hack MakeAddress() into a macro so that we can pass Oracle
 * VARCHARs instead of char *s
 */
#define MakeAddressMacro(str1,str2,city,state,zip) \
{int tmp; \
 tmp = MakeAlphaString(10,20,str1.arr); \
 str1.len = tmp; \
 tmp = MakeAlphaString(10,20,str2.arr); \
 str2.len = tmp; \
 tmp = MakeAlphaString(10,20,city.arr); \
 city.len = tmp; \
 tmp = MakeAlphaString(2,2,state.arr); \
 state.len = tmp; \
 tmp = MakeNumberString(9,9,zip.arr); \
 zip.len = tmp;}

/*
 * while we're at it, wrap MakeAlphaString() and MakeNumberString()
 * in a similar way
 */
#define MakeAlphaStringMacro(x,y,str) \
{int tmp; tmp = MakeAlphaString(x,y,str.arr); str.len = tmp;}
#define MakeNumberStringMacro(x,y,str) \
{int tmp; tmp = MakeNumberString(x,y,str.arr); str.len = tmp;}

/*
 * likewise, for Lastname()
 * counts on Lastname() producing null-terminated strings
 */
#define LastnameMacro(num,str) \
{Lastname(num, str.arr); str.len = strlen(str.arr);}

extern long count_ware;
  
/* Functions */
    
void         LoadItems();
void         LoadWare();   
void         LoadCust();
void         LoadOrd();
void         LoadNewOrd();   
int          Stock(int w_id);
int          District(int w_id);
void         Customer(int d_id, int w_id);
void         Orders(int d_id, int w_id);
void         New_Orders();
void         MakeAddress();
void         Error();
void ResetSqlResult(SqlResult* res);

#ifdef __STDC__
void SetSeed (int seed);
int RandomNumber (int min, int max);
int NURand (unsigned A, unsigned x, unsigned y);
int MakeAlphaString (int x, int y, char str[]);
int MakeNumberString (int x, int y, char str[]);
void gettimestamp (char str[], char *format, size_t n);
void InitPermutation (void);
int GetPermutation (void);
void Lastname(int num, char* name);

#endif /* __STDC__ */
    

