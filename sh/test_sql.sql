-- 自动生成的一致性检验SQL
-- 覆盖所有(w_id, d_id)组合，共50个仓库，每个仓库10个地区
create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);
create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);
create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));
create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(50), h_amount float, h_data char(24));
create table new_orders (no_o_id int, no_d_id int, no_w_id int);
create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(50), o_carrier_id int, o_ol_cnt int, o_all_local int);
create table order_line ( ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(30), ol_quantity int, ol_amount float, ol_dist_info char(24));
create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));
create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));
create index warehouse(w_id);
create index district(d_w_id, d_id);
create index customer(c_w_id, c_d_id, c_id);
create index history(h_c_id, h_c_d_id, h_c_w_id, h_d_id, h_w_id);
create index new_orders(no_w_id, no_d_id, no_o_id);
create index orders(o_w_id, o_d_id, o_id);
create index order_line(ol_w_id, ol_d_id, ol_o_id, ol_number);
create index item(i_id);
create index stock(s_w_id, s_i_id);
load ../src/test/performance_test/table_data/warehouse.csv into warehouse;
load ../src/test/performance_test/table_data/district.csv into district;
load ../src/test/performance_test/table_data/customer.csv into customer;
load ../src/test/performance_test/table_data/history.csv into history;
load ../src/test/performance_test/table_data/new_orders.csv into new_orders;
load ../src/test/performance_test/table_data/orders.csv into orders;
load ../src/test/performance_test/table_data/order_line.csv into order_line;
load ../src/test/performance_test/table_data/item.csv into item;
load ../src/test/performance_test/table_data/stock.csv into stock;
select COUNT(*) as count_warehouse from warehouse;
select COUNT(*) as count_district from district;
select COUNT(*) as count_customer from customer;
select COUNT(*) as count_history from history;
select COUNT(*) as count_new_orders from new_orders;
select COUNT(*) as count_orders from orders;
select COUNT(*) as count_order_line from order_line;
select COUNT(*) as count_item from item;
select COUNT(*) as count_stock from stock;
-- 检验组合: w_id=1, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=1;

-- 检验组合: w_id=1, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=2;

-- 检验组合: w_id=1, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=3;

-- 检验组合: w_id=1, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=4;

-- 检验组合: w_id=1, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=5;

-- 检验组合: w_id=1, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=6;

-- 检验组合: w_id=1, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=7;

-- 检验组合: w_id=1, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=8;

-- 检验组合: w_id=1, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=9;

-- 检验组合: w_id=1, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=1 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=1 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=1 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=1 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=1 AND ol_d_id=10;

-- 检验组合: w_id=2, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=1;

-- 检验组合: w_id=2, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=2;

-- 检验组合: w_id=2, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=3;

-- 检验组合: w_id=2, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=4;

-- 检验组合: w_id=2, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=5;

-- 检验组合: w_id=2, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=6;

-- 检验组合: w_id=2, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=7;

-- 检验组合: w_id=2, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=8;

-- 检验组合: w_id=2, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=9;

-- 检验组合: w_id=2, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=2 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=2 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=2 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=2 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=2 AND ol_d_id=10;

-- 检验组合: w_id=3, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=1;

-- 检验组合: w_id=3, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=2;

-- 检验组合: w_id=3, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=3;

-- 检验组合: w_id=3, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=4;

-- 检验组合: w_id=3, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=5;

-- 检验组合: w_id=3, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=6;

-- 检验组合: w_id=3, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=7;

-- 检验组合: w_id=3, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=8;

-- 检验组合: w_id=3, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=9;

-- 检验组合: w_id=3, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=3 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=3 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=3 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=3 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=3 AND ol_d_id=10;

-- 检验组合: w_id=4, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=1;

-- 检验组合: w_id=4, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=2;

-- 检验组合: w_id=4, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=3;

-- 检验组合: w_id=4, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=4;

-- 检验组合: w_id=4, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=5;

-- 检验组合: w_id=4, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=6;

-- 检验组合: w_id=4, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=7;

-- 检验组合: w_id=4, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=8;

-- 检验组合: w_id=4, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=9;

-- 检验组合: w_id=4, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=4 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=4 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=4 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=4 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=4 AND ol_d_id=10;

-- 检验组合: w_id=5, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=1;

-- 检验组合: w_id=5, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=2;

-- 检验组合: w_id=5, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=3;

-- 检验组合: w_id=5, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=4;

-- 检验组合: w_id=5, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=5;

-- 检验组合: w_id=5, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=6;

-- 检验组合: w_id=5, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=7;

-- 检验组合: w_id=5, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=8;

-- 检验组合: w_id=5, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=9;

-- 检验组合: w_id=5, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=5 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=5 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=5 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=5 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=5 AND ol_d_id=10;

-- 检验组合: w_id=6, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=1;

-- 检验组合: w_id=6, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=2;

-- 检验组合: w_id=6, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=3;

-- 检验组合: w_id=6, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=4;

-- 检验组合: w_id=6, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=5;

-- 检验组合: w_id=6, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=6;

-- 检验组合: w_id=6, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=7;

-- 检验组合: w_id=6, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=8;

-- 检验组合: w_id=6, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=9;

-- 检验组合: w_id=6, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=6 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=6 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=6 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=6 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=6 AND ol_d_id=10;

-- 检验组合: w_id=7, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=1;

-- 检验组合: w_id=7, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=2;

-- 检验组合: w_id=7, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=3;

-- 检验组合: w_id=7, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=4;

-- 检验组合: w_id=7, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=5;

-- 检验组合: w_id=7, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=6;

-- 检验组合: w_id=7, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=7;

-- 检验组合: w_id=7, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=8;

-- 检验组合: w_id=7, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=9;

-- 检验组合: w_id=7, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=7 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=7 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=7 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=7 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=7 AND ol_d_id=10;

-- 检验组合: w_id=8, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=1;

-- 检验组合: w_id=8, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=2;

-- 检验组合: w_id=8, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=3;

-- 检验组合: w_id=8, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=4;

-- 检验组合: w_id=8, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=5;

-- 检验组合: w_id=8, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=6;

-- 检验组合: w_id=8, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=7;

-- 检验组合: w_id=8, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=8;

-- 检验组合: w_id=8, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=9;

-- 检验组合: w_id=8, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=8 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=8 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=8 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=8 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=8 AND ol_d_id=10;

-- 检验组合: w_id=9, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=1;

-- 检验组合: w_id=9, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=2;

-- 检验组合: w_id=9, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=3;

-- 检验组合: w_id=9, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=4;

-- 检验组合: w_id=9, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=5;

-- 检验组合: w_id=9, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=6;

-- 检验组合: w_id=9, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=7;

-- 检验组合: w_id=9, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=8;

-- 检验组合: w_id=9, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=9;

-- 检验组合: w_id=9, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=9 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=9 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=9 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=9 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=9 AND ol_d_id=10;

-- 检验组合: w_id=10, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=1;

-- 检验组合: w_id=10, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=2;

-- 检验组合: w_id=10, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=3;

-- 检验组合: w_id=10, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=4;

-- 检验组合: w_id=10, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=5;

-- 检验组合: w_id=10, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=6;

-- 检验组合: w_id=10, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=7;

-- 检验组合: w_id=10, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=8;

-- 检验组合: w_id=10, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=9;

-- 检验组合: w_id=10, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=10 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=10 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=10 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=10 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=10 AND ol_d_id=10;

-- 检验组合: w_id=11, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=1;

-- 检验组合: w_id=11, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=2;

-- 检验组合: w_id=11, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=3;

-- 检验组合: w_id=11, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=4;

-- 检验组合: w_id=11, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=5;

-- 检验组合: w_id=11, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=6;

-- 检验组合: w_id=11, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=7;

-- 检验组合: w_id=11, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=8;

-- 检验组合: w_id=11, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=9;

-- 检验组合: w_id=11, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=11 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=11 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=11 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=11 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=11 AND ol_d_id=10;

-- 检验组合: w_id=12, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=1;

-- 检验组合: w_id=12, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=2;

-- 检验组合: w_id=12, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=3;

-- 检验组合: w_id=12, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=4;

-- 检验组合: w_id=12, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=5;

-- 检验组合: w_id=12, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=6;

-- 检验组合: w_id=12, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=7;

-- 检验组合: w_id=12, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=8;

-- 检验组合: w_id=12, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=9;

-- 检验组合: w_id=12, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=12 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=12 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=12 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=12 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=12 AND ol_d_id=10;

-- 检验组合: w_id=13, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=1;

-- 检验组合: w_id=13, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=2;

-- 检验组合: w_id=13, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=3;

-- 检验组合: w_id=13, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=4;

-- 检验组合: w_id=13, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=5;

-- 检验组合: w_id=13, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=6;

-- 检验组合: w_id=13, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=7;

-- 检验组合: w_id=13, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=8;

-- 检验组合: w_id=13, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=9;

-- 检验组合: w_id=13, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=13 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=13 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=13 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=13 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=13 AND ol_d_id=10;

-- 检验组合: w_id=14, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=1;

-- 检验组合: w_id=14, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=2;

-- 检验组合: w_id=14, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=3;

-- 检验组合: w_id=14, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=4;

-- 检验组合: w_id=14, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=5;

-- 检验组合: w_id=14, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=6;

-- 检验组合: w_id=14, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=7;

-- 检验组合: w_id=14, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=8;

-- 检验组合: w_id=14, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=9;

-- 检验组合: w_id=14, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=14 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=14 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=14 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=14 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=14 AND ol_d_id=10;

-- 检验组合: w_id=15, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=1;

-- 检验组合: w_id=15, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=2;

-- 检验组合: w_id=15, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=3;

-- 检验组合: w_id=15, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=4;

-- 检验组合: w_id=15, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=5;

-- 检验组合: w_id=15, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=6;

-- 检验组合: w_id=15, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=7;

-- 检验组合: w_id=15, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=8;

-- 检验组合: w_id=15, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=9;

-- 检验组合: w_id=15, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=15 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=15 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=15 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=15 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=15 AND ol_d_id=10;

-- 检验组合: w_id=16, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=1;

-- 检验组合: w_id=16, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=2;

-- 检验组合: w_id=16, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=3;

-- 检验组合: w_id=16, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=4;

-- 检验组合: w_id=16, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=5;

-- 检验组合: w_id=16, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=6;

-- 检验组合: w_id=16, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=7;

-- 检验组合: w_id=16, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=8;

-- 检验组合: w_id=16, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=9;

-- 检验组合: w_id=16, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=16 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=16 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=16 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=16 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=16 AND ol_d_id=10;

-- 检验组合: w_id=17, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=1;

-- 检验组合: w_id=17, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=2;

-- 检验组合: w_id=17, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=3;

-- 检验组合: w_id=17, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=4;

-- 检验组合: w_id=17, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=5;

-- 检验组合: w_id=17, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=6;

-- 检验组合: w_id=17, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=7;

-- 检验组合: w_id=17, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=8;

-- 检验组合: w_id=17, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=9;

-- 检验组合: w_id=17, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=17 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=17 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=17 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=17 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=17 AND ol_d_id=10;

-- 检验组合: w_id=18, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=1;

-- 检验组合: w_id=18, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=2;

-- 检验组合: w_id=18, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=3;

-- 检验组合: w_id=18, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=4;

-- 检验组合: w_id=18, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=5;

-- 检验组合: w_id=18, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=6;

-- 检验组合: w_id=18, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=7;

-- 检验组合: w_id=18, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=8;

-- 检验组合: w_id=18, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=9;

-- 检验组合: w_id=18, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=18 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=18 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=18 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=18 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=18 AND ol_d_id=10;

-- 检验组合: w_id=19, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=1;

-- 检验组合: w_id=19, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=2;

-- 检验组合: w_id=19, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=3;

-- 检验组合: w_id=19, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=4;

-- 检验组合: w_id=19, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=5;

-- 检验组合: w_id=19, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=6;

-- 检验组合: w_id=19, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=7;

-- 检验组合: w_id=19, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=8;

-- 检验组合: w_id=19, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=9;

-- 检验组合: w_id=19, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=19 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=19 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=19 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=19 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=19 AND ol_d_id=10;

-- 检验组合: w_id=20, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=1;

-- 检验组合: w_id=20, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=2;

-- 检验组合: w_id=20, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=3;

-- 检验组合: w_id=20, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=4;

-- 检验组合: w_id=20, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=5;

-- 检验组合: w_id=20, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=6;

-- 检验组合: w_id=20, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=7;

-- 检验组合: w_id=20, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=8;

-- 检验组合: w_id=20, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=9;

-- 检验组合: w_id=20, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=20 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=20 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=20 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=20 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=20 AND ol_d_id=10;

-- 检验组合: w_id=21, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=1;

-- 检验组合: w_id=21, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=2;

-- 检验组合: w_id=21, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=3;

-- 检验组合: w_id=21, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=4;

-- 检验组合: w_id=21, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=5;

-- 检验组合: w_id=21, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=6;

-- 检验组合: w_id=21, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=7;

-- 检验组合: w_id=21, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=8;

-- 检验组合: w_id=21, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=9;

-- 检验组合: w_id=21, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=21 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=21 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=21 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=21 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=21 AND ol_d_id=10;

-- 检验组合: w_id=22, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=1;

-- 检验组合: w_id=22, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=2;

-- 检验组合: w_id=22, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=3;

-- 检验组合: w_id=22, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=4;

-- 检验组合: w_id=22, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=5;

-- 检验组合: w_id=22, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=6;

-- 检验组合: w_id=22, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=7;

-- 检验组合: w_id=22, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=8;

-- 检验组合: w_id=22, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=9;

-- 检验组合: w_id=22, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=22 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=22 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=22 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=22 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=22 AND ol_d_id=10;

-- 检验组合: w_id=23, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=1;

-- 检验组合: w_id=23, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=2;

-- 检验组合: w_id=23, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=3;

-- 检验组合: w_id=23, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=4;

-- 检验组合: w_id=23, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=5;

-- 检验组合: w_id=23, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=6;

-- 检验组合: w_id=23, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=7;

-- 检验组合: w_id=23, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=8;

-- 检验组合: w_id=23, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=9;

-- 检验组合: w_id=23, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=23 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=23 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=23 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=23 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=23 AND ol_d_id=10;

-- 检验组合: w_id=24, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=1;

-- 检验组合: w_id=24, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=2;

-- 检验组合: w_id=24, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=3;

-- 检验组合: w_id=24, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=4;

-- 检验组合: w_id=24, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=5;

-- 检验组合: w_id=24, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=6;

-- 检验组合: w_id=24, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=7;

-- 检验组合: w_id=24, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=8;

-- 检验组合: w_id=24, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=9;

-- 检验组合: w_id=24, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=24 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=24 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=24 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=24 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=24 AND ol_d_id=10;

-- 检验组合: w_id=25, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=1;

-- 检验组合: w_id=25, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=2;

-- 检验组合: w_id=25, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=3;

-- 检验组合: w_id=25, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=4;

-- 检验组合: w_id=25, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=5;

-- 检验组合: w_id=25, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=6;

-- 检验组合: w_id=25, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=7;

-- 检验组合: w_id=25, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=8;

-- 检验组合: w_id=25, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=9;

-- 检验组合: w_id=25, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=25 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=25 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=25 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=25 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=25 AND ol_d_id=10;

-- 检验组合: w_id=26, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=1;

-- 检验组合: w_id=26, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=2;

-- 检验组合: w_id=26, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=3;

-- 检验组合: w_id=26, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=4;

-- 检验组合: w_id=26, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=5;

-- 检验组合: w_id=26, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=6;

-- 检验组合: w_id=26, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=7;

-- 检验组合: w_id=26, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=8;

-- 检验组合: w_id=26, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=9;

-- 检验组合: w_id=26, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=26 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=26 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=26 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=26 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=26 AND ol_d_id=10;

-- 检验组合: w_id=27, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=1;

-- 检验组合: w_id=27, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=2;

-- 检验组合: w_id=27, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=3;

-- 检验组合: w_id=27, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=4;

-- 检验组合: w_id=27, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=5;

-- 检验组合: w_id=27, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=6;

-- 检验组合: w_id=27, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=7;

-- 检验组合: w_id=27, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=8;

-- 检验组合: w_id=27, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=9;

-- 检验组合: w_id=27, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=27 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=27 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=27 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=27 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=27 AND ol_d_id=10;

-- 检验组合: w_id=28, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=1;

-- 检验组合: w_id=28, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=2;

-- 检验组合: w_id=28, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=3;

-- 检验组合: w_id=28, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=4;

-- 检验组合: w_id=28, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=5;

-- 检验组合: w_id=28, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=6;

-- 检验组合: w_id=28, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=7;

-- 检验组合: w_id=28, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=8;

-- 检验组合: w_id=28, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=9;

-- 检验组合: w_id=28, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=28 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=28 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=28 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=28 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=28 AND ol_d_id=10;

-- 检验组合: w_id=29, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=1;

-- 检验组合: w_id=29, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=2;

-- 检验组合: w_id=29, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=3;

-- 检验组合: w_id=29, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=4;

-- 检验组合: w_id=29, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=5;

-- 检验组合: w_id=29, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=6;

-- 检验组合: w_id=29, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=7;

-- 检验组合: w_id=29, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=8;

-- 检验组合: w_id=29, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=9;

-- 检验组合: w_id=29, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=29 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=29 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=29 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=29 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=29 AND ol_d_id=10;

-- 检验组合: w_id=30, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=1;

-- 检验组合: w_id=30, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=2;

-- 检验组合: w_id=30, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=3;

-- 检验组合: w_id=30, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=4;

-- 检验组合: w_id=30, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=5;

-- 检验组合: w_id=30, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=6;

-- 检验组合: w_id=30, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=7;

-- 检验组合: w_id=30, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=8;

-- 检验组合: w_id=30, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=9;

-- 检验组合: w_id=30, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=30 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=30 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=30 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=30 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=30 AND ol_d_id=10;

-- 检验组合: w_id=31, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=1;

-- 检验组合: w_id=31, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=2;

-- 检验组合: w_id=31, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=3;

-- 检验组合: w_id=31, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=4;

-- 检验组合: w_id=31, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=5;

-- 检验组合: w_id=31, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=6;

-- 检验组合: w_id=31, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=7;

-- 检验组合: w_id=31, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=8;

-- 检验组合: w_id=31, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=9;

-- 检验组合: w_id=31, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=31 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=31 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=31 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=31 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=31 AND ol_d_id=10;

-- 检验组合: w_id=32, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=1;

-- 检验组合: w_id=32, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=2;

-- 检验组合: w_id=32, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=3;

-- 检验组合: w_id=32, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=4;

-- 检验组合: w_id=32, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=5;

-- 检验组合: w_id=32, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=6;

-- 检验组合: w_id=32, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=7;

-- 检验组合: w_id=32, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=8;

-- 检验组合: w_id=32, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=9;

-- 检验组合: w_id=32, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=32 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=32 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=32 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=32 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=32 AND ol_d_id=10;

-- 检验组合: w_id=33, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=1;

-- 检验组合: w_id=33, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=2;

-- 检验组合: w_id=33, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=3;

-- 检验组合: w_id=33, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=4;

-- 检验组合: w_id=33, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=5;

-- 检验组合: w_id=33, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=6;

-- 检验组合: w_id=33, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=7;

-- 检验组合: w_id=33, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=8;

-- 检验组合: w_id=33, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=9;

-- 检验组合: w_id=33, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=33 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=33 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=33 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=33 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=33 AND ol_d_id=10;

-- 检验组合: w_id=34, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=1;

-- 检验组合: w_id=34, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=2;

-- 检验组合: w_id=34, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=3;

-- 检验组合: w_id=34, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=4;

-- 检验组合: w_id=34, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=5;

-- 检验组合: w_id=34, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=6;

-- 检验组合: w_id=34, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=7;

-- 检验组合: w_id=34, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=8;

-- 检验组合: w_id=34, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=9;

-- 检验组合: w_id=34, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=34 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=34 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=34 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=34 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=34 AND ol_d_id=10;

-- 检验组合: w_id=35, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=1;

-- 检验组合: w_id=35, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=2;

-- 检验组合: w_id=35, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=3;

-- 检验组合: w_id=35, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=4;

-- 检验组合: w_id=35, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=5;

-- 检验组合: w_id=35, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=6;

-- 检验组合: w_id=35, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=7;

-- 检验组合: w_id=35, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=8;

-- 检验组合: w_id=35, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=9;

-- 检验组合: w_id=35, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=35 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=35 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=35 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=35 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=35 AND ol_d_id=10;

-- 检验组合: w_id=36, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=1;

-- 检验组合: w_id=36, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=2;

-- 检验组合: w_id=36, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=3;

-- 检验组合: w_id=36, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=4;

-- 检验组合: w_id=36, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=5;

-- 检验组合: w_id=36, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=6;

-- 检验组合: w_id=36, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=7;

-- 检验组合: w_id=36, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=8;

-- 检验组合: w_id=36, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=9;

-- 检验组合: w_id=36, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=36 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=36 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=36 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=36 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=36 AND ol_d_id=10;

-- 检验组合: w_id=37, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=1;

-- 检验组合: w_id=37, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=2;

-- 检验组合: w_id=37, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=3;

-- 检验组合: w_id=37, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=4;

-- 检验组合: w_id=37, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=5;

-- 检验组合: w_id=37, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=6;

-- 检验组合: w_id=37, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=7;

-- 检验组合: w_id=37, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=8;

-- 检验组合: w_id=37, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=9;

-- 检验组合: w_id=37, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=37 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=37 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=37 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=37 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=37 AND ol_d_id=10;

-- 检验组合: w_id=38, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=1;

-- 检验组合: w_id=38, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=2;

-- 检验组合: w_id=38, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=3;

-- 检验组合: w_id=38, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=4;

-- 检验组合: w_id=38, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=5;

-- 检验组合: w_id=38, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=6;

-- 检验组合: w_id=38, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=7;

-- 检验组合: w_id=38, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=8;

-- 检验组合: w_id=38, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=9;

-- 检验组合: w_id=38, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=38 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=38 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=38 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=38 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=38 AND ol_d_id=10;

-- 检验组合: w_id=39, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=1;

-- 检验组合: w_id=39, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=2;

-- 检验组合: w_id=39, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=3;

-- 检验组合: w_id=39, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=4;

-- 检验组合: w_id=39, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=5;

-- 检验组合: w_id=39, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=6;

-- 检验组合: w_id=39, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=7;

-- 检验组合: w_id=39, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=8;

-- 检验组合: w_id=39, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=9;

-- 检验组合: w_id=39, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=39 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=39 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=39 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=39 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=39 AND ol_d_id=10;

-- 检验组合: w_id=40, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=1;

-- 检验组合: w_id=40, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=2;

-- 检验组合: w_id=40, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=3;

-- 检验组合: w_id=40, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=4;

-- 检验组合: w_id=40, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=5;

-- 检验组合: w_id=40, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=6;

-- 检验组合: w_id=40, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=7;

-- 检验组合: w_id=40, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=8;

-- 检验组合: w_id=40, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=9;

-- 检验组合: w_id=40, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=40 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=40 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=40 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=40 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=40 AND ol_d_id=10;

-- 检验组合: w_id=41, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=1;

-- 检验组合: w_id=41, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=2;

-- 检验组合: w_id=41, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=3;

-- 检验组合: w_id=41, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=4;

-- 检验组合: w_id=41, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=5;

-- 检验组合: w_id=41, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=6;

-- 检验组合: w_id=41, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=7;

-- 检验组合: w_id=41, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=8;

-- 检验组合: w_id=41, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=9;

-- 检验组合: w_id=41, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=41 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=41 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=41 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=41 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=41 AND ol_d_id=10;

-- 检验组合: w_id=42, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=1;

-- 检验组合: w_id=42, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=2;

-- 检验组合: w_id=42, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=3;

-- 检验组合: w_id=42, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=4;

-- 检验组合: w_id=42, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=5;

-- 检验组合: w_id=42, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=6;

-- 检验组合: w_id=42, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=7;

-- 检验组合: w_id=42, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=8;

-- 检验组合: w_id=42, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=9;

-- 检验组合: w_id=42, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=42 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=42 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=42 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=42 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=42 AND ol_d_id=10;

-- 检验组合: w_id=43, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=1;

-- 检验组合: w_id=43, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=2;

-- 检验组合: w_id=43, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=3;

-- 检验组合: w_id=43, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=4;

-- 检验组合: w_id=43, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=5;

-- 检验组合: w_id=43, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=6;

-- 检验组合: w_id=43, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=7;

-- 检验组合: w_id=43, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=8;

-- 检验组合: w_id=43, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=9;

-- 检验组合: w_id=43, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=43 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=43 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=43 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=43 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=43 AND ol_d_id=10;

-- 检验组合: w_id=44, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=1;

-- 检验组合: w_id=44, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=2;

-- 检验组合: w_id=44, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=3;

-- 检验组合: w_id=44, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=4;

-- 检验组合: w_id=44, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=5;

-- 检验组合: w_id=44, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=6;

-- 检验组合: w_id=44, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=7;

-- 检验组合: w_id=44, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=8;

-- 检验组合: w_id=44, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=9;

-- 检验组合: w_id=44, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=44 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=44 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=44 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=44 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=44 AND ol_d_id=10;

-- 检验组合: w_id=45, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=1;

-- 检验组合: w_id=45, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=2;

-- 检验组合: w_id=45, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=3;

-- 检验组合: w_id=45, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=4;

-- 检验组合: w_id=45, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=5;

-- 检验组合: w_id=45, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=6;

-- 检验组合: w_id=45, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=7;

-- 检验组合: w_id=45, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=8;

-- 检验组合: w_id=45, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=9;

-- 检验组合: w_id=45, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=45 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=45 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=45 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=45 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=45 AND ol_d_id=10;

-- 检验组合: w_id=46, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=1;

-- 检验组合: w_id=46, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=2;

-- 检验组合: w_id=46, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=3;

-- 检验组合: w_id=46, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=4;

-- 检验组合: w_id=46, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=5;

-- 检验组合: w_id=46, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=6;

-- 检验组合: w_id=46, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=7;

-- 检验组合: w_id=46, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=8;

-- 检验组合: w_id=46, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=9;

-- 检验组合: w_id=46, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=46 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=46 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=46 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=46 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=46 AND ol_d_id=10;

-- 检验组合: w_id=47, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=1;

-- 检验组合: w_id=47, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=2;

-- 检验组合: w_id=47, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=3;

-- 检验组合: w_id=47, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=4;

-- 检验组合: w_id=47, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=5;

-- 检验组合: w_id=47, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=6;

-- 检验组合: w_id=47, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=7;

-- 检验组合: w_id=47, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=8;

-- 检验组合: w_id=47, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=9;

-- 检验组合: w_id=47, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=47 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=47 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=47 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=47 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=47 AND ol_d_id=10;

-- 检验组合: w_id=48, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=1;

-- 检验组合: w_id=48, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=2;

-- 检验组合: w_id=48, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=3;

-- 检验组合: w_id=48, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=4;

-- 检验组合: w_id=48, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=5;

-- 检验组合: w_id=48, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=6;

-- 检验组合: w_id=48, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=7;

-- 检验组合: w_id=48, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=8;

-- 检验组合: w_id=48, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=9;

-- 检验组合: w_id=48, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=48 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=48 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=48 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=48 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=48 AND ol_d_id=10;

-- 检验组合: w_id=49, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=1;

-- 检验组合: w_id=49, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=2;

-- 检验组合: w_id=49, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=3;

-- 检验组合: w_id=49, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=4;

-- 检验组合: w_id=49, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=5;

-- 检验组合: w_id=49, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=6;

-- 检验组合: w_id=49, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=7;

-- 检验组合: w_id=49, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=8;

-- 检验组合: w_id=49, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=9;

-- 检验组合: w_id=49, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=49 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=49 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=49 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=49 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=49 AND ol_d_id=10;

-- 检验组合: w_id=50, d_id=1
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=1;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=1;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=1;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=1;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=1;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=1;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=1;

-- 检验组合: w_id=50, d_id=2
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=2;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=2;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=2;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=2;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=2;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=2;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=2;

-- 检验组合: w_id=50, d_id=3
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=3;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=3;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=3;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=3;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=3;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=3;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=3;

-- 检验组合: w_id=50, d_id=4
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=4;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=4;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=4;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=4;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=4;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=4;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=4;

-- 检验组合: w_id=50, d_id=5
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=5;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=5;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=5;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=5;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=5;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=5;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=5;

-- 检验组合: w_id=50, d_id=6
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=6;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=6;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=6;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=6;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=6;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=6;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=6;

-- 检验组合: w_id=50, d_id=7
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=7;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=7;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=7;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=7;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=7;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=7;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=7;

-- 检验组合: w_id=50, d_id=8
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=8;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=8;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=8;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=8;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=8;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=8;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=8;

-- 检验组合: w_id=50, d_id=9
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=9;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=9;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=9;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=9;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=9;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=9;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=9;

-- 检验组合: w_id=50, d_id=10
SELECT d_next_o_id FROM district WHERE d_w_id=50 AND d_id=10;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id=50 AND o_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=10;
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=10;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=10;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id=50 AND no_d_id=10;
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id=50 AND o_d_id=10;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id=50 AND ol_d_id=10;

