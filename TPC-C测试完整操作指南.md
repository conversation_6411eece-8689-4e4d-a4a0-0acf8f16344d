# TPC-C测试完整操作指南

## 概述

本指南基于实际操作演示，详细说明如何编译和运行TPC-C性能测试程序。

## 前提条件

确保您在项目根目录：`/home/<USER>/db2025/db2025-x1`

## 步骤1：编译主数据库服务器

按照`编译操作 (1).md`中的步骤：

```bash
# 如果build目录不存在，创建它
mkdir -p build
cd build

# 配置和编译
cmake .. -DCMAKE_BUILD_TYPE=Debug
make rmdb

# 验证编译结果
ls -la bin/rmdb
```

**预期结果**：应该看到`bin/rmdb`可执行文件（约21MB）

## 步骤2：编译TPC-C测试程序

### 2.1 修复CMakeLists.txt链接问题

TPC-C的CMakeLists.txt缺少库链接配置，需要修复：

```bash
cd tpcc_run/tpc-c
```

编辑`CMakeLists.txt`文件，在文件末尾添加：

```cmake
# 链接库到可执行文件
target_link_libraries(tpcc_load ${LIBS})
target_link_libraries(tpcc_start ${LIBS})
target_link_libraries(test_db2025_compatibility ${LIBS})
target_link_libraries(simple_consistency_check ${LIBS})
```

### 2.2 编译TPC-C程序

```bash
# 清理并重新创建build目录
rm -rf build
mkdir build
cd build

# 配置和编译
cmake .. -DCMAKE_BUILD_TYPE=Debug
make -j$(nproc)
```

**预期结果**：应该生成以下可执行文件：
- `tpcc_load` (476KB) - 数据加载程序
- `tpcc_start` (1.6MB) - 性能测试程序
- `test_db2025_compatibility` (267KB) - 兼容性测试
- `simple_consistency_check` (635KB) - 一致性检查

### 2.3 验证编译结果

```bash
ls -la *.exe 2>/dev/null || ls -la tpcc_*
```

## 步骤3：启动数据库服务器

在一个终端中启动数据库服务器：

```bash
cd /home/<USER>/db2025/db2025-x1/build
./bin/rmdb tpcc_testdb
```

**预期输出**：
```
  _____  __  __ _____  ____  
 |  __ \|  \/  |  __ \|  _ \ 
 | |__) | \  / | |  | | |_) |
 |  _  /| |\/| | |  | |  _ < 
 | | \ \| |  | | |__| | |_) |
 |_|  \_\_|  |_|_____/|____/ 

Welcome to RMDB!
Type 'help;' for help.

Waiting for new connection...
```

**重要**：保持这个终端运行，不要关闭！

## 步骤4：测试数据库连接

在另一个终端中：

```bash
cd /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build
./test_db2025_compatibility
```

**预期输出**：
```
Testing db2025 database compatibility...
Sending command: show tables;
...
Test completed successfully!
```

如果测试失败，检查：
1. 数据库服务器是否正在运行
2. 端口8765是否被占用
3. 防火墙设置

## 步骤5：运行数据加载程序

**注意**：完整的数据加载需要很长时间（可能30分钟以上），建议先了解过程：

```bash
./tpcc_load
```

**数据加载过程**：
1. **Item表**: 100,000条记录
2. **Warehouse表**: 50个仓库
3. **Stock表**: 每个仓库100,000条记录
4. **District表**: 每个仓库10个区域
5. **Customer表**: 每个区域3,000个客户
6. **Orders表**: 每个区域3,000个订单
7. **Order_Line表**: 订单明细
8. **History表**: 历史记录
9. **New_Orders表**: 新订单

**预期输出示例**：
```
*************************************
*** TPCC-sqlite3 Data Loader        ***
*************************************
TPCC Data Load Started...
Loading Item 
.................................................. 5000
.................................................. 10000
...
Item Done. 
Loading Warehouse 
Loading Stock Wid=1
...
```

**如果需要中断**：使用Ctrl+C停止加载

## 步骤6：运行性能测试程序

数据加载完成后，运行性能测试：

```bash
./tpcc_start
```

**测试配置**（在start.cpp中定义）：
- 仓库数量：50
- 并发连接：16
- 预热时间：2秒
- 测量时间：10秒
- 每线程事务数：500

**预期输出**：
```
***************************************
*** ###easy### TPC-C Load Generator ***
***************************************

<Parameters>
  [warehouse]: 50
 [connection]: 16
     [rampup]: 2 (sec.)
    [measure]: 10 (sec.)

RAMP-UP TIME.(2 sec.)
MEASURING START.

<Raw Results>
  [0] sc:45  lt:0  rt:0  fl:0 avg_rt: 12.3 (5)
  [1] sc:43  lt:0  rt:0  fl:0 avg_rt: 15.2 (5)
  ...

<TpmC>
                 324.567 TpmC
```

## 步骤7：数据一致性检查

测试完成后，运行一致性检查：

```bash
./simple_consistency_check
```

## 常见问题与解决方案

### 问题1：编译错误 - pthread链接失败

**错误信息**：
```
undefined reference to `pthread_create'
```

**解决方案**：
确保CMakeLists.txt中添加了`target_link_libraries`配置

### 问题2：连接数据库失败

**错误信息**：
```
Failed to connect to database server
```

**解决方案**：
1. 确认数据库服务器正在运行
2. 检查端口8765是否可用：`netstat -tlnp | grep 8765`
3. 检查防火墙设置

### 问题3：数据加载时间过长

**解决方案**：
1. 可以修改load.cpp中的仓库数量（默认50个）
2. 或者使用已有的测试数据
3. 考虑使用SSD存储提高I/O性能

### 问题4：性能测试失败

**可能原因**：
1. 数据未完全加载
2. 数据库表结构不正确
3. 并发连接数过高导致死锁

**解决方案**：
1. 确保数据加载完成
2. 检查数据库日志
3. 减少并发连接数

## 性能调优建议

### 1. 数据库配置优化

在`src/common/config.h`中：
```cpp
#define BUFFER_POOL_SIZE 262144  // 1GB缓冲池
#define MAX_CONN_LIMIT 16        // 最大连接数
```

### 2. 测试参数调优

在`start.cpp`中：
```cpp
num_ware = 10;         // 减少仓库数量
num_conn = 8;          // 减少并发连接数
measure_time = 60;     // 增加测量时间
```

### 3. 系统级优化

```bash
# 调整系统参数
echo 'vm.swappiness=1' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_ratio=15' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 结果解读

### TpmC指标

TpmC (Transactions per minute - C) 是TPC-C的核心性能指标：
- 表示每分钟处理的New Order事务数
- 数值越高表示性能越好
- 需要同时满足响应时间要求（90%事务在规定时间内完成）

### 响应时间要求

- New Order: ≤ 30秒
- Payment: ≤ 30秒  
- Order Status: ≤ 30秒
- Delivery: ≤ 120秒
- Stock Level: ≤ 60秒

## 总结

通过以上步骤，您应该能够：
1. 成功编译所有TPC-C测试程序
2. 启动数据库服务器并建立连接
3. 加载TPC-C标准测试数据
4. 运行性能测试并获得TpmC结果
5. 验证数据一致性

这个完整的测试流程将帮助您评估RMDB数据库系统的性能表现，为决赛做好准备。
