# 2024 年全国大学生计算机系统能力大赛 —— 数据库管理系统设计赛

汇报队伍：对什么队

队伍成员：甘仰发 曾庆礼 王旭升

指导老师：张利军

## 目录 / CONTENT

0 1 系统概述

0 2 实现重点

0 3 性能优化

0 4 总结思考

## Part.01 系统概述

### 项目框架

RMDB 系统框架图（引自比赛官网文档）

RMDB 服务端通过分层设计，包含了 SQL 解析、逻辑计划、物理计划、算子执行等过程，并且将存储管理、索引、事务管理、日志管理等功能模块化，便于维护和扩展。每个模块之间通过明确的接口进行交互，确保系统的高效性和可靠性。

### 开发计划



1.  **初赛**：分析赛题，根据赛题依赖关系分配任务，各成员独立开发并讨论交流

2.  **决赛前期**：主要负责完成初赛遗留题目；实现决赛要求附加功能

3.  **决赛后期**：主要进行性能优化；配合完成辅助工作；整理文档

## Part.02 实现重点

### 归并连接：外部排序



*   **前提**：两个输入关系在连接的字段上必须是有序的。如果输入关系没有排序，首先需要对它们进行排序。

*   **外部排序**：

1.  **分块处理**：数据通常被分成多个块（或页面），每个块在内存中使用任何适合内存的排序算法（快速排序、归并排序等）进行处理。

2.  **多路归并**：将所有已排序的文件进行归并，使用堆来实现多路归并，确保每次从多个块中读取最小（或最大）元素

### 不相关子查询



*   **定义**：子查询是嵌套在另一个 SQL 查询中的查询，通常用于外部查询的 WHERE 子句；不相关指子查询与主查询无关，可先于主查询执行获取结果。

*   **特点**：

1.  子查询解析（parser）：增加词法和语法解析功能，识别谓词中嵌入的子查询

2.  执行子查询（analyze）：子查询独立执行，生成结果

3.  结果返回（execution）：将子查询的结果返回谓词，最终应用在主查询中

*   **过程**：

    开始 → 解析主查询语句 → 判断是否存在子查询


    *   否 → 执行主查询 → 结束

    *   是 → 识别子查询 → 独立执行子查询 → 生成子查询结果 → 嵌入主查询 → 执行主查询 → 结束

### 间隙锁



*   **引入原因**：


    *   表级锁降低系统并发度，影响系统性能

    *   行级锁可以提高事务并发能力，但是可能会出现幻读问题

    *   指定范围的已存在或待插入记录进行加锁，可避免幻读问题

*   **加锁位置**：


    *   两个索引键之间的间隙

    *   第一个索引键之前

    *   最后一个索引键之后

*   **示例**：

    T1: FIND id >0 & id < 20

    T2: INSERT id = 6

### 基于静态检查点的故障恢复



1.  **WAL 机制**：先写日志后写数据

2.  **REDO/UNDO 日志**：redo 已提交事务；undo 未提交 /abort 事务

3.  **静态检查点**：故障恢复时从最新的一个检查点开始扫描日志

*   流程：开始 → 停止接收新的事务 → 将检查点记录写入日志文件 → 将检查点记录的地址写入 "restarttxt" 中 → 将脏页和日志刷盘 → 结束

## Part.03 性能优化

### 连接算子



*   **归并连接**：


    *   不足：排序操作会增加额外的开销；对于小表，效率不如哈希连接；需要多次磁盘 I/O，影响性能

*   **哈希连接**：


    *   优势：不需要输入数据排序，适合无序数据集；对于等值连接，通常比归并连接更快

    *   单独优化：构建的左算子哈希表常驻内存

*   **效果**：TPCC 测试事务吞吐量提升约 20%

### 缓冲池管理策略



*   **问题**：


    *   缓冲池资源争夺激烈

    *   缓存命中率低，频繁磁盘 I/O

    *   测试运行过程中内存资源相对充足

*   **优化策略**：使用独立的索引缓冲池（数据页与索引页分离管理）

*   **优点**：


    *   提高了页的命中率

    *   减少磁盘 I/O 操作，查询速度加快

    *   充分利用内存资源，系统运行更高效

    *   数据页和索引页可以使用不同的管理策略

*   **效果**：TPCC 测试事务吞吐量提升约 10%

### 蟹行协议



*   **问题**：并发访问时直接锁住整颗 B + 树，尽管能保证数据一致性，但粒度大，并发度低

*   **策略**：


    *   索引页增加读写锁（rwlock）

    *   查找：从根开始，逐级下探，获取子节点读锁，再解锁父节点，直到到达叶子节点

    *   插入 / 删除：从根开始，沿途获取节点写锁，子节点安全时，释放祖先节点的锁

### 乐观锁和悲观锁



*   **现象**：


    *   修改 B + 树每次都需要对沿途 node 加写锁

    *   上层节点锁竞争激烈

    *   结点 split/redistribute/coalesce 是小概率的

*   **策略**：


    *   采用乐观锁和悲观锁相结合的方式

    *   首先持乐观态度遍历 B + 树，认为操作是安全的，对沿途结点加读锁，叶节点加写锁

    *   若发现结点不安全，放弃持有的所有锁，悲观加锁，重新遍历

*   **效果**：本地测试 50+50 线程并发，插入删除操作时间减少约 10%\~15%

### 锁管理



*   **优化点**：

1.  **降低粒度**：原先锁管理器只使用了一把全局 std::mutex latch\_锁；每个锁请求队列增加单独锁，大大提高了锁管理的并发性能

2.  **锁授予规则**：

*   仅当前请求是第一个处于 waiting 状态的请求时，才授予锁

*   判断当前请求是否与前面的 waiting 请求兼容。若兼容，则仍可以授予

<!---->

*   **效果**：TPCC 测试事务吞吐量提升约 100%

### 意向锁



*   **问题**：


    *   加行锁时，需要检查表是否存在锁、检查表中任意一行的锁

*   **策略**：意向锁机制 —— 给行加 S/X 锁前先给该表加 IS/IX 锁

*   **锁兼容性矩阵**：



|    | S | X | IS | IX |
| -- | - | - | -- | -- |
| S  | ✔ | ✘ | ✔  | ✘  |
| X  | ✘ | ✘ | ✘  | ✘  |
| IS | ✔ | ✘ | ✔  | ✔  |
| IX | ✘ | ✘ | ✔  | ✔  |

## Part.04 总结思考

### 成绩排名

（展示各队伍 tpc-x 得分及排名，部分如下）：



*   1：cabbageDB / 华中科技大学，65652.577433

*   2：RUSHDB / 成都理工大学，32820.801132

*   ...

*   15：对什么队 / 西北工业大学，40.913798

*   16：啊对对队 / 西北工业大学，15.108638

*   16：日薪月亿 / 哈尔滨工业大学 (深圳)，11.60729

### 设计文档

（含相关图片，此处引用原 PPT 图片描述）

### 优势与不足



*   **优势创新**：


    *   缓冲池分离：使用独立的索引缓冲池，提高页命中率，减少磁盘 I/O，充分利用内存，数据页和索引页可采用不同管理策略

    *   优化 Hash join：Hash Join 使用哈希表将较小的表放入内存，探测阶段只扫描较大的表，减少 I/O，优化内存使用

*   **不足反思**：


    *   未能发现性能瓶颈：决赛后期才发现性能瓶颈在于并发控制策略，基于严格两阶段锁的可串行化策略导致事务吞吐率极低，本地测试平均 16 个线程中 13 个因锁策略阻塞

    *   时间不足：考虑使用多版本并发控制（MVCC）改善事务吞吐率低的问题，但受限于时间未能实现

敬请批评指正

> （注：文档部分内容可能由 AI 生成）