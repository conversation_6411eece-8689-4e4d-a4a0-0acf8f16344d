/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "buffer_pool_manager.h"
#include "recovery/log_manager.h"

/**
 * @description: 从free_list或replacer中得到可淘汰帧页的 *frame_id
 * @return {bool} true: 可替换帧查找成功 , false: 可替换帧查找失败
 * @param {frame_id_t*} frame_id 帧页id指针,返回成功找到的可替换帧id
 */
bool BufferPoolManager::find_victim_page(frame_id_t* frame_id) {
    assert(frame_id != nullptr);
    // 优先从free_list获取可用帧
    if (!free_list_.empty()) {
        *frame_id = free_list_.front();
        free_list_.pop_front();
        return true;
    }
    // free_list为空时从replacer淘汰页面
    return replacer_->victim(frame_id);
}

/**
 * @description: 更新页面数据, 如果为脏页则需写入磁盘，再更新为新页面，更新page元数据(data, is_dirty, page_id)和page table
 * @param {Page*} page 写回页指针
 * @param {PageId} new_page_id 新的page_id
 * @param {frame_id_t} new_frame_id 新的帧frame_id
 */
void BufferPoolManager::update_page(Page *page, PageId new_page_id, frame_id_t new_frame_id) {
    if (page == nullptr) return;

    const PageId old_page_id = page->get_page_id();
    const auto it = page_table_.find(old_page_id);

    // 页面不在内存时直接更新页表和页面元数据
    if (it == page_table_.end()) {
        page_table_[new_page_id] = new_frame_id;
        page->id_ = new_page_id;
        page->reset_memory();
        return;
    }

    // 脏页刷盘（含WAL检查）
    if (page->is_dirty()) {
        if (log_manager_ != nullptr) {
            const lsn_t page_lsn = page->get_page_lsn();
            const lsn_t persist_lsn = log_manager_->get_persist_lsn();
            if (page_lsn > persist_lsn) {
                log_manager_->flush_log_to_disk();
            }
        }
        // std::fstream outfile;
        // outfile.open("PID.txt",std::ios::out | std::ios::app);
        // outfile << "UPDATE page " << old_page_id.page_no <<std::endl;
        // outfile.close();
        disk_manager_->write_page(old_page_id.fd, old_page_id.page_no, page->get_data(), PAGE_SIZE);
        page->is_dirty_ = false;
    }

    // 更新页表和页面元数据
    page_table_.erase(it);  // 利用迭代器直接删除，减少查找
    page_table_[new_page_id] = new_frame_id;
    page->id_ = new_page_id;
    page->reset_memory();
    page->is_dirty_ = false;
}

/**
 * @description: 从buffer pool获取需要的页
 * @return {Page*} 若获得了需要的页则将其返回，否则返回nullptr
 * @param {PageId} page_id 需要获取的页的PageId
 */
Page* BufferPoolManager::fetch_page(PageId page_id) {
    std::scoped_lock lock{latch_};

    // 检查目标页是否已在缓冲池
    const auto it = page_table_.find(page_id);
    if (it != page_table_.end()) {
        const frame_id_t frame_id = it->second;
        replacer_->pin(frame_id);
        Page& target_page = pages_[frame_id];
        target_page.pin_count_++;
        return &target_page;
    }

    // 查找可用帧
    frame_id_t victim_frame = -1;
    if (!find_victim_page(&victim_frame)) {
        return nullptr;
    }

    // 更新victim帧为新页面
    Page& victim_page = pages_[victim_frame];
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "victim page " << victim_page.get_page_id().page_no <<std::endl;
    // outfile.close();
    update_page(&victim_page, page_id, victim_frame);
    
    // 从磁盘读取数据到帧
    char page_raw_data[PAGE_SIZE];
    disk_manager_->read_page(page_id.fd, page_id.page_no, page_raw_data, PAGE_SIZE);
    memcpy(victim_page.data_, page_raw_data, PAGE_SIZE);
    victim_page.pin_count_ = 1;

    return &victim_page;
}

/**
 * @description: 取消固定pin_count>0的在缓冲池中的page
 * @return {bool} 如果目标页的pin_count<=0则返回false，否则返回true
 * @param {PageId} page_id 目标page的page_id
 * @param {bool} is_dirty 若目标page应该被标记为dirty则为true，否则为false
 */
bool BufferPoolManager::unpin_page(PageId page_id, bool is_dirty) {
    std::scoped_lock lock{latch_};

    // 检查目标页是否在缓冲池
    const auto it = page_table_.find(page_id);
    if (it == page_table_.end()) {
        return false;
    }

    // 检查pin_count是否有效
    const frame_id_t frame_id = it->second;
    Page& target_page = pages_[frame_id];
    if (target_page.pin_count_ <= 0) {
        return false;
    }

    // 更新pin_count，若减为0则调用unpin
    if (--target_page.pin_count_ == 0) {
        replacer_->unpin(frame_id);
    }

    // 更新脏页标记
    if (is_dirty) {
        target_page.is_dirty_ = true;
    }

    return true;
}

/**
 * @description: 将目标页写回磁盘，不考虑当前页面是否正在被使用
 * @return {bool} 成功则返回true，否则返回false(只有page_table_中没有目标页时)
 * @param {PageId} page_id 目标页的page_id，不能为INVALID_PAGE_ID
 */
bool BufferPoolManager::flush_page(PageId page_id) {
    std::scoped_lock lock{latch_};

    // 检查page_id有效性
    if (page_id.page_no == INVALID_PAGE_ID) {
        throw InternalError("BufferPoolManager::flush_page: invalid page_id \n");
    }

    // 检查目标页是否在缓冲池
    const auto it = page_table_.find(page_id);
    if (it == page_table_.end()) {
        return false;
    }

    // 执行WAL检查并刷盘
    const frame_id_t target_frame = it->second;
    Page& target_page = pages_[target_frame];
    if (log_manager_ != nullptr) {
        const lsn_t page_lsn = target_page.get_page_lsn();
        const lsn_t persist_lsn = log_manager_->get_persist_lsn();
        if (page_lsn > persist_lsn) {
            log_manager_->flush_log_to_disk();
        }
    }

    // 写回磁盘并更新脏页标记
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "FLUSH page " << page_id.page_no <<std::endl;
    // outfile.close();
    disk_manager_->write_page(page_id.fd, page_id.page_no, target_page.get_data(), PAGE_SIZE);
    target_page.is_dirty_ = false;

    return true;
}

/**
 * @description: 创建一个新的page
 * @return {Page*} 返回新创建的page，若创建失败则返回nullptr
 * @param {PageId*} page_id 当成功创建一个新的page时存储其page_id
 */
Page* BufferPoolManager::new_page(PageId* page_id) {
    std::scoped_lock lock{latch_};

    // 查找可用帧
    frame_id_t new_frame = INVALID_FRAME_ID;
    if (!find_victim_page(&new_frame)) {
        return nullptr;
    }

    // 分配新页ID
    const page_id_t new_page_no = disk_manager_->allocate_page(page_id->fd);
    page_id->page_no = new_page_no;

    // 处理帧中原有脏页（含WAL检查）
    Page& new_page = pages_[new_frame];
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "victim page " << new_page.get_page_id().page_no <<std::endl;
    // outfile.close();
    if (new_page.is_dirty()) {
        if (log_manager_ != nullptr) {
            const lsn_t page_lsn = new_page.get_page_lsn();
            const lsn_t persist_lsn = log_manager_->get_persist_lsn();
            if (page_lsn > persist_lsn) {
                log_manager_->flush_log_to_disk();
            }
        }
        const PageId old_page_id = new_page.get_page_id();
        // std::fstream outfile;
        // outfile.open("PID.txt",std::ios::out | std::ios::app);
        // outfile << "NEW PSGE " << old_page_id.page_no <<std::endl;
        // outfile.close();
        disk_manager_->write_page(old_page_id.fd, old_page_id.page_no, new_page.get_data(), PAGE_SIZE);
        new_page.is_dirty_ = false;
    }

    // 固定帧并更新页数据
    replacer_->pin(new_frame);
    update_page(&new_page, *page_id, new_frame);
    new_page.pin_count_ = 1;

    return &new_page;
}

/**
 * @description: 从buffer_pool删除目标页
 * @return {bool} 如果目标页不存在于buffer_pool或者成功被删除则返回true，若其存在于buffer_pool但无法删除则返回false
 * @param {PageId} page_id 目标页
 */
bool BufferPoolManager::delete_page(PageId page_id) {
    std::scoped_lock lock{latch_};

    // 检查目标页是否在缓冲池
    const auto it = page_table_.find(page_id);
    if (it == page_table_.end()) {
        return true;
    }

    // 检查pin_count是否为0（无法删除被使用的页）
    const frame_id_t frame_id = it->second;
    Page& target_page = pages_[frame_id];
    if (target_page.pin_count_ != 0) {
        return false;
    }

    // 释放帧资源并重置页元数据
    free_list_.push_back(frame_id);
    replacer_->pin(frame_id);  // 从替换器中移除
    target_page.reset_memory();
    target_page.is_dirty_ = false;
    target_page.id_ = {0, INVALID_PAGE_ID};
    target_page.pin_count_ = 0;
    page_table_.erase(it);  // 利用迭代器删除，减少查找

    return true;
}

/**
 * @description: 将buffer_pool中的所有页写回到磁盘
 * @param {int} fd 文件句柄
 */
void BufferPoolManager::flush_all_pages(int fd) {
    std::scoped_lock lock{latch_};

    // 遍历所有帧，刷写脏页
    for (size_t i = 0; i < pool_size_; ++i) {
        Page& curr_page = pages_[i];
        const PageId curr_page_id = curr_page.get_page_id();
        
        // 只处理有效且脏的页
        if (curr_page_id.page_no != INVALID_PAGE_ID && curr_page.is_dirty()) {
            // WAL检查：确保日志先于数据持久化
            if (log_manager_ != nullptr) {
                const lsn_t page_lsn = curr_page.get_page_lsn();
                const lsn_t persist_lsn = log_manager_->get_persist_lsn();
                if (page_lsn > persist_lsn) {
                    log_manager_->flush_log_to_disk();
                }
            }

            // 写回磁盘并清除脏标记
            // std::fstream outfile;
            // outfile.open("PID.txt",std::ios::out | std::ios::app);
            // outfile << "flush_all_pages " << curr_page_id.page_no <<std::endl;
            // outfile.close();
            disk_manager_->write_page(curr_page_id.fd, curr_page_id.page_no, curr_page.get_data(), PAGE_SIZE);
            curr_page.is_dirty_ = false;
        }
    }
}