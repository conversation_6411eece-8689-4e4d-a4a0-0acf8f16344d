/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "transaction/watermark.h"


auto Watermark::AddTxn(timestamp_t read_ts) -> void {
    /*增加一个时间戳，将改时间戳插入到合适的位置*/
    auto it = std::find(timestamps.begin(), timestamps.end(), read_ts);
    if (it != timestamps.end()) {
        /*找到了更新map的个数*/
        current_reads_[read_ts]++;
    }
    else
    {
        /*没找到则加入vector和map*/
        timestamps.push_back(read_ts);
        std::sort(timestamps.begin(), timestamps.end());
        current_reads_[read_ts] = 1;
    }
    watermark_ = timestamps.front();
}

auto Watermark::RemoveTxn(timestamp_t read_ts) -> void {
     auto it = std::find(timestamps.begin(), timestamps.end(), read_ts);
     if(it == timestamps.end())
     {
        return;
     }
     else
     {
        current_reads_[read_ts]--;
        int count = current_reads_[read_ts];
        if(count == 0)
        {
            current_reads_.erase(read_ts);
            timestamps.erase(it);
        }
     }
     if(!timestamps.empty())
     {
        watermark_ = timestamps.front();
     }
     else
     {
        watermark_ = commit_ts_;
     }
}

auto Watermark::UpdateCommitTs(timestamp_t commit_ts) -> void
{
    commit_ts_ = commit_ts;
    if(current_reads_.empty())
    {
        watermark_ = commit_ts;
    }
}