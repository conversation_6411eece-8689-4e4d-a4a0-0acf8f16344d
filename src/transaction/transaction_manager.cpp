/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "transaction_manager.h"
#include "record/rm_file_handle.h"
#include "system/sm_manager.h"
#include "execution/execution_common.h"

std::unordered_map<txn_id_t, Transaction *> TransactionManager::txn_map = {};

/**
 * @description: 事务的开始方法
 * @return {Transaction*} 开始事务的指针
 * @param {Transaction*} txn 事务指针，空指针代表需要创建新事务，否则开始已有事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
Transaction * TransactionManager::begin(Transaction* txn, LogManager* log_manager) {
    // Todo:
    // 1. 判断传入事务参数是否为空指针
    // 2. 如果为空指针，创建新事务
    // 3. 把开始事务加入到全局事务表中
    // 4. 返回当前事务指针
    // 如果需要支持MVCC请在上述过程中添加代码
    
    txn_id_t txn_id = INVALID_TXN_ID;
    timestamp_t timestamp = last_commit_ts_.load();
    
    if(!txn)
    {
        /*创建新事务*/
        txn_id = next_txn_id_.fetch_add(1,std::memory_order_relaxed);
        txn = new Transaction(txn_id);
        if(get_concurrency_mode() == ConcurrencyMode::MVCC)
        {
            /*设置开始时间戳、读取时间戳*/
            txn->set_start_ts(timestamp);//开始时间戳是事务调用begin的时间
            // txn->set_read_ts(timestamp);
        }
    }

    /*设置事务的状态*/
    txn->set_state(TransactionState::GROWING);
    txn->set_txn_mode(true);

    /*写开始日志*/
    BeginLogRecord *begin_log = new BeginLogRecord(txn_id);
    txn->set_prev_lsn(log_manager->add_log_to_buffer(begin_log));

    /*把开始事务加入全局事务表*/
    /*加锁*/
    std::unique_lock<std::mutex> lock(latch_);
    txn_map[txn->get_transaction_id()] = txn;

    // if(txn->get_txn_mode())
    // {
    //     printf("modeddcsdfcec\n");
    // }


    /*加入水印槽*/
    if(get_concurrency_mode() == ConcurrencyMode::MVCC)
    {
        running_txns_.AddTxn(txn->get_read_ts());
        /*2.设置读取时间戳*/
        txn->set_read_ts(timestamp);
    }
    return txn;
}

/**
 * @description: 事务的提交方法
 * @param {Transaction*} txn 需要提交的事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
void TransactionManager::commit(Transaction* txn, LogManager* log_manager) {
    // Todo:
    // 1. 如果存在未提交的写操作，提交所有的写操作
    // 2. 释放所有锁
    // 3. 释放事务相关资源，eg.锁集
    // 4. 把事务日志刷入磁盘中
    // 5. 更新事务状态
    // 如果需要支持MVCC请在上述过程中添加代码
    std::unique_lock<std::mutex> lock(commit_mutex_);
    /*0.判断事务状态*/
    // assert(txn->get_state() != TransactionState::COMMITTED);
    // assert(txn->get_state() != TransactionState::ABORTED);
    timestamp_t curr_timestamp = last_commit_ts_.load();
    auto &private_var = txn->get_private_update();
    auto &private_insert = txn->get_private_insert();
    /*首先进行冲突检测*/
    if(verfiyConflict(txn, private_var))
    {
        // abort(txn, log_manager);
        // return;
        /*有冲突回滚事务*/
        throw TransactionAbortException(txn->get_transaction_id(), AbortReason::UPGRADE_CONFLICT1);  
    }

    /*如果没有冲突则进行物理修改*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn, nullptr);
    /*先进行插入操作，因为后续的更新是基于insert的*/
    for(auto pvi : private_insert)
    {
        auto &rid = pvi.first.second;
        auto &fd = pvi.first.first;
        auto &val = pvi.second;
        auto table_name = sm_manager_->get_disk_manager()->get_file_name(fd);
        auto filehandle = sm_manager_->fhs_.at(table_name).get();
        auto tuple_meta = filehandle->get_tuple_meta(rid);
        TabMeta &tab = sm_manager_->db_.get_table(table_name);
        if(private_var.count(pvi.first))
        {
            auto &update_val = private_var.at(pvi.first);
            commit_insert(std::get<0>(update_val), std::get<0>(update_val), table_name, rid, tab, filehandle, context.get());
            filehandle->insert_record(rid,std::get<0>(update_val).data);
            private_var.erase(pvi.first);
        }
        else
        {
            // try
            // {
            commit_insert(std::get<0>(val), std::get<0>(val), table_name, rid, tab, filehandle, context.get());
            // }
            // catch (UniqueConstraintError &e)
            // {
            //     printf("INSERT ERROR %s\n", table_name.c_str());
            //     std::get<0>(val).showrecord(tab);
            // }
            // filehandle->insert_record(rid,std::get<0>(val).data);
        }
        filehandle->update_tuple_meta(rid, {curr_timestamp + 1, false});
    }
    for(auto pv: private_var)
    {
        auto &rid = pv.first.second;
        auto &fd = pv.first.first;
        auto &val = pv.second;
        auto table_name = sm_manager_->get_disk_manager()->get_file_name(fd);
        auto filehandle = sm_manager_->fhs_.at(table_name).get();
        auto tuple_meta = filehandle->get_tuple_meta(rid);
        TabMeta &tab = sm_manager_->db_.get_table(table_name);
        bool isWWconflict = false;
        switch(std::get<1>(val))
        {
            case OperationType::DELETE:
            {
                auto old_record = get_visible_record(&tab, filehandle, rid, txn, &isWWconflict, context.get());
                /*延迟到垃圾回收*/
                commit_delete(*old_record, std::get<0>(val), table_name, rid, tab, filehandle, context.get());
                filehandle->update_tuple_meta(rid, {curr_timestamp + 1, true});
                break;
            }
            case OperationType::UPDATE:
            {
                RmRecord old_record;
                if(private_insert.count(pv.first))
                {
                    old_record = std::get<0>(val);
                }
                else
                {
                    old_record = *(get_visible_record(&tab, filehandle, rid, txn, &isWWconflict, context.get()));
                }
                commit_update(old_record, std::get<0>(val), table_name,
                rid, tab, filehandle, context.get());
                filehandle->update_record(rid,std::get<0>(val).data, context.get());
                filehandle->update_tuple_meta(rid, {curr_timestamp + 1, false});
                break;
            }
            default:
                break;
        }
    }

    /*2.释放锁有锁*/
    auto lock_set = txn->get_lock_set();
    if(lock_set)
    {
        for(auto& ls : *lock_set)
        {
            lock_manager_->unlock(txn, ls);
        }
        if(!lock_set->empty())
        {
            lock_set->clear();
        }
    }
    

    /*3.释放事务资源*/
    auto idps = txn->get_index_deleted_page_set();
    if(idps)
    {
        if(!idps->empty())
        {
            idps->clear();
        }
    }
    auto dps = txn->get_index_latch_page_set();
    if(dps)
    {
        if(!dps->empty())
        {
            dps->clear();
        }
    }

    /*4.写入提交日志*/
    CommitLogRecord *commit_log = new CommitLogRecord(txn->get_transaction_id());
    txn->set_prev_lsn(log_manager->add_log_to_buffer(commit_log));
    // log_manager->flush_log_to_disk();

    /*5.更新事务状态*/
    txn->set_state(TransactionState::COMMITTED);

    /*6.MVCC*/
    if(get_concurrency_mode() == ConcurrencyMode::MVCC)
    {
        running_txns_.RemoveTxn(txn->get_read_ts());
        /*6.1更新提交时间戳*/
        last_commit_ts_.fetch_add(1, std::memory_order_relaxed);
        txn->set_commit_ts(last_commit_ts_);
        running_txns_.UpdateCommitTs(txn->get_commit_ts());
    }

    /*移除事务*/
    // std::scoped_lock lock{latch_};
    // txn_map.erase(txn->get_transaction_id());
    //// delete txn;
}

/**
 * @description: 事务的终止（回滚）方法
 * @param {Transaction *} txn 需要回滚的事务
 * @param {LogManager} *log_manager 日志管理器指针
 */
void TransactionManager::abort(Transaction * txn, LogManager *log_manager) {
    // Todo:
    // 1. 回滚所有写操作
    // 2. 释放所有锁
    // 3. 清空事务相关资源，eg.锁集
    // 4. 把事务日志刷入磁盘中
    // 5. 更新事务状态
    // 如果需要支持MVCC请在上述过程中添加代码
    
    /*1.回滚写操作*/
    std::unordered_set<Rid> rids;
    std::unordered_map<Rid, std::string> tablenames;
    /*如果没有冲突则进行物理修改*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn, nullptr);
    auto &private_var = txn->get_private_insert();
    auto &private_update_var = txn->get_private_update();
    for(auto pv: private_var)
    {
        auto &rid = pv.first.second;
        auto &fd = pv.first.first;
        auto &val = pv.second;
        auto table_name = sm_manager_->get_disk_manager()->get_file_name(fd);
        auto filehandle = sm_manager_->fhs_.at(table_name).get();
        auto tuple_meta = filehandle->get_tuple_meta(rid);
        if(std::get<1>(val) == OperationType::INSERT)
        {
            /*清除槽位*/
            filehandle->deAllocateRid(rid, context.get());
            /*移除时间戳*/
            filehandle->erase_tuple_meta(rid);
        }
    }
    private_update_var.clear();
    private_var.clear();

    /*清除删除集合*/
    auto delete_set = txn->get_delete_set();
    if(delete_set)
    {
        for (auto wr : *delete_set) {  // 遍历每个WriteRecord指针
            delete wr;  // 释放动态分配的WriteRecord
        }
        delete_set->clear();  // 清空集合，避免悬垂指针
    }

    /*2.释放锁有锁*/
    auto lock_set = txn->get_lock_set();
    if(lock_set)
    {
        for(auto& ls : *lock_set)
        {
            lock_manager_->unlock(txn, ls);
        }
    }
    lock_set->clear();

    /*3.释放事务资源*/
    auto idps = txn->get_index_deleted_page_set();
    if(idps)
    {
        idps->clear();
    }
    auto dps = txn->get_index_latch_page_set();
    if(dps)
    {
        dps->clear();
    }

    /*4.写入提交日志*/
    AbortLogRecord *abort_log = new AbortLogRecord(txn->get_transaction_id());
    txn->set_prev_lsn(log_manager->add_log_to_buffer(abort_log));
    // log_manager->flush_log_to_disk();

    /*5.更新事务状态*/
    txn->set_state(TransactionState::ABORTED);

    /*6.MVCC*/
    if(get_concurrency_mode() == ConcurrencyMode::MVCC)
    {
        running_txns_.RemoveTxn(txn->get_read_ts());
    }

    /*移除事务*/
    // std::scoped_lock lock{latch_};
    // txn_map.erase(txn->get_transaction_id());
    // delete txn;
}

void TransactionManager::insert_abort(Transaction * txn, WriteRecord wr, LogManager *log_manager)
{
    /*1.INSERT的回滚就是将插入的元组删除*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn, nullptr);
    std::string table_name = wr.GetTableName();
    auto fileHandle = sm_manager_->fhs_.at(table_name).get();
    auto tab_ = sm_manager_->db_.get_table(table_name);
    auto rid = wr.GetRid();
    auto record = fileHandle->get_record(rid, context.get());

    /*2.删除索引*/
    for(size_t idx = 0;idx < tab_.indexes.size();idx++)
    {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
        char *key = new char[index.col_tot_len];
        int offset = 0;
        /*2.1构造主键值*/
        for(size_t i = 0;i < index.col_num; i++)
        {
            memcpy(key+offset, record->data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        /*2.2删除索引*/
        ih->delete_entry(key, txn);
    }

    /*3.删除记录*/
    fileHandle->delete_record(rid, context.get());
}

void TransactionManager::delete_abort(Transaction * txn, WriteRecord wr, LogManager *log_manager)
{
    /*1.删除的回滚就是重新插入*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn, nullptr);
    std::string table_name = wr.GetTableName();
    auto fileHandle = sm_manager_->fhs_.at(table_name).get();
    auto tab_ = sm_manager_->db_.get_table(table_name);
    auto record = wr.GetRecord();
    
    /*2.不能从wr获取rid,因为此时rid的位置已经被占用*/
    auto rid = fileHandle->insert_record(record.data, context.get());

    /*3.插入索引*/
    for(size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
        char* key = new char[index.col_tot_len];
        int offset = 0;
        for(size_t i = 0; i < index.col_num; ++i) {
            memcpy(key + offset, record.data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        ih->insert_entry(key, rid, txn);
    }
}

void TransactionManager::update_abort(Transaction * txn, WriteRecord wr, LogManager *log_manager)
{
    /*1.更新的回滚就是将旧值写回去*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn, nullptr);
    std::string table_name = wr.GetTableName();
    auto fileHandle = sm_manager_->fhs_.at(table_name).get();
    auto tab_ = sm_manager_->db_.get_table(table_name);
    auto rid = wr.GetRid();
    auto new_record = wr.GetRecord();

    /*2.删除旧索引*/
    auto old_record = fileHandle->get_record(rid, context.get());
    for(size_t idx = 0;idx < tab_.indexes.size();idx++)
    {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
        char *key = new char[index.col_tot_len];
        int offset = 0;
        /*2.1构造主键值*/
        for(size_t i = 0;i < index.col_num; i++)
        {
            memcpy(key+offset, old_record->data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        /*2.2删除索引*/
        ih->delete_entry(key, txn);
    }

    /*3.更新记录*/
    fileHandle->update_record(rid, new_record.data, context.get());

    /*4.插入新索引*/
    for(size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
        char* key = new char[index.col_tot_len];
        int offset = 0;
        for(size_t i = 0; i < index.col_num; ++i) {
            memcpy(key + offset, new_record.data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        ih->insert_entry(key, rid, txn);
    }
}

/**
* @brief 更新一个versionlink,若没版本会创建。
* 在更新之前，将调用 `check` 函数以确保有效性。
*/
bool TransactionManager::UpdateUndoLink(int fd, Rid rid, std::optional<UndoLink> prev_link,
                    std::function<bool(std::optional<UndoLink>)> &&check)
{
    /*首先根据元组找到对应的版本*/
    std::optional<VersionUndoLink> prev_version_link;
    if(prev_link.has_value())
    {
        prev_version_link = std::make_optional<VersionUndoLink>({prev_link.value(), true});
    }
    else
    {
        prev_version_link = std::nullopt;
    }
    /*根据fd找到对应的表version*/
    /*锁住表级别版本连*/
    std::scoped_lock lock(table_version_info_mutex_);
    /*取出对应的versioninfo*/
    // 先检查fd是否存在,不存在直接返回空
    if (table_version_info_.find(fd) == table_version_info_.end()) {
        /*不存在则新建一个*/
        std::shared_ptr<VersionInfo> empty_versioninfo = std::make_shared<VersionInfo>();
        table_version_info_[fd] = empty_versioninfo;
    }
    /*存在取出对应的表版本连*/
    auto &version_info = table_version_info_.at(fd);
    return UpdateVersionLink(*version_info, rid, prev_version_link);
}

/**
 * @brief 更新一个撤销链接，该链接将表堆元组与第一个撤销日志连接起来。
 * 在更新之前，将调用 `check` 函数以确保有效性。
 */
bool TransactionManager::UpdateVersionLink(VersionInfo &version_info, Rid rid, std::optional<VersionUndoLink> prev_version,
                       std::function<bool(std::optional<VersionUndoLink>)> &&check)
{
    /*调用check*/

    /*首先根据元组找到对应的版本*/
    std::scoped_lock lockf(version_info.version_info_mutex_);
    auto &version_info_ = version_info.version_info_;
    if(version_info_.find(rid.page_no) == version_info_.end())
    {
        /*没有，先创建*/
        std::shared_ptr<PageVersionInfo>pageversion = std::make_shared<PageVersionInfo>();
        version_info_[rid.page_no] = pageversion;
    }
    auto page_version_info = version_info_.find(rid.page_no);
    std::scoped_lock lock(page_version_info->second->mutex_);
    if(prev_version.has_value())
    {
        if(page_version_info->second->prev_version_.find(rid.slot_no) == page_version_info->second->prev_version_.end())
        {
            /*若是没有版本链则新建一个*/
            page_version_info->second->prev_version_.insert({rid.slot_no, VersionUndoLink{}});
        }
        page_version_info->second->prev_version_[rid.slot_no] = prev_version.value();
    }
    else
    {
        /*删除对应的version*/
        if(page_version_info->second->prev_version_.find(rid.slot_no) != page_version_info->second->prev_version_.end())
        {
            /*有对应的版本则删除*/
            page_version_info->second->prev_version_.erase(rid.slot_no);
        }
    }
    return true;
}

/** @brief 获取表堆元组的第一个撤销日志。 */
std::optional<UndoLink> TransactionManager::GetUndoLink(int fd, Rid rid)
{
    /*首先检查rid的合法性*/
    if(rid.page_no == INVALID_PAGE_ID || fd < 0)
    {
        return std::nullopt;
    }
    /*锁住表级别版本连*/
    std::scoped_lock lock(table_version_info_mutex_);
    /*取出对应的versioninfo*/
    // 先检查fd是否存在,不存在直接返回空
    if (table_version_info_.find(fd) == table_version_info_.end()) {
        return std::nullopt;
    }
    /*存在取出对应的表版本连*/
    auto &version_info = table_version_info_.at(fd);
    std::optional<VersionUndoLink> version_link = GetVersionLink(*version_info, rid);
    if(version_link.has_value())
    {
        return version_link.value().prev_;
    }
    return std::nullopt;
}

/** @brief 获取表堆元组的第一个撤销日志。*/
std::optional<VersionUndoLink> TransactionManager::GetVersionLink(VersionInfo &version_info, Rid rid)
{
    /*首先根据rid的页号早到页版本链*/
    std::scoped_lock lock1(version_info.version_info_mutex_);
    auto &version_info_ = version_info.version_info_;
    // 先检查page_no是否存在
    if (version_info_.find(rid.page_no) == version_info_.end()) {
        return std::nullopt;
    }
    auto page_version_info = version_info_.at(rid.page_no);

    std::scoped_lock lock2(page_version_info->mutex_);
    auto tuple_version_link = page_version_info->prev_version_.find(rid.slot_no);
    if(tuple_version_link == page_version_info->prev_version_.end())
    {
        return std::nullopt;
    }
    return tuple_version_link->second;
}

/** @brief 访问事务撤销日志缓冲区并获取撤销日志。如果事务不存在，返回 nullopt。
 * 如果索引超出范围仍然会抛出异常。 */
std::optional<UndoLog> TransactionManager::GetUndoLogOptional(UndoLink link)
{
    /*根据link存储的事务和undolog下标在相应的事务中取出undo_log*/
    std::scoped_lock lock1(latch_);
    if(link.IsValid())
    {
      if(txn_map.find(link.prev_txn_) != txn_map.end())
      {
        auto it = txn_map.find(link.prev_txn_);
        return it->second->GetUndoLog(link.prev_log_idx_);
      }
    }
    return std::nullopt;
}

/** @brief 访问事务撤销日志缓冲区并获取撤销日志。除非访问当前事务缓冲区，
 * 否则应该始终调用此函数以获取撤销日志，而不是手动检索事务 shared_ptr 并访问缓冲区。 */
UndoLog TransactionManager::GetUndoLog(UndoLink link)
{
    /*根据link存储的事务和undolog下标在相应的事务中取出undo_log*/
    auto undolog = GetUndoLogOptional(link);
    if(undolog.has_value())
    {
        return undolog.value();
    }
    else
    {
        throw InternalError("");
    }
}

/** @brief 获取系统中的最低读时间戳。 */
timestamp_t TransactionManager::GetWatermark()
{
    return running_txns_.GetWatermark();
}

/** @brief 垃圾回收。仅在所有事务都未访问时调用。 */
void TransactionManager::GarbageCollection()
{
    auto watermark = GetWatermark();

    /*用于存储待删除的事务ID（避免边遍历边删除导致迭代器失效）*/
    std::vector<txn_id_t> to_remove;

    /*1. 遍历所有事务，筛选候选事务*/
    for (const auto& [txn_id, txn] : txn_map) {
        /*1.1条件1：事务必须已提交或已中止（未完成的事务不能回收）*/
        if (txn->get_state() != TransactionState::COMMITTED && 
            txn->get_state() != TransactionState::ABORTED) {
            continue;
        }

        /*1.2条件2：事务的所有undo log都对活跃事务不可见（即日志版本≤watermark）*/
        bool can_remove = true;
        for (size_t i = 0; i < txn->GetUndoLogNum(); ++i) {
            UndoLog undo_log = txn->GetUndoLog(i);
            // 若存在任何undo log的版本≥watermark，说明仍可能被活跃事务访问
            if (undo_log.ts_ >= watermark) {
                can_remove = false;
                break;
            }
        }

        /*1.3满足条件的事务加入待删除列表*/
        if (can_remove) {
            to_remove.push_back(txn_id);
        }
    }

    // 2. 移除候选事务（单独遍历删除，避免迭代器失效）
    for (txn_id_t txn_id : to_remove) {
        auto temp_txn = txn_map[txn_id];
        std::shared_ptr<Context> context = std::make_shared<Context>(nullptr, nullptr, nullptr, nullptr);
        /*首先执行物理删除*/
        auto delete_set = temp_txn->get_delete_set();
        if (delete_set == nullptr) {
            continue;  // 或记录日志，跳过无效的 delete_set
        }
        for(auto& ds : *(delete_set))
        {
            /*删除记录*/
            auto filehandle = sm_manager_->fhs_.at(ds->GetTableName()).get();
            filehandle->delete_record(ds->GetRid(), context.get());
            /*删除索引*/
            auto tab = sm_manager_->db_.get_table(ds->GetTableName());
            for (const auto& index : tab.indexes) {
                // 获取索引句柄
                const std::string index_name = sm_manager_->get_ix_manager()->get_index_name(ds->GetTableName(), index.cols);
                auto* ih = sm_manager_->ihs_.at(index_name).get();
                // 构造索引键
                char* key = new char[index.col_tot_len];
                int offset = 0;
                for (const auto& col : index.cols) {
                    memcpy(key + offset, ds->GetRecord().data + col.offset, col.len);
                    offset += col.len;
                }
                // 执行索引删除
                ih->delete_entry(key, temp_txn);
                delete[] key;  // 及时释放动态分配的内存
            }
        }

        /*关键：释放delete_set中所有WriteRecord的内存*/
        for (auto wr : *delete_set) {  // 遍历每个WriteRecord指针
            delete wr;  // 释放动态分配的WriteRecord
        }
        delete_set->clear();  // 清空集合，避免悬垂指针

        // 释放事务对象内存（假设Transaction由new创建）
        delete txn_map[txn_id];
        // 从事务映射表中删除
        txn_map.erase(txn_id);
    }

    // /*清除txn_map_*/
}


/** @brief 撤销一个undolog。 */
std::optional<TupleMeta> TransactionManager::WithdrawUndolog(int fd, Rid rid)
{
    auto undo_link = GetUndoLink(fd, rid).value_or(UndoLink{});
    if(!undo_link.IsValid())
    {
        return std::nullopt;
    }
    auto undolog = GetUndoLog(undo_link);

    /*更新元组微元的时间戳*/
    TupleMeta tuplemeta{undolog.ts_, undolog.is_deleted_};

    auto prev_undo_link = undolog.prev_version_;

    // /*锁住表级别版本连*/
    // std::scoped_lock lock(table_version_info_mutex_);
    // /*存在取出对应的表版本连*/
    // auto &version_info = table_version_info_.at(fd);
    // auto prev_version_link = GetVersionLink(*version_info, rid).value();
    // prev_version_link.prev_ = prev_undo_link;
    // UpdateVersionLink(rid, std::make_optional(prev_version_link));

    UpdateUndoLink(fd, rid, prev_undo_link);
    return tuplemeta;
}

bool TransactionManager:: verfiyConflict(Transaction* txn, std::unordered_map<std::pair<int, Rid>,std::tuple<RmRecord, OperationType>> private_var)
{
    bool conflict = false;
    timestamp_t read_ts = txn->get_read_ts();
    timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
    /*遍历私有变量集合*/
    for(auto &pv : private_var)
    {
        auto &rid = pv.first.second;
        auto &fd = pv.first.first;
        auto &val = pv.second;
        auto table_name = sm_manager_->get_disk_manager()->get_file_name(fd);
        auto filehandle = sm_manager_->fhs_.at(table_name).get();
        auto tuple_meta = filehandle->get_tuple_meta(rid);
        if((tuple_meta.ts_ >= TXN_START_ID && tuple_meta.ts_ != temp_ts)
        || (tuple_meta.ts_ < TXN_START_ID && tuple_meta.ts_ > read_ts))
        {
            conflict = true;
        }
        if(conflict)
        {
            return true;
        }
    }
    return false;
}

void TransactionManager::commit_update(RmRecord old_record, RmRecord new_record, std::string table_name,Rid rid, TabMeta &tab, RmFileHandle *fh, Context *context)
{
    auto tuplemeta = fh->get_tuple_meta(rid);
    std::vector<bool> modifiedvalue_bool(tab.cols.size(),false);
    std::vector<Value> modifiedvalue_value;

    ExtractDifferentValues(tab, old_record, new_record, modifiedvalue_value, modifiedvalue_bool);
    /*处理索引*/
    // 4. 构造新 key 并检查唯一性（仅检查受影响的索引）
    std::unordered_set<std::string> updated_cols;
    for(int i = 0; i < modifiedvalue_bool.size(); i++)
    {
        if(modifiedvalue_bool[i])
        {
            updated_cols.insert(tab.cols[i].name);
        }
    }
    std::vector<std::unique_ptr<char[]>> new_keys;  // 缓存 key，后续重复用
    std::vector<IxIndexHandle *> related_indexes;   // 对应索引句柄

    for (auto &index : tab.indexes) {
        // 检查索引是否涉及更新列
        bool is_related = false;
        for (auto &col : index.cols) {
            if (updated_cols.count(col.name)) {
                is_related = true;
                break;
            }
        }
        if (!is_related) continue;
        // 构造新索引键
        auto new_key = std::make_unique<char[]>(index.col_tot_len);
        int key_offset = 0;
        for (int j = 0; j < index.col_num; ++j) {
            memcpy(new_key.get() + key_offset, 
                   new_record.data + index.cols[j].offset, 
                   index.cols[j].len);
            key_offset += index.cols[j].len;
        }
        // 验证索引唯一性
        auto index_handle = sm_manager_->ihs_.at(
            sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
        index_handle->insert_entry(new_key.get(), rid, context->txn_);
        related_indexes.push_back(index_handle);
        new_keys.push_back(std::move(new_key));
    }

    // 5. 删除旧索引项（仅删除受影响的索引）
    for (size_t i = 0; i < related_indexes.size(); ++i) {
        auto index_handle = related_indexes[i];
        const auto &index_meta = tab.indexes[i];
        // 构造旧索引键
        auto old_key = std::make_unique<char[]>(index_meta.col_tot_len);
        int key_offset = 0;
        for (int j = 0; j < index_meta.col_num; ++j) {
            memcpy(old_key.get() + key_offset, 
                   old_record.data + index_meta.cols[j].offset, 
                   index_meta.cols[j].len);
            key_offset += index_meta.cols[j].len;
        }
        index_handle->delete_entry(old_key.get(), context->txn_);
    }

    /*新建版本链*/
    /*该事务没被其它未提交事务修改且当前提交时间戳小于读取时间戳*/
    auto prev_undolink = GetUndoLink(fh->GetFd(), rid);
    UndoLink undo_link_value = prev_undolink.value_or(UndoLink{});
    UndoLog undo_log{false, modifiedvalue_bool, modifiedvalue_value, nullptr, tuplemeta.ts_, undo_link_value};
    /*将该undolog放到版本连的第一位*/
    UndoLink undolink{context->txn_->AppendUndoLog(undo_log)};
    UpdateUndoLink(fh->GetFd(), rid, std::make_optional(undolink));
}

void TransactionManager::commit_insert(RmRecord old_record, RmRecord record, std::string table_name, Rid rid, TabMeta &tab, RmFileHandle *fh, Context *context)
{
    /*处理索引*/
    /*预检查并构建索引键*/
    std::vector<std::pair<IxIndexHandle*, std::unique_ptr<char[]>>> pending_keys;
    pending_keys.reserve(tab.indexes.size());  // 预留空间避免扩容

    for (const auto& index : tab.indexes) {
        const std::string index_name = sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols);
        auto* ih = sm_manager_->ihs_.at(index_name).get();
        auto key = std::make_unique<char[]>(index.col_tot_len);
        int offset = 0;
        for (int i = 0; i < index.col_num; ++i) {
            const auto& col = index.cols[i];
            memcpy(key.get() + offset, record.data + col.offset, col.len);
            offset += col.len;
        }

        // 索引预插入检查
        ih->insert_entry(key.get(), Rid{-1, -1}, context->txn_);  // 临时测试插入
        ih->delete_entry(key.get(), context->txn_);               // 清理临时记录
        pending_keys.emplace_back(ih, std::move(key));
    }

    // record.showrecord(tab, rid);
    // std::fstream outfile;
    // outfile.open("RID.txt", std::ios::out | std::ios::app);
    // outfile << rid.page_no << " " << rid.slot_no << std::endl;
    
    fh->insert_record(rid,record.data);

    // 插入真实索引记录
    for (auto& [ih, key] : pending_keys) {
        ih->insert_entry(key.get(), rid, context->txn_);
        // outfile << rid.page_no << " " <<  rid.slot_no << std::endl;
    }

    // outfile.close();

    /*处理版本链表*/
    /*8.新建版本链*/
    UndoLink undo_link;

    /*8.插入一个版本连非法的undolink*/
    UpdateUndoLink(fh->GetFd(), rid, std::make_optional(undo_link));
}

void TransactionManager::commit_delete(RmRecord old_record, RmRecord record, std::string table_name, Rid rid, TabMeta &tab, RmFileHandle *fh, Context *context)
{
    /*首先获取元组的元数据*/
    auto tuplemeta = fh->get_tuple_meta(rid);
    std::vector<bool> modifiedvalue_bool(tab.cols.size(), true);
    std::vector<Value> modifiedvalue_value;
    ExtractValuesFromRecord(tab.cols, &old_record, modifiedvalue_value);

    /*该元组被本事务写过*/
    /*这块是构建一个undolog并和该元组的先前版本串起来*/
    auto prev_undolink = GetUndoLink(fh->GetFd(), rid);
    UndoLink undo_link_value = prev_undolink.value_or(UndoLink{});
    UndoLog undo_log{false, modifiedvalue_bool, modifiedvalue_value, nullptr, tuplemeta.ts_, undo_link_value};
    /*将该undolog放到版本连的第一位*/
    UndoLink undolink{context->txn_->AppendUndoLog(undo_log)};
    UpdateUndoLink(fh->GetFd(), rid, std::make_optional(undolink));

    // // 删除相关索引
    // for (const auto& index : tab.indexes) {
    //     // 获取索引句柄
    //     const std::string index_name = sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols);
    //     auto* ih = sm_manager_->ihs_.at(index_name).get();
    //     // 构造索引键
    //     char* key = new char[index.col_tot_len];
    //     int offset = 0;
    //     for (const auto& col : index.cols) {
    //         memcpy(key + offset, record.data + col.offset, col.len);
    //         offset += col.len;
    //     }
    //     // 执行索引删除
    //     ih->delete_entry(key, context->txn_);
    //     delete[] key;  // 及时释放动态分配的内存
    // }
}
