/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "sm_manager.h"

#include <sys/stat.h>
#include <unistd.h>

#include <fstream>

#include "index/ix.h"
#include "record/rm.h"
#include "record_printer.h"
#include "transaction/transaction_manager.h"
#include "recovery/log_manager.h"

/**
 * @description: 判断是否为一个文件夹
 * @return {bool} 返回是否为一个文件夹
 * @param {string&} db_name 数据库文件名称，与文件夹同名
 */
bool SmManager::is_dir(const std::string&   db_name) {
    struct stat st;
    return stat(db_name.c_str(), &st) == 0 && S_ISDIR(st.st_mode);
}

/**
 * @description: 创建数据库，所有的数据库相关文件都放在数据库同名文件夹下
 * @param {string&} db_name 数据库名称
 */
void SmManager::create_db(const std::string& db_name) {
    if (is_dir(db_name)) {
        throw DatabaseExistsError(db_name);
    }
    //为数据库创建一个子目录
    std::string cmd = "mkdir " + db_name;
    if (system(cmd.c_str()) < 0) {  // 创建一个名为db_name的目录
        throw UnixError();
    }
    if (chdir(db_name.c_str()) < 0) {  // 进入名为db_name的目录
        throw UnixError();
    }
    //创建系统目录
    DbMeta *new_db = new DbMeta();
    new_db->name_ = db_name;

    // 注意，此处ofstream会在当前目录创建(如果没有此文件先创建)和打开一个名为DB_META_NAME的文件
    std::ofstream ofs(DB_META_NAME);

    // 将new_db中的信息，按照定义好的operator<<操作符，写入到ofs打开的DB_META_NAME文件中
    ofs << *new_db;  // 注意：此处重载了操作符<<

    delete new_db;

    // 创建日志文件
    disk_manager_->create_file(LOG_FILE_NAME);

    // 回到根目录
    if (chdir("..") < 0) {
        throw UnixError();
    }
}

/**
 * @description: 删除数据库，同时需要清空相关文件以及数据库同名文件夹
 * @param {string&} db_name 数据库名称，与文件夹同名
 */
void SmManager::drop_db(const std::string& db_name) {
    if (!is_dir(db_name)) {
        throw DatabaseNotFoundError(db_name);
    }
    std::string cmd = "rm -r " + db_name;
    if (system(cmd.c_str()) < 0) {
        throw UnixError();
    }
    flush_meta();
}

/**
 * @description: 打开数据库，找到数据库对应的文件夹，并加载数据库元数据和相关文件
 * @param {string&} db_name 数据库名称，与文件夹同名
 */
void SmManager::open_db(const std::string& db_name) {
    if (!is_dir(db_name)) {
        throw DatabaseNotFoundError(db_name);
    }
    if (chdir(db_name.c_str()) < 0) {  // 进入名为db_name的目录
        throw UnixError();
    }
    // 打开数据库元数据文件
    std::ifstream ifs(DB_META_NAME);
    ifs >> db_;  // 注意：此处重载了操作符>>
    if (!ifs) {
        throw RMDBError("Failed to read database metadata from " + DB_META_NAME);
    }
    for(auto &entry : db_.tabs_) {
        const std::string &tab_name = entry.first;
        TabMeta &tab_meta = entry.second;
        fhs_.emplace(tab_name, rm_manager_->open_file(tab_name));
        for (const auto &index : tab_meta.indexes) {
            ihs_.emplace(ix_manager_->get_index_name(tab_name,index.cols), ix_manager_->open_index(tab_name, index.cols));
        }
    }
}

/**
 * @description: 把数据库相关的元数据刷入磁盘中
 */
void SmManager::flush_meta() {
    // 默认清空文件
    std::ofstream ofs(DB_META_NAME);
    ofs << db_;
}

/**
 * @description: 关闭数据库并把数据落盘
 */
void SmManager::close_db() {
        std::ofstream ofs(DB_META_NAME);
    ofs<<db_;
    db_.name_.clear();
    db_.tabs_.clear();
    // Close all record files
    for (auto &entry : fhs_) {
        rm_manager_->close_file(entry.second.get());
    }
	fhs_.clear();
	// Close all index files
    for(auto &entry:ihs_){
        ix_manager_->close_index(entry.second.get());
    }
	ihs_.clear();
    if (chdir("..") < 0) {
        throw UnixError();
    }
    
}

/**
 * @description: 显示所有的表,通过测试需要将其结果写入到output.txt,详情看题目文档
 * @param {Context*} context 
 */
void SmManager::show_tables(Context* context) {
    std::fstream outfile;
    if(context->iswrite)
    {
        outfile.open("output.txt", std::ios::out | std::ios::app);
        outfile << "| Tables |\n";
    }
    RecordPrinter printer(1);
    printer.print_separator(context);
    printer.print_record({"Tables"}, context);
    printer.print_separator(context);
    for (auto &entry : db_.tabs_) {
        auto &tab = entry.second;
        printer.print_record({tab.name}, context);
        if(context->iswrite)
        {
            outfile << "| " << tab.name << " |\n";
        }
    }
    printer.print_separator(context);\
    if(context->iswrite)
    {
        outfile.close();
    }
}

/**
 * @description: 显示表的元数据
 * @param {string&} tab_name 表名称
 * @param {Context*} context 
 */
void SmManager::desc_table(const std::string& tab_name, Context* context) {
    TabMeta &tab = db_.get_table(tab_name);

    std::vector<std::string> captions = {"Field", "Type", "Index"};
    RecordPrinter printer(captions.size());
    // Print header
    printer.print_separator(context);
    printer.print_record(captions, context);
    printer.print_separator(context);
    // Print fields
    for (auto &col : tab.cols) {
        std::vector<std::string> field_info = {col.name, coltype2str(col.type), col.index ? "YES" : "NO"};
        printer.print_record(field_info, context);
    }
    // Print footer
    printer.print_separator(context);
}

/**
 * @description: 创建表
 * @param {string&} tab_name 表的名称
 * @param {vector<ColDef>&} col_defs 表的字段
 * @param {Context*} context 
 */
void SmManager::create_table(const std::string& tab_name, const std::vector<ColDef>& col_defs, Context* context) {
    if (db_.is_table(tab_name)) {
        throw TableExistsError(tab_name);
    }
    // Create table meta
    int curr_offset = 0;
    TabMeta tab;
    tab.name = tab_name;
    for (auto &col_def : col_defs) {
        ColMeta col = {.tab_name = tab_name,
                       .name = col_def.name,
                       .type = col_def.type,
                       .len = col_def.len,
                       .offset = curr_offset,
                       .index = false};
        curr_offset += col_def.len;
        tab.cols.push_back(col);
    }
    // Create & open record file
    int record_size = curr_offset;  // record_size就是col meta所占的大小（表的元数据也是以记录的形式进行存储的）
    rm_manager_->create_file(tab_name, record_size);
    db_.tabs_[tab_name] = tab;
    // fhs_[tab_name] = rm_manager_->open_file(tab_name);
    fhs_.emplace(tab_name, rm_manager_->open_file(tab_name));

    flush_meta();
}

/**
 * @description: 删除表
 * @param {string&} tab_name 表的名称
 * @param {Context*} context
 */
void SmManager::drop_table(const std::string& tab_name, Context* context) {
    TabMeta &tab = db_.get_table( tab_name );
    if (!db_.is_table(tab_name)) {
        throw TableNotFoundError(tab_name);
    }

    /*删除缓冲池中与该表有关的页*/
    // PageId
    RmFileHandle *fh = fhs_[tab_name].get();
    auto f_h_ = fh->get_file_hdr();
    // buffer_pool_manager_->unpin_page()
    for(int page_no = 0;page_no < f_h_.num_pages; page_no++)
    {
        /*解pin，直到pin为0*/
        bool is_success = buffer_pool_manager_->unpin_page({fh->GetFd(),page_no},false);
        while(is_success)
        {
            is_success = buffer_pool_manager_->unpin_page({fh->GetFd(),page_no},false);
        }
        /*删除该页*/
        buffer_pool_manager_->delete_page({fh->GetFd(),page_no});
    }

    // close & delete record file
    rm_manager_->close_file( fhs_.at(tab_name).get());
    rm_manager_->destroy_file( tab_name );

    for(auto i:tab.indexes) {
        drop_index(tab_name, i.cols, context);
    }
    tab.indexes.clear();
    db_.tabs_.erase( tab_name );
    fhs_.erase( tab_name );
    flush_meta();
}
/**
 * @description: 创建索引
 * @param {string&} tab_name 表的名称
 * @param {vector<string>&} col_names 索引包含的字段名称
 * @param {Context*} context
 */
void SmManager::create_index(const std::string& tab_name, const std::vector<std::string>& col_names, Context* context) {
    TabMeta &tab = db_.get_table( tab_name );

    // 判断索引是否已经存在
    if (ix_manager_->exists(tab_name, col_names)) {
        throw IndexExistsError(tab_name,col_names);
    }
    
    // 创建索引
    std::vector<ColMeta> cols;
    for(auto &col_name:col_names) {
        cols.push_back( *tab.get_col(col_name) );
    }
    ix_manager_->create_index(tab_name, cols);

    // 打开索引文件
    auto ih = ix_manager_->open_index(tab_name, cols);

    // 计算索引的长度
    int col_tot_len = 0;
    for(auto &col:cols) {
        col_tot_len += col.len;
    }
    // 插入所有的记录
    auto file_handle = fhs_.at(tab_name).get();
    char key[col_tot_len];
    for(RmScan scan(file_handle); !scan.is_end(); scan.next()) {
        auto record = file_handle->get_record(scan.rid(),context);
        int offset = 0;
        for(size_t i = 0; i < cols.size(); ++i) {
            memcpy(key + offset, record.get()->data + cols[i].offset, cols[i].len);
            offset += cols[i].len;
        }
        ih->insert_entry(key, scan.rid(), context->txn_);      
    }

    // 更新表的元数据
    tab.indexes.push_back( IndexMeta{tab_name, col_tot_len, (int)cols.size(), cols} );
    // 更新ihs_
    ihs_.emplace(ix_manager_->get_index_name(tab_name, col_names), std::move(ih));
    // 将元数据刷新到磁盘
    flush_meta();
}

/**
 * @description: 删除索引
 * @param {string&} tab_name 表名称
 * @param {vector<string>&} col_names 索引包含的字段名称
 * @param {Context*} context
 */
void SmManager::drop_index(const std::string& tab_name, const std::vector<std::string>& col_names, Context* context) {
    // 判断索引是否存在
    if (!ix_manager_->exists(tab_name, col_names)) {
        throw IndexNotFoundError(tab_name,col_names);
    }
    std::string index_name = ix_manager_->get_index_name(tab_name, col_names);
     
    // 删除索引
    ix_manager_->close_index( ihs_.at(index_name).get() );
    ix_manager_->destroy_index(tab_name, col_names);
    // 更新表的元数据
    TabMeta &tab = db_.get_table( tab_name );
    tab.indexes.erase(tab.get_index_meta(col_names));
    // 更新ihs_
    ihs_.erase(index_name);
    flush_meta();
}

/**
 * @description: 删除索引
 * @param {string&} tab_name 表名称
 * @param {vector<ColMeta>&} 索引包含的字段元数据
 * @param {Context*} context
 */
void SmManager::drop_index(const std::string& tab_name, const std::vector<ColMeta>& cols, Context* context) {
    std::vector<std::string> col_names;
    for(auto &col:cols) {
        col_names.push_back(col.name);
    }
    drop_index(tab_name, col_names, context);
}

void SmManager::add_alia(std::string tablename, std::string alia)
{
    db_.alias_.insert(std::make_pair(tablename,alia));
}

bool SmManager::get_real_table_name(std::string *tablename, std::string alia)
{
    auto it = db_.alias_.begin();
    while(it != db_.alias_.end())
    {
        if(it->second == alia)
        {
            *tablename = it->first;
            return true;
        }
        it++;
    }
    return false;
}
void SmManager::show_index(const std::string& tab_name, Context* context) {
    // 打开output.txt文件进行追加写入
    std::fstream outfile;
    if(context->iswrite)
    {
        outfile.open("output.txt", std::ios::out | std::ios::app);
    }
    TabMeta &tab = db_.get_table(tab_name);
    std::vector<std::string> captions = {"Table", "Unique", "Columns"};
    RecordPrinter printer(captions.size());
    // 打印表头和分隔线
    // printer.print_separator(context);
    // printer.print_record(captions, context);
    // printer.print_separator(context);
    // 遍历表的所有索引
    for (const auto &index : tab.indexes) {
        // 构建列名字符串，格式为"(col1, col2, ...)"
        std::string cols_str = "(";
        for (size_t i = 0; i < index.cols.size(); ++i) {
            cols_str += index.cols[i].name;
            if (i < index.cols.size() - 1) {
                cols_str += ",";
            }
        }
        cols_str += ")";
        // 打印索引信息
        std::vector<std::string> index_info = {tab_name, "unique", cols_str};
        printer.print_record(index_info, context);
        if(context->iswrite)
        {
            outfile << "| " << tab_name << " | unique | " << cols_str << " |\n";
        }
    }
    if(context->iswrite)
    {
        // 打印底部分隔线
        // printer.print_separator(context);
        outfile.close();
    }
}
void SmManager::redo_index(const std::string &tab_name, TabMeta &table_meta, const std::vector<std::string> &col_names,
                           const std::string &file_name, Context *context) {
    ix_manager_->close_index(ihs_[file_name].get());
    ix_manager_->destroy_index(file_name,col_names);

    std::vector<ColMeta> col_metas;
    col_metas.reserve(col_names.size());
    auto total_len = 0;
    for (auto &col_name: col_names) {
        col_metas.emplace_back(*table_meta.get_col(col_name));
        total_len += col_metas.back().len;
    }

    // auto ix_name = std::move(ix_manager_->get_index_name(tab_name, col_names));
    ix_manager_->create_index(file_name, col_metas);
    auto &&ih = ix_manager_->open_index(file_name, col_metas);
    auto &&fh = fhs_[tab_name];

    int offset = 0;
    char *key = new char[total_len];
    for (auto &&scan = std::make_unique<RmScan>(fh.get()); !scan->is_end(); scan->next()) {
        auto &&rid = scan->rid();
        auto &&record = fh->get_record(rid, context);
        offset = 0;
        for (auto &col_meta: col_metas) {
            memcpy(key + offset, record->data + col_meta.offset, col_meta.len);
            offset += col_meta.len;
        }
        // 插入B+树
        if (ih->insert_entry(key, rid, context->txn_) == IX_NO_PAGE) {
            // 释放内存
            delete []key;
            // 重复了
            ix_manager_->close_index(ih.get());
            ix_manager_->destroy_index(file_name, col_metas);
            // drop_index(tab_name, col_names, context);
            throw NonUniqueIndexError(tab_name, col_names);
        }
    }
    // 释放内存
    delete []key;

    // 插入索引句柄
    ihs_[file_name] = std::move(ih);
}

/**
 * @description: 创建静态检查点
 * @param {Context*} context 上下文信息
 */
void SmManager::create_static_checkpoint(Context* context) {
    // 1. 停止接收新事务和正在运行的事务
    // 注意：在实际实现中，这里需要与事务管理器协调，暂停所有事务处理

    // 2. 获取当前所有活跃事务的ID列表
    std::vector<txn_id_t> active_txns = TransactionManager::get_active_txn_ids();
    std::cout << "Creating checkpoint with " << active_txns.size() << " active transactions" << std::endl;

    // 3. 将仍保留在日志缓冲区中的内容写到日志文件中
    if (context->log_mgr_) {
        context->log_mgr_->flush_log_to_disk();
    }

    // 4. 在日志文件中写入一个"检查点记录"
    CheckpointLogRecord* checkpoint_record = new CheckpointLogRecord(active_txns);
    lsn_t checkpoint_lsn = INVALID_LSN;
    if (context->log_mgr_) {
        checkpoint_lsn = context->log_mgr_->add_log_to_buffer(checkpoint_record);
        context->log_mgr_->flush_log_to_disk();
    }

    // 5. 将当前数据库缓冲区中的内容写到数据库中
    // 刷新所有脏页到磁盘
    if (buffer_pool_manager_) {
        // 遍历所有打开的文件句柄，刷新对应的页面
        for (const auto& fh_pair : fhs_) {
            auto& fh = fh_pair.second;
            // 获取文件的所有页面并刷新
            int fd = fh->GetFd();

            // 强制刷新该文件的所有页面
            buffer_pool_manager_->flush_all_pages(fd);
        }

        // 刷新索引文件的页面
        for (const auto& ih_pair : ihs_) {
            auto& ih = ih_pair.second;
            int fd = ih->GetFd();
            buffer_pool_manager_->flush_all_pages(fd);
        }

        // 强制写入所有表文件的文件头到磁盘
        for (const auto& fh_pair : fhs_) {
            auto& fh = fh_pair.second;
            int fd = fh->GetFd();
            // 获取文件头并写入磁盘
            RmFileHdr file_hdr = fh->get_file_hdr();
            disk_manager_->write_page(fd, RM_FILE_HDR_PAGE, (char *)&file_hdr, sizeof(file_hdr));
        }
    }

    // 6. 把日志文件中检查点记录的地址写到"重新启动文件"中
    if (checkpoint_lsn != INVALID_LSN) {
        disk_manager_->write_restart_file(checkpoint_lsn);
    }

    // checkpoint_record已经在add_log_to_buffer中被删除了，不需要再次删除

    // 输出成功信息到客户端
    std::string success_msg = "Static checkpoint created successfully.\n";
    memcpy(context->data_send_ + *(context->offset_), success_msg.c_str(), success_msg.length());
    *(context->offset_) += success_msg.length();
}

