/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "defs.h"
#include "storage/buffer_pool_manager.h"
#include "system/sm_meta.h"

constexpr int RM_NO_PAGE = -1;
constexpr int RM_FILE_HDR_PAGE = 0;
constexpr int RM_FIRST_RECORD_PAGE = 1;
constexpr int RM_MAX_RECORD_SIZE = 512;

struct TupleMeta {
    timestamp_t ts_;
    bool is_deleted_;

    friend auto operator==(const TupleMeta &a, const TupleMeta &b) {
        return a.ts_ == b.ts_ && a.is_deleted_ == b.is_deleted_;
    }

    friend auto operator!=(const TupleMeta &a, const TupleMeta &b) { return !(a == b); }
};

/* 文件头，记录表数据文件的元信息，写入磁盘中文件的第0号页面 */
struct RmFileHdr {
    int record_size;            // 表中每条记录的大小，由于不包含变长字段，因此当前字段初始化后保持不变
    int num_pages;              // 文件中分配的页面个数（初始化为1）
    int num_records_per_page;   // 每个页面最多能存储的元组个数
    int first_free_page_no;     // 文件中当前第一个包含空闲空间的页面号（初始化为-1）
    int bitmap_size;            // 每个页面bitmap大小
};

/* 表数据文件中每个页面的页头，记录每个页面的元信息 */
struct RmPageHdr {
    int next_free_page_no;  // 当前页面满了之后，下一个包含空闲空间的页面号（初始化为-1）
    int num_records;        // 当前页面中当前已经存储的记录个数（初始化为0）
};

/* 表中的记录 */
struct RmRecord {
    char* data;  // 记录的数据
    int size;    // 记录的大小
    bool allocated_ = false;    // 是否已经为数据分配空间

    RmRecord() = default;

    RmRecord(const RmRecord& other) {
        size = other.size;
        data = new char[size];
        memcpy(data, other.data, size);
        allocated_ = true;
    };


    RmRecord &operator=(const RmRecord& other) {
        size = other.size;
        data = new char[size];
        memcpy(data, other.data, size);
        allocated_ = true;
        return *this;
    };

    RmRecord(int size_) {
        size = size_;
        data = new char[size_];
        allocated_ = true;
    }

    RmRecord(int size_, char* data_) {
        size = size_;
        data = new char[size_];
        memcpy(data, data_, size_);
        allocated_ = true;
    }

    void SetData(char* data_) {
        memcpy(data, data_, size);
    }

    void Deserialize(const char* data_) {
        size = *reinterpret_cast<const int*>(data_);
        if(allocated_) {
            delete[] data;
        }
        data = new char[size];
        memcpy(data, data_ + sizeof(int), size);
    }

void showrecord(const TabMeta& tab, const Rid& rid)
{
    if(!allocated_)
    {
        throw InternalError("Record is NULL\n");
    }

    // 打开文件，使用追加模式，确保每次输出不会覆盖之前的内容
    std::ofstream debug_file("debug.txt", std::ios::app);
    if (!debug_file.is_open())
    {
        throw InternalError("Failed to open debug.txt for writing\n");
    }

    debug_file << rid.page_no << " " << rid.slot_no << std::endl;

    // 输出列名
    for(const auto& col : tab.cols)
    {
        debug_file << col.name << "      ";
    }
    debug_file << std::endl;

    // 输出列值
    for(const auto& col : tab.cols)
    {
        switch(col.type)
        {
            case TYPE_INT:
            {
                int value = *reinterpret_cast<const int*>(data + col.offset);
                debug_file << value << "      ";
                break;
            }
            case TYPE_FLOAT:
            {
                float fvalue = *reinterpret_cast<const float*>(data + col.offset);
                debug_file << fvalue << "      ";
                break;
            }
            case TYPE_STRING:
            {
                debug_file << (data + col.offset) << "      ";
                break;
            }
        }
    }
    debug_file << std::endl;

    // 关闭文件（离开作用域时会自动关闭，但显式关闭更清晰）
    debug_file.close();
}
    
    ~RmRecord() {
        if(allocated_) {
            delete[] data;
        }
        allocated_ = false;
        data = nullptr;
    }
};
