/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "rm_file_handle.h"

/**
 * @description: 获取当前表中记录号为rid的记录
 * @param {Rid&} rid 记录号，指定记录的位置
 * @param {Context*} context
 * @return {unique_ptr<RmRecord>} rid对应的记录对象指针
 */
std::unique_ptr<RmRecord> RmFileHandle::get_record(const Rid& rid, Context* context) const {
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    char* record_data = page_handle.get_slot(rid.slot_no);
    auto record = std::make_unique<RmRecord>(file_hdr_.record_size, record_data);
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
    return record;
}

/**
 * @description: 在当前表中插入一条记录，不指定插入位置
 * @param {char*} buf 要插入的记录的数据
 * @param {Context*} context
 * @return {Rid} 插入的记录的记录号（位置）
 */
Rid RmFileHandle::insert_record(char* buf, Context* context) {
    std::lock_guard<std::mutex> lock1(set_mutex); // 加锁
    /*1.获取当前未满的页的page handle*/
    RmPageHandle page_handle = create_page_handle();
    
     std::lock_guard<std::mutex> lock(*page_handle.bitmap_mutex_ptr);  // 新增：保护位图修改

    const int num_records_per_page = file_hdr_.num_records_per_page;
    const int record_size = file_hdr_.record_size;

    /*2.获取第一个空槽*/
    const int first_free_slot_no = Bitmap::first_bit(false, page_handle.bitmap, num_records_per_page);
    if (first_free_slot_no < num_records_per_page) {
        char* tuple_ptr = page_handle.get_slot(first_free_slot_no);
        memcpy(tuple_ptr, buf, record_size);
        Bitmap::set(page_handle.bitmap, first_free_slot_no);
    }

    Rid rid{page_handle.page->get_page_id().page_no, first_free_slot_no};
    
    /*3.写INSERT日志 (WAL原则)*/
    if (context && context->log_mgr_) {
        RmRecord temp_record(record_size, buf);
        InsertLogRecord* insert_log = new InsertLogRecord(
            context->txn_->get_transaction_id(), 
            temp_record, 
            rid, 
            disk_manager_->get_file_name(fd_)
        );
        insert_log->prev_lsn_ = context->txn_->get_prev_lsn();
        lsn_t lsn = context->log_mgr_->add_log_to_buffer(insert_log);
        context->txn_->set_prev_lsn(lsn);
    }

    /*4.更新page_hdr*/
    page_handle.page_hdr->num_records++;

    /*5.处理插入后页面已满的情况*/
    // if (Bitmap::next_bit(false, page_handle.bitmap, num_records_per_page, first_free_slot_no) == num_records_per_page) {
    //     if (page_handle.page_hdr->next_free_page_no != INVALID_PAGE_ID) {
    //         file_hdr_.first_free_page_no = page_handle.page_hdr->next_free_page_no;
    //     } else {
    //         file_hdr_.first_free_page_no = INVALID_PAGE_ID;
    //         buffer_pool_manager_->unpin_page(create_new_page_handle().page->get_page_id(), true);
    //     }
    // }

    // if (Bitmap::next_bit(false, page_handle.bitmap, num_records_per_page, first_free_slot_no) == num_records_per_page) {
    //     mark_page_full(page_handle.page->get_page_id().page_no);
    //     if(get_first_free_page_no() == INVALID_PAGE_ID)
    //     {
    //         /*没有空闲页，则新建*/
    //         create_new_page_handle();
    //     }
    // }

    if (Bitmap::next_bit(false, page_handle.bitmap, num_records_per_page, first_free_slot_no) == num_records_per_page) {
        has_free_slot_pages.erase(page_handle.page->get_page_id().page_no);
        // if(get_first_free_page_no() == INVALID_PAGE_ID)
        // {
        //     /*没有空闲页，则新建*/
        //     create_new_page_handle();
        // }
    }

    const page_id_t page_no = page_handle.page->get_page_id().page_no;
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "insert record dirty " << page_no <<std::endl;
    // outfile.close();
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
    /*6.加入insertedrid列表*/
    // insertedRids.push_back(rid);
    // insertedRawRids.push_back(rid);


    return Rid{page_no, first_free_slot_no};
}

/**
 * @description: 在当前表中的指定位置插入一条记录
 * @param {Rid&} rid 要插入记录的位置
 * @param {char*} buf 要插入记录的数据
 */
void RmFileHandle::insert_record(const Rid& rid, char* buf) {
    
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    std::lock_guard<std::mutex> lock(*page_handle.bitmap_mutex_ptr);  // 新增：保护位图修改

    char* tuple_ptr = page_handle.get_slot(rid.slot_no);
    memcpy(tuple_ptr, buf, file_hdr_.record_size);
    Bitmap::set(page_handle.bitmap, rid.slot_no);
    page_handle.page_hdr->num_records++;
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "insert record1 dirty " << page_handle.page->get_page_id().page_no 
    // << " " << rid.page_no <<std::endl;
    // outfile << page_handle.page->is_dirty() <<std::endl;
    // outfile.close();
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);

    
}

/**
 * @description: 删除记录文件中记录号为rid的记录
 * @param {Rid&} rid 要删除的记录的记录号（位置）
 * @param {Context*} context
 */
void RmFileHandle::delete_record(const Rid& rid, Context* context) {
    std::lock_guard<std::mutex> lock1(set_mutex); // 加锁
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    
    std::lock_guard<std::mutex> lock(*page_handle.bitmap_mutex_ptr);  // 新增：保护位图修改

    /*1.写DELETE日志 (WAL原则)*/
    if (context && context->log_mgr_) {
        auto record = get_record(rid, context);
        Rid rid_copy = rid;
        DeleteLogRecord* delete_log = new DeleteLogRecord(
            context->txn_->get_transaction_id(), 
            *record, 
            rid_copy, 
            disk_manager_->get_file_name(fd_)
        );
        delete_log->prev_lsn_ = context->txn_->get_prev_lsn();
        lsn_t lsn = context->log_mgr_->add_log_to_buffer(delete_log);
        context->txn_->set_prev_lsn(lsn);
    }
    
    /*2.清除记录数据并更新位图*/
    char* tuple_ptr = page_handle.get_slot(rid.slot_no);
    memset(tuple_ptr, 0, file_hdr_.record_size);
    Bitmap::reset(page_handle.bitmap, rid.slot_no);

    /*3.页面从满变未满时更新空闲链表*/
    // if (file_hdr_.first_free_page_no != rid.page_no) {
        // release_page_handle1(page_handle);
        // mark_page_has_free_slot(rid.page_no);
        
        has_free_slot_pages.emplace(rid.page_no);
    // }
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "delete record dirty " << page_handle.page->get_page_id().page_no <<std::endl;
    // outfile.close();
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
    
    
}

/**
 * @description: 删除记录文件中记录号为rid的记录
 * @param {Rid&} rid 要删除的记录的记录号（位置）
 * @param {Context*} context
 */
void RmFileHandle::delete_record1(const Rid& rid, Context* context) {
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
     std::lock_guard<std::mutex> lock(*page_handle.bitmap_mutex_ptr);  // 新增：保护位图修改
    /*1.写DELETE日志 (WAL原则)*/
    if (context && context->log_mgr_) {
        auto record = get_record(rid, context);
        Rid rid_copy = rid;
        DeleteLogRecord* delete_log = new DeleteLogRecord(
            context->txn_->get_transaction_id(), 
            *record, 
            rid_copy, 
            disk_manager_->get_file_name(fd_)
        );
        delete_log->prev_lsn_ = context->txn_->get_prev_lsn();
        lsn_t lsn = context->log_mgr_->add_log_to_buffer(delete_log);
        context->txn_->set_prev_lsn(lsn);
    }
    
    /*2.清除记录数据并更新位图*/
    char* tuple_ptr = page_handle.get_slot(rid.slot_no);
    memset(tuple_ptr, 0, file_hdr_.record_size);
    Bitmap::reset(page_handle.bitmap, rid.slot_no);

    // /*3.页面从满变未满时更新空闲链表*/
    // if (file_hdr_.first_free_page_no != rid.page_no) {
    //     release_page_handle(page_handle);
    // }
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "delete record1 dirty " << page_handle.page->get_page_id().page_no <<std::endl;
    // outfile.close();
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
    
}

/**
 * @description: 更新记录文件中记录号为rid的记录
 * @param {Rid&} rid 要更新的记录的记录号（位置）
 * @param {char*} buf 新记录的数据
 * @param {Context*} context
 */
void RmFileHandle::update_record(const Rid& rid, char* buf, Context* context) {
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    /*1.写UPDATE日志 (WAL原则)*/
    if (context && context->log_mgr_) {
        auto old_record = get_record(rid, context);
        RmRecord new_record(file_hdr_.record_size, buf);
        Rid rid_copy = rid;
        UpdateLogRecord* update_log = new UpdateLogRecord(
            context->txn_->get_transaction_id(), 
            *old_record, 
            new_record, 
            rid_copy, 
            disk_manager_->get_file_name(fd_)
        );
        update_log->prev_lsn_ = context->txn_->get_prev_lsn();
        lsn_t lsn = context->log_mgr_->add_log_to_buffer(update_log);
        context->txn_->set_prev_lsn(lsn);
    }

    /*2.更新记录数据*/
    char* tuple_ptr = page_handle.get_slot(rid.slot_no);
    memcpy(tuple_ptr, buf, file_hdr_.record_size);
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "update record dirty " << page_handle.page->get_page_id().page_no <<std::endl;
    // outfile.close();
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
}

/**
 * @description: 获取指定页面的页面句柄
 * @param {int} page_no 页面号
 * @return {RmPageHandle} 指定页面的句柄
 */
RmPageHandle RmFileHandle::fetch_page_handle(int page_no) const {
    if (page_no == INVALID_PAGE_ID) {
        throw PageNotExistError(disk_manager_->get_file_name(fd_), page_no);
    }
    return RmPageHandle(&file_hdr_, buffer_pool_manager_->fetch_page({fd_, page_no}));
}

/**
 * @description: 创建一个新的page handle
 * @return {RmPageHandle} 新的PageHandle
 */
RmPageHandle RmFileHandle::create_new_page_handle() {
    PageId new_page_id{fd_, INVALID_PAGE_ID};
    Page* new_page = buffer_pool_manager_->new_page(&new_page_id);
    RmPageHandle new_page_handle{&file_hdr_, new_page};
    
    if (new_page) {
        // if (file_hdr_.first_free_page_no != INVALID_PAGE_ID) {
        //     RmPageHandle curr_handle = fetch_page_handle(file_hdr_.first_free_page_no);
        //     while (curr_handle.page_hdr->next_free_page_no != INVALID_PAGE_ID) {
        //         buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), false);
        //         curr_handle = fetch_page_handle(curr_handle.page_hdr->next_free_page_no);
        //     }
        //     new_page_handle.page_hdr->next_free_page_no = INVALID_PAGE_ID;
        //     curr_handle.page_hdr->next_free_page_no = new_page_id.page_no;
        //     buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), true);
        // } else {
        //     new_page_handle.page_hdr->next_free_page_no = INVALID_PAGE_ID;
        //     file_hdr_.first_free_page_no = new_page_id.page_no;
        // }
        file_hdr_.num_pages++;
        // // 扩容 bitmap 以覆盖新页面
        // expand_free_slot_bitmap(file_hdr_.num_pages);

        // // 新页面刚创建时一定有空闲槽位，标记到 bitmap 中
        // mark_page_has_free_slot(new_page_id.page_no);
        // std::lock_guard<std::mutex> lock1(set_mutex); // 加锁
        has_free_slot_pages.emplace(new_page_id.page_no);
    }
    else
    {
        printf("wedwdwdw\n");
    }

    return new_page_handle;
}


/**
 * @description: 创建一个新的page handle
 * @return {RmPageHandle} 新的PageHandle
 */
RmPageHandle RmFileHandle::create_new_page_handle1() {
    PageId new_page_id{fd_, INVALID_PAGE_ID};
    Page* new_page = buffer_pool_manager_->new_page(&new_page_id);
    RmPageHandle new_page_handle{&file_hdr_, new_page};
    
    if (new_page) {
        // if (file_hdr_.first_free_page_no != INVALID_PAGE_ID) {
        //     RmPageHandle curr_handle = fetch_page_handle(file_hdr_.first_free_page_no);
        //     while (curr_handle.page_hdr->next_free_page_no != INVALID_PAGE_ID) {
        //         buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), false);
        //         curr_handle = fetch_page_handle(curr_handle.page_hdr->next_free_page_no);
        //     }
        //     new_page_handle.page_hdr->next_free_page_no = INVALID_PAGE_ID;
        //     curr_handle.page_hdr->next_free_page_no = new_page_id.page_no;
        //     buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), true);
        // } else {
        //     new_page_handle.page_hdr->next_free_page_no = INVALID_PAGE_ID;
        //     file_hdr_.first_free_page_no = new_page_id.page_no;
        // }
        file_hdr_.num_pages++;
    }

    return new_page_handle;
}

/**
 * @brief 创建或获取一个空闲的page handle
 *
 * @return RmPageHandle 返回生成的空闲page handle
 * @note pin the page, remember to unpin it outside!
 */
RmPageHandle RmFileHandle::create_page_handle() {
    // file_hdr_.first_free_page_no = get_first_free_page_no();
    // if (file_hdr_.first_free_page_no != INVALID_PAGE_ID) {
    //     return fetch_page_handle(file_hdr_.first_free_page_no);
    // } else {
    //     return create_new_page_handle();
    // }
    
    if(has_free_slot_pages.empty())
    {
        buffer_pool_manager_->unpin_page(create_new_page_handle().page->get_page_id(), true);
    }
    // printf("123455678\n");
    file_hdr_.first_free_page_no = *(has_free_slot_pages.begin());
    return fetch_page_handle(file_hdr_.first_free_page_no);
}

/**
 * @description: 当一个页面从没有空闲空间的状态变为有空闲空间状态时，更新文件头和页头中空闲页面相关的元数据
 */
void RmFileHandle::release_page_handle(RmPageHandle& page_handle) {
    const page_id_t current_page_no = page_handle.page->get_page_id().page_no;
    const page_id_t old_first_free = file_hdr_.first_free_page_no;

    if (current_page_no < old_first_free) {
        file_hdr_.first_free_page_no = current_page_no;
        page_handle.page_hdr->next_free_page_no = old_first_free;
    } else {
        RmPageHandle curr_handle = fetch_page_handle(old_first_free);
        page_id_t next_free = curr_handle.page_hdr->next_free_page_no;

        while (next_free != INVALID_PAGE_ID) {
            if (next_free < current_page_no) {
                buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), false);
                curr_handle = fetch_page_handle(next_free);
                next_free = curr_handle.page_hdr->next_free_page_no;
            } else if (next_free == current_page_no) {
                buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), false);
                return;
            } else {
                break;
            }
        }

        curr_handle.page_hdr->next_free_page_no = current_page_no;
        page_handle.page_hdr->next_free_page_no = next_free;
        buffer_pool_manager_->unpin_page(curr_handle.page->get_page_id(), true);
    }
}

/**
 * @description: 当一个页面从没有空闲空间的状态变为有空闲空间状态时，更新文件头和页头中空闲页面相关的元数据
 */
void RmFileHandle::release_page_handle1(RmPageHandle& page_handle) {
    const page_id_t current_page_no = page_handle.page->get_page_id().page_no;
    const page_id_t old_first_free = file_hdr_.first_free_page_no;

    file_hdr_.first_free_page_no = current_page_no;
    page_handle.page_hdr->next_free_page_no = old_first_free;
}

int RmFileHandle::get_num_records() {
    int num_records = 0;
    for (page_id_t i = 1; i < file_hdr_.num_pages; ++i) {
        RmPageHandle page_handle = fetch_page_handle(i);
        num_records += page_handle.page_hdr->num_records;
        buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
    }
    return num_records;
}

Rid RmFileHandle::allocateRid()
{
    std::lock_guard<std::mutex> lock1(set_mutex); // 加锁
    /*1.获取当前未满的页的page handle*/
    RmPageHandle page_handle = create_page_handle();
    /*加页级锁*/
    std::lock_guard<std::mutex> lock(*page_handle.bitmap_mutex_ptr);  // 新增：保护位图修改

    const int num_records_per_page = file_hdr_.num_records_per_page;
    const int record_size = file_hdr_.record_size;

    /*2.获取第一个空槽*/
    const int first_free_slot_no = Bitmap::first_bit(false, page_handle.bitmap, num_records_per_page);
    Bitmap::set(page_handle.bitmap, first_free_slot_no);
    Rid rid{page_handle.page->get_page_id().page_no, first_free_slot_no};

    if (Bitmap::next_bit(false, page_handle.bitmap, num_records_per_page, first_free_slot_no) == num_records_per_page) {
        has_free_slot_pages.erase(page_handle.page->get_page_id().page_no);
    }
    // std::fstream outfile;
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "allocate dirty " << page_handle.page->get_page_id().page_no 
    // << " " << first_free_slot_no <<std::endl;
    // outfile << page_handle.page->is_dirty() <<std::endl;
    // outfile.close();
    const page_id_t page_no = page_handle.page->get_page_id().page_no;
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);

    return Rid{page_no, first_free_slot_no};
}

void RmFileHandle::deAllocateRid(const Rid& rid, Context* context)
{
    std::lock_guard<std::mutex> lock1(set_mutex); // 加锁
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    /*加页级锁*/
    std::lock_guard<std::mutex> lock(*page_handle.bitmap_mutex_ptr);  // 自动释放锁，避免泄漏
    // std::fstream outfile;
    // outfile.open("deallocate.txt",std::ios::out | std::ios::app);
    // outfile << "deallocate dirty " << rid.page_no << " " << rid.slot_no << " " << file_hdr_.num_records_per_page <<std::endl;
    // outfile.close();
    /*2.清除记录数据并更新位图*/
    char* tuple_ptr = page_handle.get_slot(rid.slot_no);
    memset(tuple_ptr, 0, file_hdr_.record_size);
    Bitmap::reset(page_handle.bitmap, rid.slot_no);

    /*3.页面从满变未满时更新空闲链表*/
    // if (file_hdr_.first_free_page_no != rid.page_no) {
    // std::lock_guard<std::mutex> lock1(set_mutex); // 加锁
        has_free_slot_pages.emplace(rid.page_no);
    // }
    // outfile.open("PID.txt",std::ios::out | std::ios::app);
    // outfile << "deallocate dirty " << page_handle.page->get_page_id().page_no 
    // << " " << rid.slot_no << std::endl;
    // outfile << page_handle.page->is_dirty() <<std::endl;
    // outfile.close();
    /*解页级锁*/
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
}