/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <assert.h>

#include <memory>

#include "bitmap.h"
#include "common/context.h"
#include "rm_defs.h"

class RmManager;

/* 对表数据文件中的页面进行封装 */
struct RmPageHandle {
    const RmFileHdr *file_hdr;  // 当前页面所在文件的文件头指针
    Page *page;                 // 页面的实际数据，包括页面存储的数据、元信息等
    RmPageHdr *page_hdr;        // page->data的第一部分，存储页面元信息，指针指向首地址，长度为sizeof(RmPageHdr)
    char *bitmap;               // page->data的第二部分，存储页面的bitmap，指针指向首地址，长度为file_hdr->bitmap_size
    char *slots;                // page->data的第三部分，存储表的记录，指针指向首地址，每个slot的长度为file_hdr->record_size
    std::shared_ptr<std::mutex> bitmap_mutex_ptr;
    RmPageHandle(const RmFileHdr *fhdr_, Page *page_) : file_hdr(fhdr_), page(page_),
    bitmap_mutex_ptr(std::make_shared<std::mutex>()){
        page_hdr = reinterpret_cast<RmPageHdr *>(page->get_data() + page->OFFSET_PAGE_HDR);
        bitmap = page->get_data() + sizeof(RmPageHdr) + page->OFFSET_PAGE_HDR;
        slots = bitmap + file_hdr->bitmap_size;
    }
    

    // 返回指定slot_no的slot存储收地址
    char* get_slot(int slot_no) const {
        return slots + slot_no * file_hdr->record_size;  // slots的首地址 + slot个数 * 每个slot的大小(每个record的大小)
    }

    // 返回空槽
    int get_empty_slot()
    {
        int slot_no = -1;
        char *ptr = bitmap;
        for(int i=0;i<file_hdr->bitmap_size;i++)
        {
            if(*ptr == 0)
            {
                slot_no = i;
                break;
            }
            ptr++;
        }
        return slot_no;
    }

    // 返回从slot_no开始的首个非空槽
    int get_unempty_slot(int start_slot_no)
    {
        int slot_no = -1;
        char *ptr = bitmap + start_slot_no;
        for(int i=start_slot_no;i<file_hdr->bitmap_size;i++)
        {
            if(*ptr == 1)
            {
                slot_no = i;
                break;
            }
            ptr++;
        }
        return slot_no;
    }

    // 设置特定位置的槽的值
    void set_bitmap_value(int slot_no,char value)
    {
        bitmap[slot_no] = value;
    }
};

/* 每个RmFileHandle对应一个表的数据文件，里面有多个page，每个page的数据封装在RmPageHandle中 */
class RmFileHandle {
    friend class RmScan;
    friend class RmManager;

   private:
    DiskManager *disk_manager_;
    BufferPoolManager *buffer_pool_manager_;
    int fd_;        // 打开文件后产生的文件句柄
    RmFileHdr file_hdr_;    // 文件头，维护当前表文件的元数据
    std::unordered_map<Rid,TupleMeta> tuple_info_;
    mutable std::mutex tuple_mutex_;  //  mutable 允许 const 成员函数加锁
    int last_free_page_no{INVALID_PAGE_ID};
    std::mutex set_mutex; // 保护 has_free_slot_pages 的互斥锁
    std::unordered_set<int> has_free_slot_pages;
    std::vector<Rid> insertedRids;
    std::vector<Rid> insertedRawRids;
    std::mutex global_mutex_;  // 添加全局互斥锁

    // 新增：跟踪页面是否有空闲槽位的 bitmap
    std::vector<char> free_slot_pages_bitmap_;  // bitmap 原始数据存储
    int bitmap_total_bits_;                     // 当前 bitmap 可覆盖的总页数（总位数）
    static constexpr int BITMAP_WIDTH = 8;      // 假设 Bitmap 类中 BITMAP_WIDTH 为 8（1字节=8位）
    
   public:
    RmFileHandle(DiskManager *disk_manager, BufferPoolManager *buffer_pool_manager, int fd)
        : disk_manager_(disk_manager), buffer_pool_manager_(buffer_pool_manager), fd_(fd) {
        // 注意：这里从磁盘中读出文件描述符为fd的文件的file_hdr，读到内存中
        // 这里实际就是初始化file_hdr，只不过是从磁盘中读出进行初始化
        // init file_hdr_
        disk_manager_->read_page(fd, RM_FILE_HDR_PAGE, (char *)&file_hdr_, sizeof(file_hdr_));
        // disk_manager管理的fd对应的文件中，设置从file_hdr_.num_pages开始分配page_no
        disk_manager_->set_fd2pageno(fd, file_hdr_.num_pages);

        /*初始化*/
        // init_bitmap();
        has_free_slot_pages.clear();
    }

    std::vector<Rid>& get_inserted_rid()
    {
        return insertedRids;
    }

    std::vector<Rid>& get_raw_inserted_rid()
    {
        return insertedRawRids;
    }

    RmFileHdr get_file_hdr() { return file_hdr_; }
    int GetFd() { return fd_; }

    /* 判断指定位置上是否已经存在一条记录，通过Bitmap来判断 */
    bool is_record(const Rid &rid) const {
        RmPageHandle page_handle = fetch_page_handle(rid.page_no);
        return Bitmap::is_set(page_handle.bitmap, rid.slot_no);  // page的slot_no位置上是否有record
    }

    std::unique_ptr<RmRecord> get_record(const Rid &rid, Context *context) const;

    Rid insert_record(char *buf, Context *context);

    void insert_record(const Rid &rid, char *buf);

    void delete_record(const Rid &rid, Context *context);
    void delete_record1(const Rid &rid, Context *context);

    void update_record(const Rid &rid, char *buf, Context *context);

    std::string get_file_name(){return disk_manager_->get_file_name(fd_);};

    RmPageHandle create_new_page_handle();
    RmPageHandle create_new_page_handle1();

    RmPageHandle fetch_page_handle(int page_no) const;

    int get_num_records();

    Rid allocateRid();
    void deAllocateRid(const Rid& rid, Context* context);

    auto get_tuple_meta(Rid rid)
    {
        std::lock_guard<std::mutex> lock(tuple_mutex_);  // 自动加锁/解锁
        // printf("DEBUG******* %d %d\n",rid.page_no,rid.slot_no);
        return tuple_info_[rid];
    }

    void add_tuple_meta(Rid rid, TupleMeta tuplemeta)
    {
        std::lock_guard<std::mutex> lock(tuple_mutex_);  // 自动加锁/解锁
        tuple_info_[rid] = tuplemeta;
    }

    void update_tuple_meta(Rid rid, TupleMeta tuplemeta)
    {
        std::lock_guard<std::mutex> lock(tuple_mutex_);  // 自动加锁/解锁
        tuple_info_[rid] = tuplemeta;
    }

    void erase_tuple_meta(Rid rid)
    {
        std::lock_guard<std::mutex> lock(tuple_mutex_);  // 自动加锁/解锁
        tuple_info_.erase(rid);
    }

    auto get_tuplemeta_end()
    {
        std::lock_guard<std::mutex> lock(tuple_mutex_);  // 自动加锁/解锁
        return tuple_info_.end();
    }

   private:
    RmPageHandle create_page_handle();

    void release_page_handle(RmPageHandle &page_handle);
    void release_page_handle1(RmPageHandle &page_handle);

    // 假设在 RmFileHandle 初始化函数中（如 open 时）
    void init_bitmap() {
        // 初始页数为文件头中的 num_pages
        int initial_page_count = file_hdr_.num_pages;
        // 计算初始所需字节数：向上取整（页数 / 每字节位数）
        int initial_bytes = (initial_page_count + BITMAP_WIDTH - 1) / BITMAP_WIDTH;
        // 分配 bitmap 内存并初始化为 0（默认所有页面无空闲槽位）
        free_slot_pages_bitmap_.resize(initial_bytes);
        Bitmap::init(free_slot_pages_bitmap_.data(), initial_bytes);
        // 记录总位数（覆盖所有初始页面）
        bitmap_total_bits_ = initial_page_count;
        // （可选）如果需要初始化已有页面的空闲状态，可在此处扫描页面并更新 bitmap
        // 例如：遍历所有页面，检查是否有空闲槽位，调用 mark_page_has_free_slot 更新
    }

    void expand_free_slot_bitmap(int new_total_pages) {
        if (new_total_pages <= bitmap_total_bits_) {
            return;  // 无需扩容
        }

        // 计算新 bitmap 所需总字节数
        int new_bytes = (new_total_pages + BITMAP_WIDTH - 1) / BITMAP_WIDTH;
        int old_bytes = free_slot_pages_bitmap_.size();

        // 扩容 bitmap 存储容器（新增字节用 0 初始化）
        free_slot_pages_bitmap_.resize(new_bytes, 0);
        // 初始化新增的字节（从 old_bytes 到 new_bytes-1）
        Bitmap::init(free_slot_pages_bitmap_.data() + old_bytes, new_bytes - old_bytes);

        // 更新总位数
        bitmap_total_bits_ = new_total_pages;
    }

        /**
     * @brief 标记页面有空闲槽位（更新 bitmap 对应位为 1）
     * @param page_no 页面号
     */
    void mark_page_has_free_slot(int page_no) {
        // 确保 bitmap 能覆盖该页面（必要时扩容）
        if (page_no >= bitmap_total_bits_) {
            expand_free_slot_bitmap(page_no + 1);  // 至少覆盖到 page_no
        }
        // 设置 bitmap 对应位为 1
        Bitmap::set(free_slot_pages_bitmap_.data(), page_no);
    }

    /**
     * @brief 标记页面无空闲槽位（更新 bitmap 对应位为 0）
     * @param page_no 页面号
     */
    void mark_page_full(int page_no) {
        if (page_no >= bitmap_total_bits_) {
            return;  // 页面超出当前 bitmap 范围，无需操作（理论上不应发生）
        }
        // 重置 bitmap 对应位为 0
        Bitmap::reset(free_slot_pages_bitmap_.data(), page_no);
    }

    /**
     * @brief 检查页面是否有空闲槽位
     * @param page_no 页面号
     * @return 有空闲槽位返回 true，否则返回 false
     */
    bool has_free_slot(int page_no) const {
        if (page_no >= bitmap_total_bits_) {
            return false;  // 超出范围的页面视为无空闲
        }
        return Bitmap::is_set(free_slot_pages_bitmap_.data(), page_no);
    }

    /**
     * @brief 获取第一个有空闲槽位的页面号
     * @return 找到则返回页面号，无空闲页面返回 INVALID_PAGE_ID
     */
    int get_first_free_page_no() const {
        if (bitmap_total_bits_ == 0) {
            return INVALID_PAGE_ID;  // bitmap未初始化，无页面可查
        }

        // 调用Bitmap的first_bit查找第一个为1的位（表示有空闲槽位）
        int first_free_pos = Bitmap::first_bit(
            true,                  // 查找值为1的位（有空闲槽位）
            free_slot_pages_bitmap_.data(),  // bitmap数据起始地址
            bitmap_total_bits_     // 最大查找范围（总页数）
        );

        // 如果找到的位置等于总位数，说明没有空闲页面
        return (first_free_pos < bitmap_total_bits_) ? first_free_pos : INVALID_PAGE_ID;
    }
};