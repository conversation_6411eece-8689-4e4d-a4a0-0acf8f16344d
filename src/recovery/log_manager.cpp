/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include <cstring>
#include "log_manager.h"

/**
 * @description: 添加日志记录到日志缓冲区中，并返回日志记录号
 * @param {LogRecord*} log_record 要写入缓冲区的日志记录
 * @return {lsn_t} 返回该日志的日志记录号
 */
lsn_t LogManager::add_log_to_buffer(LogRecord* log_record) {
    std::scoped_lock lock{latch_};

    /*0.首先判断缓冲区满没满，如果满了就先刷新到磁盘*/
    auto log_size = log_record->log_tot_len_;
    if(log_buffer_.is_full(log_size))
    {
        // 刷新缓冲区到磁盘,
        // disk_manager_->write_log(log_buffer_.buffer_, log_buffer_.offset_);
        // 重置缓冲区
        log_buffer_.offset_ = 0;
        memset(log_buffer_.buffer_, 0, sizeof(log_buffer_.buffer_));
        // 更新持久化LSN为当前最大的LSN
        persist_lsn_ = global_lsn_ - 1;
    }

    /*1.为该日志分配一个lsn*/
    log_record->lsn_ = global_lsn_;
    global_lsn_++;

    /*2.将日志写到缓冲区*/
    log_record->serialize(log_buffer_.buffer_ + log_buffer_.offset_);
    log_buffer_.offset_ += log_size;

    lsn_t result_lsn = log_record->lsn_;

    /*3.释放日志记录内存*/
    delete log_record;

    return result_lsn;
}

/**
 * @description: 把日志缓冲区的内容刷到磁盘中，由于目前只设置了一个缓冲区，因此需要阻塞其他日志操作
 */
void LogManager::flush_log_to_disk() {
    std::scoped_lock lock{latch_};

    /*1.调用磁盘管理器的写日志函数*/
    if (log_buffer_.offset_ > 0) {
        // disk_manager_->write_log(log_buffer_.buffer_, log_buffer_.offset_);

        // 更新持久化LSN为当前最大的LSN
        persist_lsn_ = global_lsn_ - 1;

        // 刷新后重置缓冲区
        log_buffer_.offset_ = 0;
        memset(log_buffer_.buffer_, 0, sizeof(log_buffer_.buffer_));
    }
}
