/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "log_recovery.h"
#include "common/config.h"

/**
 * @description: 主恢复函数，执行完整的故障恢复流程
 */
void RecoveryManager::recover() {
    int log_file_size = disk_manager_->get_file_size("db.log");
    if (log_file_size <= 0) {
        return;
    }
    // 读取重启文件，获取最新检查点位置
    checkpoint_lsn_ = disk_manager_->read_restart_file();

    if (checkpoint_lsn_ != -1) {
        // 有检查点，从检查点开始恢复
        std::cout << "Found checkpoint at LSN: " << checkpoint_lsn_ << std::endl;
    } else {
        // 没有检查点，从头开始恢复
        std::cout << "No checkpoint found, starting from beginning" << std::endl;
    }

    // 执行三阶段恢复
    analyze();
    redo();
    undo();
    /*因为不知道表页持久化时相应的索引是否被持久化，所以重建索引*/
    reBuildIndex();

    // 恢复完成后，只刷新缓冲池，避免使用可能阻塞的sync()系统调用
    std::cout << "Recovery completed, flushing buffer pool..." << std::endl;

    // // 刷新所有脏页到磁盘
    // for (auto& fh_pair : sm_manager_->fhs_) {
    //     auto& fh = fh_pair.second;
    //     int fd = fh->GetFd();
    //     // 强制刷新该文件的所有页面
    //     for (page_id_t page_id = 0; page_id < disk_manager_->get_fd2pageno(fd); page_id++) {
    //         buffer_pool_manager_->flush_page({fd, page_id});
    //     }
    // }

    std::cout << "Recovery completed successfully" << std::endl;
}

/**
 * @description: analyze阶段，需要获得脏页表（DPT）和未完成的事务列表（ATT）
 */
void RecoveryManager::analyze() {
    std::cout << "Analyzing log records..." << std::endl;

    // 清空恢复数据结构
    undo_list_.clear();
    redo_list_.clear();
    txn_last_lsn_.clear();

    // 确定分析起始位置
    int start_lsn = 0;
    if (checkpoint_lsn_ != -1) {
        start_lsn = checkpoint_lsn_;
        // 从检查点记录中读取活跃事务列表
        analyze_checkpoint_record(checkpoint_lsn_);
    }

    // 一次性读取日志文件并解析所有记录
    read_and_parse_log(start_lsn);
}

/**
 * @description: 重做所有未落盘的操作
 */
void RecoveryManager::redo() {
    // 重做所有已提交事务的操作
    /*正向遍历日志，重做在redolist中的操作*/
    // 使用unordered_set加速查找
    const std::unordered_set<txn_id_t> redo_set(redo_list_.begin(), redo_list_.end());
    
    for(const auto &log : logs)
    {
        const auto &cur_log = log.second;
        // 快速查找事务是否需要重做
        if(redo_set.count(cur_log->log_tid_))
        {
            switch(cur_log->log_type_)
            {
                case UPDATE:
                {
                    if(auto updateLog = std::dynamic_pointer_cast<UpdateLogRecord>(cur_log))
                    {
                        redo_update_operation(*updateLog);
                    }
                    break;
                }
                case INSERT:
                {
                    if(auto insertLog = std::dynamic_pointer_cast<InsertLogRecord>(cur_log))
                    {
                        redo_insert_operation(*insertLog);
                    }
                    break;
                }
                case DELETE:
                {
                    if(auto deleteLog = std::dynamic_pointer_cast<DeleteLogRecord>(cur_log))
                    {
                        redo_delete_operation(*deleteLog);
                    }
                    break;
                }
                default:
                    continue;
            }
        }
    }
}

/**
 * @description: 回滚未完成的事务
 */
void RecoveryManager::undo() {
    /* 反向遍历日志，undo在undolist中的操作 */
    // 使用unordered_set加速查找
    const std::unordered_set<txn_id_t> undo_set(undo_list_.begin(), undo_list_.end());
    
    // 首先收集所有需要undo的日志记录
    // std::vector<std::shared_ptr<LogRecord>> undo_logs;
    // undo_logs.reserve(logs.size());  // 预分配内存
    
    // for (const auto &logPair : logs) {
    //     const auto &log = logPair.second;
    //     // 快速检查事务ID是否在undo列表中
    //     if (undo_set.count(log->log_tid_)) {
    //         undo_logs.push_back(log);
    //     }
    // }

    // 反向遍历logs容器
    for (auto it = logs.rbegin(); it != logs.rend(); ++it) {
        if(it->first < global_min_undo_lsn_)
        {
            break;
        }

        switch (it->second->log_type_) {
            case UPDATE: {
                if (auto updateLog = std::dynamic_pointer_cast<UpdateLogRecord>(it->second)) {
                    // 撤销更新：将新值改回旧值
                    undo_update_operation(*updateLog);
                }
                break;
            }
            case INSERT: {
                if (auto insertLog = std::dynamic_pointer_cast<InsertLogRecord>(it->second)) {
                    // 撤销插入：删除记录
                    undo_insert_operation(*insertLog);
                }
                break;
            }
            case DELETE: {
                if (auto deleteLog = std::dynamic_pointer_cast<DeleteLogRecord>(it->second)) {
                    // 撤销删除：恢复记录
                    undo_delete_operation(*deleteLog);
                }
                break;
            }
            default:
                break;
        }

        // const auto &log = it->second;
        // // 快速检查事务ID是否在undo列表中
        // if (undo_set.count(log->log_tid_)) {
        //     undo_logs.push_back(log);
        // }
    }

    // // 按LSN降序排序（逆序处理日志）
    // std::sort(undo_logs.begin(), undo_logs.end(), 
    //           [](const std::shared_ptr<LogRecord> &a, const std::shared_ptr<LogRecord> &b) { 
    //               return a->lsn_ > b->lsn_; 
    //           });

    // // 逆序执行undo操作
    // for (const auto &log : undo_logs) {
    //     switch (log->log_type_) {
    //         case UPDATE: {
    //             if (auto updateLog = std::dynamic_pointer_cast<UpdateLogRecord>(log)) {
    //                 // 撤销更新：将新值改回旧值
    //                 undo_update_operation(*updateLog);
    //             }
    //             break;
    //         }
    //         case INSERT: {
    //             if (auto insertLog = std::dynamic_pointer_cast<InsertLogRecord>(log)) {
    //                 // 撤销插入：删除记录
    //                 undo_insert_operation(*insertLog);
    //             }
    //             break;
    //         }
    //         case DELETE: {
    //             if (auto deleteLog = std::dynamic_pointer_cast<DeleteLogRecord>(log)) {
    //                 // 撤销删除：恢复记录
    //                 undo_delete_operation(*deleteLog);
    //             }
    //             break;
    //         }
    //         default:
    //             break;
    //     }
    // }
}

// void RecoveryManager::reBuildIndex() {
//     // 假设 RecoveryManager 拥有以下成员（需根据实际代码调整）：
//     // - sm_manager_: 指向 SmManager 的指针，用于操作表和索引
//     // - context_: 上下文信息（包含日志管理器、事务等）
//     // - tables_: 所有需要恢复的表名列表
//     Context cnt{nullptr,nullptr,nullptr};
//     /* 步骤1：收集所有表的索引元数据（保存索引信息，用于后续重建） */
//     std::unordered_map<std::string, std::vector<IndexMeta>> index_metadata;  // key: 表名，value: 该表的所有索引
//     for (const auto& tablename : sm_manager_->db_.get_tabs()) {
//         // 获取表的元数据，提取所有索引信息
//         auto table_name = tablename.first;
//         TabMeta &tab_meta = sm_manager_->db_.get_table(table_name);
//         index_metadata[table_name] = tab_meta.indexes;  // 保存索引元数据（字段、类型等）
//     }

//     /* 步骤2：删除所有旧索引（避免旧索引数据干扰） */
//     for (const auto& tablename : sm_manager_->db_.get_tabs()) {
//         try {
//             auto table_name = tablename.first;
//             TabMeta &tab_meta = sm_manager_->db_.get_table(table_name);
//             // 遍历该表的所有索引，逐个删除
//             std::vector<IndexMeta> indexes_snapshot = tab_meta.indexes;
//             // 基于快照遍历删除
//             for (const auto& index : indexes_snapshot) {
//                 std::vector<std::string> index_col_names;
//                 for (const auto& col_meta : index.cols) {
//                     index_col_names.push_back(col_meta.name);
//                 }
//                 // 即使删除操作修改了 tab_meta.indexes，也不影响快照遍历
//                 sm_manager_->drop_index(table_name, index_col_names, &cnt);
//             }
//         } catch (const TableNotFoundError& e) {
//             continue;  // 表不存在，跳过
//         }
//     }


//     /* 步骤3：基于当前表数据重建所有索引 */
//     for (const auto& [table_name, indexes] : index_metadata) {
//         // 为每个索引重新创建
//         for (const auto& index : indexes) {
//             // 提取索引包含的字段名（用于创建索引）
//             std::vector<std::string> index_col_names;
//             for (const auto& col_meta : index.cols) {
//                 index_col_names.push_back(col_meta.name);
//             }
//             // 调用 SmManager 的创建索引接口：
//             // 内部会扫描表中所有记录，为每条记录生成索引项，确保索引与当前数据一致
//             sm_manager_->create_index(table_name, index_col_names, &cnt);
//         }
//     }
//     std::cout << "All indexes have been rebuilt successfully." << std::endl;
// }


void RecoveryManager::reBuildIndex() {
    // 检查必要资源是否有效
    Context cnt{nullptr,nullptr,nullptr,nullptr};
    if (!sm_manager_) {
        std::cerr << "Recovery failed: invalid manager or context" << std::endl;
        return;
    }

    // 遍历所有表
    for (const auto& tab_entry : sm_manager_->db_.get_tabs()) {
        const std::string& tab_name = tab_entry.first;
        const TabMeta& tab_info = tab_entry.second;

        // 处理每个索引
        std::vector<IndexMeta> indexes_snapshot = tab_info.indexes;
        for (const auto& idx : indexes_snapshot) {
            try {
                // 收集索引字段
                std::vector<std::string> idx_fields;
                for (const auto& col : idx.cols) {
                    idx_fields.push_back(col.name);
                }

                // 删除并重建索引
                sm_manager_->drop_index(tab_name, idx_fields, &cnt);
                sm_manager_->create_index(tab_name, idx_fields, &cnt);
            } 
            catch (const std::exception& e) {
                std::cerr << "Index rebuild failed for " << tab_name << ": " << e.what() << std::endl;
            }
        }
    }

    std::cout << "Index rebuild process finished" << std::endl;
}


/**
 * @description: 分析检查点记录
 */
void RecoveryManager::analyze_checkpoint_record(int checkpoint_lsn) {
    // 读取检查点记录，获取活跃事务列表
    try {
        // 获取日志文件大小
        int log_file_size = disk_manager_->get_file_size("db.log");
        if (log_file_size <= 0) {
            std::cout << "Log file is empty, no checkpoint record to analyze" << std::endl;
            return;
        }

        // 读取整个日志文件
        char* log_buffer = new char[log_file_size];
        disk_manager_->read_log(log_buffer, log_file_size, 0);

        // 查找指定LSN的检查点记录
        int current_offset = 0;
        bool found_checkpoint = false;

        int num = 0;

        while (current_offset < log_file_size) {
            num++;
            // if(num > 100)
            // {
            //     assert(1 == 2);
            // }
            
            if (current_offset + LOG_HEADER_SIZE > log_file_size) break;

            // 读取日志头部信息
            LogType log_type = *reinterpret_cast<LogType*>(log_buffer + current_offset + OFFSET_LOG_TYPE);
            lsn_t lsn = *reinterpret_cast<lsn_t*>(log_buffer + current_offset + OFFSET_LSN);
            uint32_t log_tot_len = *reinterpret_cast<uint32_t*>(log_buffer + current_offset + OFFSET_LOG_TOT_LEN);

            if (log_tot_len == 0 || current_offset + log_tot_len > log_file_size) break;

            // 找到目标检查点记录
            if (log_type == LogType::CHECKPOINT && lsn == checkpoint_lsn) {
                found_checkpoint = true;

                // 反序列化检查点记录
                CheckpointLogRecord checkpoint_record;
                checkpoint_record.deserialize(log_buffer + current_offset);

                // 将检查点时的活跃事务加入undo_list
                for (size_t i = 0; i < checkpoint_record.active_txn_count_; i++) {
                    txn_id_t txn_id = checkpoint_record.active_txns_[i];
                    undo_list_.insert(txn_id);
                    std::cout << "Added active transaction " << txn_id << " to undo list from checkpoint" << std::endl;
                }

                std::cout << "Analyzed checkpoint record: " << checkpoint_record.active_txn_count_
                          << " active transactions" << std::endl;
                break;
            }

            current_offset += log_tot_len;
        }

        if (!found_checkpoint) {
            std::cout << "Warning: Checkpoint record at LSN " << checkpoint_lsn << " not found" << std::endl;
        }

        delete[] log_buffer;
    } catch (const std::exception& e) {
        std::cerr << "Error analyzing checkpoint record: " << e.what() << std::endl;
    }
}

/**
 * @description: 一次性读取并解析日志文件，缓存所有日志记录
 */
void RecoveryManager::read_and_parse_log(int start_lsn) {
    // 清空缓存
    log_records_.clear();
    // txn_log_indices_.clear();

    // 读取日志文件
    int log_file_size = 0;
    try {
        log_file_size = disk_manager_->get_file_size("db.log");
    } catch (...) {
        std::cout << "Failed to get log file size" << std::endl;
        return;
    }

    if (log_file_size <= 0) {
        std::cout << "Log file is empty" << std::endl;
        return;
    }

    // 使用智能指针管理缓冲区，避免内存泄漏
    const auto log_buffer = std::make_unique<char[]>(log_file_size + 1);
    const int offset = 0;
    const int bytes_read = disk_manager_->read_log(log_buffer.get(), log_file_size, offset);
    std::cout << "Read " << bytes_read << " bytes from log file" << std::endl;

    // 解析并缓存所有日志记录
    int current_offset = 0;
    while (current_offset < log_file_size) {
        // 检查头部是否完整
        if (current_offset + LOG_HEADER_SIZE > log_file_size) {
            break;
        }

        // 读取日志头部信息（使用const引用避免重复计算）
        const char* current_pos = log_buffer.get() + current_offset;
        const LogType log_type = *reinterpret_cast<const LogType*>(current_pos + OFFSET_LOG_TYPE);
        const lsn_t lsn = *reinterpret_cast<const lsn_t*>(current_pos + OFFSET_LSN);
        const uint32_t log_tot_len = *reinterpret_cast<const uint32_t*>(current_pos + OFFSET_LOG_TOT_LEN);
        const txn_id_t txn_id = *reinterpret_cast<const txn_id_t*>(current_pos + OFFSET_LOG_TID);

        // 检查日志记录是否完整
        if (log_tot_len == 0 || current_offset + log_tot_len > log_file_size) {
            break;
        }

        // 对于活跃事务，需要处理它们在检查点之前的操作
        // 只有当LSN小于start_lsn且事务不在undo_list中时才跳过
        if (lsn < start_lsn && undo_list_.find(txn_id) == undo_list_.end()) {
            current_offset += log_tot_len;
            continue;
        }

        // 根据 log_type 创建对应的派生类实例
        std::shared_ptr<LogRecord> log_record;
        switch (log_type) {
            case LogType::INSERT:
                log_record = std::make_shared<InsertLogRecord>();
                break;
            case LogType::DELETE:
                log_record = std::make_shared<DeleteLogRecord>();
                break;
            case LogType::UPDATE:
                log_record = std::make_shared<UpdateLogRecord>();
                break;
            case LogType::begin:
                log_record = std::make_shared<BeginLogRecord>();
                break;
            case LogType::commit:
                log_record = std::make_shared<CommitLogRecord>();
                break;
            case LogType::ABORT:
                log_record = std::make_shared<AbortLogRecord>();
                break;
            case LogType::CHECKPOINT:
                log_record = std::make_shared<CheckpointLogRecord>();
                break;
            default:
                // 跳过未知类型日志，避免错误
                current_offset += log_tot_len;
                continue;
        }

        // 反序列化（派生类重写 deserialize 以初始化自身字段）
        log_record->deserialize(current_pos);
        // log_record->format_print();
        // logs.emplace(lsn, log_record);  // 使用emplace减少一次拷贝

        // 收集相关的表名（合并类型判断）
        // if (log_type == LogType::INSERT || log_type == LogType::DELETE || log_type == LogType::UPDATE) {
        //     const char* table_name_ptr = nullptr;
        //     size_t table_name_size = 0;

        //     if (log_type == LogType::INSERT) {
        //         auto insert_log = std::dynamic_pointer_cast<InsertLogRecord>(log_record);
        //         if (insert_log) {
        //             table_name_ptr = insert_log->table_name_;
        //             table_name_size = insert_log->table_name_size_;
        //         }
        //     } else if (log_type == LogType::DELETE) {
        //         auto delete_log = std::dynamic_pointer_cast<DeleteLogRecord>(log_record);
        //         if (delete_log) {
        //             table_name_ptr = delete_log->table_name_;
        //             table_name_size = delete_log->table_name_size_;
        //         }
        //     } else if (log_type == LogType::UPDATE) {
        //         auto update_log = std::dynamic_pointer_cast<UpdateLogRecord>(log_record);
        //         if (update_log) {
        //             table_name_ptr = update_log->table_name_;
        //             table_name_size = update_log->table_name_size_;
        //         }
        //     }

        //     if (table_name_ptr && table_name_size > 0) {
        //         tables_.emplace(table_name_ptr, table_name_size);  // 直接使用迭代器范围构造字符串
        //     }
        // }

        // 分析不同类型的日志记录
        switch (log_type) {
            case LogType::begin:
                undo_list_.insert(txn_id);
                txn_lsn[txn_id] = lsn;
                break;
            case LogType::commit:
            case LogType::ABORT:  // 合并相同处理逻辑
                undo_list_.erase(txn_id);
                redo_list_.push_back(txn_id);
                txn_lsn.erase(txn_id);
                break;
            default:
                // txn_last_lsn_[txn_id] = lsn;
                logs.push_back({lsn, log_record});  // 使用emplace减少一次拷贝
                break;
        }

        current_offset += log_tot_len;
        // max_lsn = lsn;
    }

    global_min_undo_lsn_ = INVALID_LSN;
    if(!txn_lsn.empty())
    {
        global_min_undo_lsn_ = txn_lsn.begin()->second;
    }
    for(const auto &entry : txn_lsn)
    {
        if(entry.second < global_min_undo_lsn_)
        {
            global_min_undo_lsn_ = entry.second;
        }
    }
}

/**
 * @description: 从指定LSN开始分析日志（保留原方法以兼容）
 */
void RecoveryManager::analyze_log_from_lsn(int start_lsn) {
    // 这个方法现在由read_and_parse_log替代，保留空实现以兼容
}

/**
 * @description: 重做插入操作
 */
void RecoveryManager::redo_insert_operation(const InsertLogRecord& log) {
    // 构造表名
    std::string table_name(log.table_name_, log.table_name_size_);
    auto& fh = sm_manager_->fhs_.at(table_name);
    const auto& file_hdr = fh->get_file_hdr();
    const PageId page_id = {fh->GetFd(), log.rid_.page_no};

    // 检查页面是否存在，不存在则创建
    if (log.rid_.page_no >= file_hdr.num_pages) {
        while (fh->get_file_hdr().num_pages <= log.rid_.page_no) {
            auto new_page_handle = fh->create_new_page_handle1();
            buffer_pool_manager_->unpin_page(new_page_handle.page->get_page_id(), false);
        }
    } else {
        // 页面存在时检查LSN，判断是否需要重做
        auto page_handle = fh->fetch_page_handle(log.rid_.page_no);
        const lsn_t page_lsn = page_handle.page->get_page_lsn();
        if (page_lsn >= log.lsn_) {
            buffer_pool_manager_->unpin_page(page_id, false);
            return;
        }
        buffer_pool_manager_->unpin_page(page_id, false);
    }

    // 重新插入记录
    fh->insert_record(log.rid_, log.insert_value_.data);

    /*2.插入索引*/
    // TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // auto record = log.insert_value_;
    // for(size_t idx = 0;idx < tab.indexes.size();idx++)
    // {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
    //     char *key = new char[index.col_tot_len];
    //     int offset = 0;
    //     /*2.1构造主键值*/
    //     for(size_t i = 0;i < index.col_num; i++)
    //     {
    //         memcpy(key+offset, record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     ih->insert_entry(key, log.rid_, nullptr);
    // }
}

/**
 * @description: 重做删除操作
 */
void RecoveryManager::redo_delete_operation(const DeleteLogRecord& log) {
    // 构造表名
    std::string table_name(log.table_name_, log.table_name_size_);
    auto& fh = sm_manager_->fhs_.at(table_name);
    const auto& file_hdr = fh->get_file_hdr();
    const PageId page_id = {fh->GetFd(), log.rid_.page_no};

    // 检查页面是否存在，不存在则创建
    if (log.rid_.page_no >= file_hdr.num_pages) {
        while (fh->get_file_hdr().num_pages <= log.rid_.page_no) {
            auto new_page_handle = fh->create_new_page_handle1();
            buffer_pool_manager_->unpin_page(new_page_handle.page->get_page_id(), false);
        }
    } else {
        // 页面存在时检查LSN，判断是否需要重做
        auto page_handle = fh->fetch_page_handle(log.rid_.page_no);
        const lsn_t page_lsn = page_handle.page->get_page_lsn();
        if (page_lsn >= log.lsn_) {
            buffer_pool_manager_->unpin_page(page_id, false);
            return;
        }
        buffer_pool_manager_->unpin_page(page_id, false);
    }

    // 重新执行删除操作
    Context temp_context(nullptr, nullptr, nullptr, nullptr);
    fh->delete_record1(log.rid_, &temp_context);

    // /*3.删除索引*/
    // TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // auto record = log.delete_value_;
    // for(size_t idx = 0; idx < tab.indexes.size(); ++idx) {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
    //     char* key = new char[index.col_tot_len];
    //     int offset = 0;
    //     for(size_t i = 0; i < index.col_num; ++i) {
    //         memcpy(key + offset, record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     /*2.2删除索引*/
    //     ih->delete_entry(key, nullptr);
    // }
}

/**
 * @description: 重做更新操作
 */
void RecoveryManager::redo_update_operation(const UpdateLogRecord& log) {
    // 构造表名
    std::string table_name(log.table_name_, log.table_name_size_);
    auto& fh = sm_manager_->fhs_.at(table_name);
    const auto& file_hdr = fh->get_file_hdr();
    const PageId page_id = {fh->GetFd(), log.rid_.page_no};

    // 检查页面是否存在，不存在则创建
    if (log.rid_.page_no >= file_hdr.num_pages) {
        while (fh->get_file_hdr().num_pages <= log.rid_.page_no) {
            auto new_page_handle = fh->create_new_page_handle1();
            buffer_pool_manager_->unpin_page(new_page_handle.page->get_page_id(), false);
        }
    } else {
        // 页面存在时检查LSN，判断是否需要重做
        auto page_handle = fh->fetch_page_handle(log.rid_.page_no);
        const lsn_t page_lsn = page_handle.page->get_page_lsn();
        if (page_lsn >= log.lsn_) {
            buffer_pool_manager_->unpin_page(page_id, false);
            return;
        }
        buffer_pool_manager_->unpin_page(page_id, false);
    }

    // 使用新值执行更新操作
    Context temp_context(nullptr, nullptr, nullptr, nullptr);
    fh->update_record(log.rid_, log.new_value_.data, &temp_context);

    /*处理索引*/
    // /*2.删除旧索引*/
    // TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // auto old_record = log.old_value_;
    // auto new_record = log.new_value_;
    // for(size_t idx = 0;idx < tab.indexes.size();idx++)
    // {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
    //     char *key = new char[index.col_tot_len];
    //     int offset = 0;
    //     /*2.1构造主键值*/
    //     for(size_t i = 0;i < index.col_num; i++)
    //     {
    //         memcpy(key+offset, old_record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     /*2.2删除索引*/
    //     ih->delete_entry(key, nullptr);
    // }

    // /*4.插入新索引*/
    // for(size_t idx = 0; idx < tab.indexes.size(); ++idx) {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
    //     char* key = new char[index.col_tot_len];
    //     int offset = 0;
    //     for(size_t i = 0; i < index.col_num; ++i) {
    //         memcpy(key + offset, new_record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     ih->insert_entry(key, log.rid_, nullptr);
    // }
}

/**
 * @description: 撤销插入操作（删除记录）
 */
void RecoveryManager::undo_insert_operation(const InsertLogRecord& log) {
    // 构造表名
    std::string table_name(log.table_name_, log.table_name_size_);
    auto& fh = sm_manager_->fhs_.at(table_name);
    const auto& file_hdr = fh->get_file_hdr();
    const PageId page_id = {fh->GetFd(), log.rid_.page_no};

    // 页面不存在则无需撤销
    if (file_hdr.num_pages <= log.rid_.page_no) {
        return;
    }

    // 检查页面LSN，判断是否需要撤销
    auto page_handle = fh->fetch_page_handle(log.rid_.page_no);
    const lsn_t page_lsn = page_handle.page->get_page_lsn();
    if (page_lsn < log.lsn_) {
        buffer_pool_manager_->unpin_page(page_id, false);
        return;
    }
    buffer_pool_manager_->unpin_page(page_id, false);

    // 执行删除操作撤销插入
    Context temp_context(nullptr, nullptr, nullptr, nullptr);
    fh->delete_record1(log.rid_, &temp_context);

    /*3.删除索引*/
    // TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // auto record = log.insert_value_;
    // for(size_t idx = 0; idx < tab.indexes.size(); ++idx) {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
    //     char* key = new char[index.col_tot_len];
    //     int offset = 0;
    //     for(size_t i = 0; i < index.col_num; ++i) {
    //         memcpy(key + offset, record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     /*2.2删除索引*/
    //     ih->delete_entry(key, nullptr);
    // }
}

/**
 * @description: 撤销删除操作（重新插入记录）
 */
void RecoveryManager::undo_delete_operation(const DeleteLogRecord& log) {
    // 构造表名
    std::string table_name(log.table_name_, log.table_name_size_);
    auto& fh = sm_manager_->fhs_.at(table_name);
    const auto& file_hdr = fh->get_file_hdr();
    const PageId page_id = {fh->GetFd(), log.rid_.page_no};

    // 页面不存在则无需撤销
    if (file_hdr.num_pages <= log.rid_.page_no) {
        return;
    }

    // 检查页面LSN，判断是否需要撤销
    auto page_handle = fh->fetch_page_handle(log.rid_.page_no);
    const lsn_t page_lsn = page_handle.page->get_page_lsn();
    if (page_lsn < log.lsn_) {
        buffer_pool_manager_->unpin_page(page_id, false);
        return;
    }
    buffer_pool_manager_->unpin_page(page_id, false);

    // 重新插入记录撤销删除
    fh->insert_record(log.rid_, log.delete_value_.data);

    /*2.插入索引*/
    // TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // auto record = log.delete_value_;
    // for(size_t idx = 0;idx < tab.indexes.size();idx++)
    // {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
    //     char *key = new char[index.col_tot_len];
    //     int offset = 0;
    //     /*2.1构造主键值*/
    //     for(size_t i = 0;i < index.col_num; i++)
    //     {
    //         memcpy(key+offset, record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     ih->insert_entry(key, log.rid_, nullptr);
    // }
}

/**
 * @description: 撤销更新操作（恢复旧值）
 */
void RecoveryManager::undo_update_operation(const UpdateLogRecord& log) {
    // 构造表名
    std::string table_name(log.table_name_, log.table_name_size_);
    auto& fh = sm_manager_->fhs_.at(table_name);
    const auto& file_hdr = fh->get_file_hdr();
    const PageId page_id = {fh->GetFd(), log.rid_.page_no};

    // 页面不存在则无需撤销
    if (file_hdr.num_pages <= log.rid_.page_no) {
        return;
    }

    // 检查页面LSN，判断是否需要撤销
    auto page_handle = fh->fetch_page_handle(log.rid_.page_no);
    const lsn_t page_lsn = page_handle.page->get_page_lsn();
    if (page_lsn < log.lsn_) {
        buffer_pool_manager_->unpin_page(page_id, false);
        return;
    }
    buffer_pool_manager_->unpin_page(page_id, false);

    // 恢复旧值撤销更新
    Context temp_context(nullptr, nullptr, nullptr, nullptr);
    fh->update_record(log.rid_, log.old_value_.data, &temp_context);

    /*处理索引*/
    /*2.删除旧索引*/
    // TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // auto new_record = log.old_value_;
    // auto old_record = log.new_value_;
    // for(size_t idx = 0;idx < tab.indexes.size();idx++)
    // {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
    //     char *key = new char[index.col_tot_len];
    //     int offset = 0;
    //     /*2.1构造主键值*/
    //     for(size_t i = 0;i < index.col_num; i++)
    //     {
    //         memcpy(key+offset, old_record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     /*2.2删除索引*/
    //     ih->delete_entry(key, nullptr);
    // }

    // /*4.插入新索引*/
    // for(size_t idx = 0; idx < tab.indexes.size(); ++idx) {
    //     auto& index = tab.indexes[idx];
    //     auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
    //     char* key = new char[index.col_tot_len];
    //     int offset = 0;
    //     for(size_t i = 0; i < index.col_num; ++i) {
    //         memcpy(key + offset, new_record.data + index.cols[i].offset, index.cols[i].len);
    //         offset += index.cols[i].len;
    //     }
    //     ih->insert_entry(key, log.rid_, nullptr);
    // }
}