/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "executor_aggregation.h"
#include "executor_seq_scan.h"
#include "common/common.h"
#include <algorithm>
#include <cstring>
#include <limits>
#include <climits>

void AggregationExecutor::execute_aggregation() {
    group_states_.clear();
    results_.clear();

    // 检查是否可以优化COUNT(*)查询
    if (can_optimize_count_star()) {
        execute_optimized_count_star();
        return;
    }

    // 遍历所有输入元组
    for (prev_->beginTuple(); !prev_->is_end(); prev_->nextTuple()) {
        auto record = prev_->Next();
        if (!record) continue;

        // 提取分组键
        GroupKey group_key = extract_group_key(record);

        // 获取或创建该分组的聚合状态
        AggregateState& state = group_states_[group_key];

        // 更新聚合状态
        for (const auto& agg_expr : agg_exprs_) {
            update_aggregate_state(state, agg_expr, record);
        }
    }

    // 处理空结果集的情况
    if (group_states_.empty() && group_cols_.empty()) {
        // 对于没有GROUP BY的空表聚合查询，需要特殊处理
        // COUNT(*) 和 COUNT(column) 应该返回0
        // MAX, MIN, SUM, AVG 根据要求不显示数据行

        bool has_count_function = false;
        for (const auto& agg_expr : agg_exprs_) {
            if (agg_expr.type == AGG_COUNT) {
                has_count_function = true;
                break;
            }
        }

        if (has_count_function) {
            // 如果有COUNT函数，需要返回一行结果（COUNT返回0）
            GroupKey empty_key(std::vector<Value>{});
            AggregateState default_state;
            AggregateResult result = compute_final_result(default_state);

            // 检查HAVING条件
            if (evaluate_having_conditions(empty_key, result)) {
                results_[empty_key] = result;
            }
        }
        // 如果只有MAX/MIN/SUM/AVG，不添加任何记录（不显示数据行）
    } else if (!group_states_.empty()) {
        // 计算最终结果并应用HAVING条件
        for (const auto& pair : group_states_) {
            const GroupKey& group_key = pair.first;
            const AggregateState& state = pair.second;

            AggregateResult result = compute_final_result(state);

            // 检查HAVING条件
            if (evaluate_having_conditions(group_key, result)) {
                results_[group_key] = result;
            }
        }
    }
}

GroupKey AggregationExecutor::extract_group_key(const std::unique_ptr<RmRecord>& record) {
    std::vector<Value> key_values;
    
    for (const auto& group_col : group_cols_) {
        Value val = extract_column_value(record, group_col);
        key_values.push_back(val);
    }
    
    return GroupKey(key_values);
}

void AggregationExecutor::update_aggregate_state(AggregateState& state, const ast::AggExpr& agg_expr,
                                                 const std::unique_ptr<RmRecord>& record) {
    // 获取聚合函数的唯一标识符
    std::string agg_key = get_agg_key(agg_expr);
    SingleAggState& single_state = state.agg_states[agg_key];

    switch (agg_expr.type) {
        case AGG_COUNT:
            if (agg_expr.col == nullptr) {
                // COUNT(*)
                single_state.count++;
            } else {
                // COUNT(column) - 只计算非NULL值
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});
                // 这里简化处理，假设所有值都非NULL
                single_state.count++;
            }
            single_state.has_value = true;
            break;

        case AGG_SUM:
        case AGG_AVG:
            if (agg_expr.col) {
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});
                double num_val = 0.0;

                if (val.type == TYPE_INT) {
                    num_val = static_cast<double>(val.int_val);
                } else if (val.type == TYPE_FLOAT) {
                    num_val = static_cast<double>(val.float_val);
                }

                single_state.sum += num_val;
                single_state.count++;
                single_state.has_value = true;
            }
            break;

        case AGG_MAX:
            if (agg_expr.col) {
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});

                if (!single_state.has_value) {
                    single_state.max_val = val;
                    single_state.has_value = true;
                } else {
                    // 比较并更新最大值
                    if (compare_values(val, single_state.max_val) > 0) {
                        single_state.max_val = val;
                    }
                }
            }
            break;

        case AGG_MIN:
            if (agg_expr.col) {
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});

                if (!single_state.has_value) {
                    single_state.min_val = val;
                    single_state.has_value = true;
                } else {
                    // 比较并更新最小值
                    if (compare_values(val, single_state.min_val) < 0) {
                        single_state.min_val = val;
                    }
                }
            }
            break;

        default:
            break;
    }
}

AggregateResult AggregationExecutor::compute_final_result(const AggregateState& state) {
    AggregateResult result;

    for (const auto& agg_expr : agg_exprs_) {
        std::string key = get_agg_key(agg_expr);
        Value final_val;

        // 获取该聚合函数的状态
        auto it = state.agg_states.find(key);
        if (it == state.agg_states.end()) {
            // 如果没有找到状态，根据聚合函数类型设置默认值
            switch (agg_expr.type) {
                case AGG_COUNT:
                    final_val.set_int(0);  // COUNT返回0
                    break;
                default:
                    final_val.set_null();  // 其他聚合函数返回NULL
                    break;
            }
            result.values[key] = final_val;
            continue;
        }

        const SingleAggState& single_state = it->second;

        switch (agg_expr.type) {
            case AGG_COUNT:
                final_val.set_int(single_state.count);
                break;

            case AGG_SUM:
                if (single_state.has_value) {
                    // SUM应该根据原列类型返回相应类型的值
                    if (agg_expr.col) {
                        ColMeta source_col = prev_->get_col_offset({agg_expr.col->tab_name, agg_expr.col->col_name});
                        if (source_col.type == TYPE_INT) {
                            final_val.set_int(static_cast<int>(single_state.sum));
                        } else {
                            final_val.set_float(static_cast<float>(single_state.sum));
                        }
                    } else {
                        final_val.set_int(static_cast<int>(single_state.sum));
                    }
                } else {
                    final_val.set_null();  // 空表时返回NULL
                }
                break;

            case AGG_AVG:
                if (single_state.count > 0) {
                    final_val.set_float(static_cast<float>(single_state.sum / single_state.count));
                } else {
                    final_val.set_null();  // 空表时返回NULL
                }
                break;

            case AGG_MAX:
                if (single_state.has_value) {
                    final_val = single_state.max_val;
                } else {
                    final_val.set_null();  // 空表时返回NULL
                }
                break;

            case AGG_MIN:
                if (single_state.has_value) {
                    final_val = single_state.min_val;
                } else {
                    final_val.set_null();  // 空表时返回NULL
                }
                break;

            default:
                final_val.set_null();
                break;
        }

        result.values[key] = final_val;
    }

    return result;
}

bool AggregationExecutor::evaluate_having_conditions(const GroupKey& group_key, const AggregateResult& agg_result) {
    // 如果没有HAVING条件，返回true
    if (having_conds_.empty()) {
        return true;
    }



    // 评估所有HAVING条件（AND连接）
    for (const auto& cond : having_conds_) {
        Value lhs_val, rhs_val;

        // 获取左操作数的值
        if (cond.is_lhs_agg) {
            // 左操作数是聚合函数
            // 首先尝试使用原始键查找
            std::string agg_key = get_agg_key(cond.lhs_agg);

            auto it = agg_result.values.find(agg_key);
            if (it != agg_result.values.end()) {
                lhs_val = it->second;
            } else {
                // 如果找不到，尝试通过函数类型和列匹配
                bool found = false;
                for (const auto& pair : agg_result.values) {
                    // 检查是否有对应的聚合表达式
                    for (const auto& agg_expr : agg_exprs_) {
                        if (agg_expr.type == cond.lhs_agg.type) {
                            // 检查列是否匹配
                            bool col_match = false;
                            if (agg_expr.col == nullptr && cond.lhs_agg.col == nullptr) {
                                col_match = true;  // 都是COUNT(*)
                            } else if (agg_expr.col != nullptr && cond.lhs_agg.col != nullptr) {
                                col_match = (agg_expr.col->col_name == cond.lhs_agg.col->col_name);
                            }
                            // 如果一个是nullptr，另一个不是，则不匹配
                            // col_match保持false

                            if (col_match && get_agg_key(agg_expr) == pair.first) {
                                lhs_val = pair.second;
                                found = true;
                                break;
                            }
                        }
                    }
                    if (found) break;
                }
            }
        } else {
            // 左操作数是分组列
            for (size_t i = 0; i < group_cols_.size(); ++i) {
                if (group_cols_[i].tab_name == cond.lhs_col.tab_name &&
                    group_cols_[i].col_name == cond.lhs_col.col_name) {
                    lhs_val = group_key.key_values[i];
                    break;
                }
            }
        }

        // 获取右操作数的值
        if (cond.is_rhs_val) {
            rhs_val = cond.rhs_val;
        } else {
            // 右操作数是列，这里简化处理
            // 实际应该从分组键或聚合结果中获取
        }

        // 评估条件
        if (!evaluate_condition(lhs_val, rhs_val, cond.op)) {
            return false;
        }
    }

    return true;
}

std::string AggregationExecutor::get_agg_key(const ast::AggExpr& agg_expr) {
    if (!agg_expr.alias.empty()) {
        return agg_expr.alias;
    }

    std::string func_name;
    switch (agg_expr.type) {
        case AGG_COUNT: func_name = "COUNT"; break;
        case AGG_SUM: func_name = "SUM"; break;
        case AGG_AVG: func_name = "AVG"; break;
        case AGG_MAX: func_name = "MAX"; break;
        case AGG_MIN: func_name = "MIN"; break;
        default: func_name = "UNKNOWN"; break;
    }

    if (agg_expr.col) {
        return func_name + "(" + agg_expr.col->col_name + ")";
    } else {
        return func_name + "(*)";
    }
}

void AggregationExecutor::write_value_to_record(char* dest, const Value& value, const ColMeta& col_meta) {
    if (value.is_null) {
        // 对于NULL值，我们需要特殊处理
        // 这里我们使用一个特殊的标记来表示NULL
        switch (col_meta.type) {
            case TYPE_INT:
                *reinterpret_cast<int*>(dest) = INT_MIN;  // 使用INT_MIN表示NULL
                break;
            case TYPE_FLOAT:
                *reinterpret_cast<float*>(dest) = std::numeric_limits<float>::quiet_NaN();  // 使用NaN表示NULL
                break;
            case TYPE_STRING:
                std::memset(dest, 0, col_meta.len);
                std::memcpy(dest, "NULL", std::min(4UL, static_cast<size_t>(col_meta.len)));
                break;
            default:
                break;
        }
    } else {
        switch (col_meta.type) {
            case TYPE_INT:
                *reinterpret_cast<int*>(dest) = value.int_val;
                break;
            case TYPE_FLOAT:
                *reinterpret_cast<float*>(dest) = value.float_val;
                break;
            case TYPE_STRING:
                std::memset(dest, 0, col_meta.len);
                std::memcpy(dest, value.str_val.c_str(), std::min(value.str_val.length(), static_cast<size_t>(col_meta.len)));
                break;
            default:
                break;
        }
    }
}

Value AggregationExecutor::extract_column_value(const std::unique_ptr<RmRecord>& record, const TabCol& col) {
    ColMeta col_meta = prev_->get_col_offset(col);
    Value value;
    
    const char* data = record->data + col_meta.offset;
    
    switch (col_meta.type) {
        case TYPE_INT:
            value.set_int(*reinterpret_cast<const int*>(data));
            break;
        case TYPE_FLOAT:
            value.set_float(*reinterpret_cast<const float*>(data));
            break;
        case TYPE_STRING:
            value.set_str(std::string(data, col_meta.len));
            // 去除尾部的空字符
            value.str_val.resize(std::strlen(value.str_val.c_str()));
            break;
        default:
            break;
    }
    
    return value;
}

// 辅助函数：评估条件
bool AggregationExecutor::evaluate_condition(const Value& lhs, const Value& rhs, CompOp op) {
    // 处理类型不匹配的情况（仅支持 INT 和 FLOAT 之间的转换比较）
    if (lhs.type != rhs.type) {
        if ((lhs.type == TYPE_INT && rhs.type == TYPE_FLOAT) ||
            (lhs.type == TYPE_FLOAT && rhs.type == TYPE_INT)) {
            // 转换为 double 进行比较（避免精度损失）
            double v1 = (lhs.type == TYPE_INT) ? lhs.int_val : lhs.float_val;
            double v2 = (rhs.type == TYPE_INT) ? rhs.int_val : rhs.float_val;

            switch (op) {
                case OP_EQ: return v1 == v2;
                case OP_NE: return v1 != v2;
                case OP_LT: return v1 < v2;
                case OP_GT: return v1 > v2;
                case OP_LE: return v1 <= v2;
                case OP_GE: return v1 >= v2;
                default: return false;
            }
        }
        // 其他类型不匹配的情况，直接返回 false
        return false;
    }

    // 类型相同的情况，根据类型进行比较
    switch (lhs.type) {
        case TYPE_INT: {
            int v1 = lhs.int_val;
            int v2 = rhs.int_val;

            switch (op) {
                case OP_EQ: return v1 == v2;
                case OP_NE: return v1 != v2;
                case OP_LT: return v1 < v2;
                case OP_GT: return v1 > v2;
                case OP_LE: return v1 <= v2;
                case OP_GE: return v1 >= v2;
                default: return false;
            }
        }

        case TYPE_FLOAT: {
            float v1 = lhs.float_val;
            float v2 = rhs.float_val;

            switch (op) {
                case OP_EQ: return v1 == v2;
                case OP_NE: return v1 != v2;
                case OP_LT: return v1 < v2;
                case OP_GT: return v1 > v2;
                case OP_LE: return v1 <= v2;
                case OP_GE: return v1 >= v2;
                default: return false;
            }
        }

        case TYPE_STRING: {
            const std::string& s1 = lhs.str_val;
            const std::string& s2 = rhs.str_val;

            switch (op) {
                case OP_EQ: return s1 == s2;
                case OP_NE: return s1 != s2;
                case OP_LT: return s1 < s2;
                case OP_GT: return s1 > s2;
                case OP_LE: return s1 <= s2;
                case OP_GE: return s1 >= s2;
                default: return false;
            }
        }

        default:
            return false;
    }
}

// 辅助函数：比较两个值
int AggregationExecutor::compare_values(const Value& v1, const Value& v2) {
    if (v1.type != v2.type) {
        // 类型不同时的处理
        if ((v1.type == TYPE_INT && v2.type == TYPE_FLOAT) || 
            (v1.type == TYPE_FLOAT && v2.type == TYPE_INT)) {
            double val1 = (v1.type == TYPE_INT) ? v1.int_val : v1.float_val;
            double val2 = (v2.type == TYPE_INT) ? v2.int_val : v2.float_val;
            
            if (val1 < val2) return -1;
            if (val1 > val2) return 1;
            return 0;
        }
        return 0; // 其他类型不匹配的情况
    }
    
    switch (v1.type) {
        case TYPE_INT:
            if (v1.int_val < v2.int_val) return -1;
            if (v1.int_val > v2.int_val) return 1;
            return 0;
        case TYPE_FLOAT:
            if (v1.float_val < v2.float_val) return -1;
            if (v1.float_val > v2.float_val) return 1;
            return 0;
        case TYPE_STRING:
            return v1.str_val.compare(v2.str_val);
        default:
            return 0;
    }
}

// 检查是否可以优化COUNT(*)查询
bool AggregationExecutor::can_optimize_count_star() {
    // 必须满足以下条件才能优化：
    // 1. 没有GROUP BY子句
    // 2. 只有一个聚合函数且为COUNT(*)
    // 3. 没有HAVING条件
    // 4. 前置执行器是SeqScanExecutor且没有WHERE条件

    if (!group_cols_.empty()) {
        return false;  // 有GROUP BY子句
    }

    if (agg_exprs_.size() != 1) {
        return false;  // 不是单个聚合函数
    }

    const ast::AggExpr& agg_expr = agg_exprs_[0];
    // if (agg_expr.type != AGG_COUNT || agg_expr.col != nullptr) {
    //     return false;  // 不是COUNT(*)
    // }
    if (agg_expr.type != AGG_COUNT && !sel_cols_.empty()) {
        return false;  // 不是COUNT(*)
    }

    if (!having_conds_.empty()) {
        return false;  // 有HAVING条件
    }

    // 检查前置执行器是否为SeqScanExecutor且没有WHERE条件
    if (prev_->getType() == "SeqScanExecutor") {
        std::string tab_name = prev_->get_tab_name();
        if (!tab_name.empty()) {
            // 尝试通过dynamic_cast获取SeqScanExecutor以检查条件
            SeqScanExecutor* seq_scan = dynamic_cast<SeqScanExecutor*>(prev_.get());
            if (seq_scan != nullptr) {
                // 检查是否有WHERE条件
                const std::vector<Condition>& conditions = seq_scan->get_conditions();
                if (conditions.empty()) {
                    // 没有WHERE条件，可以优化
                    return true;
                }
            }
        }
    }

    return false;
}

// 执行优化的COUNT(*)查询
void AggregationExecutor::execute_optimized_count_star() {
    // 获取表名
    std::string tab_name = prev_->get_tab_name();
    if (tab_name.empty()) {
        // 如果无法获取表名，回退到常规方法
        // 执行常规聚合
        for (prev_->beginTuple(); !prev_->is_end(); prev_->nextTuple()) {
            auto record = prev_->Next();
            if (!record) continue;
            GroupKey group_key = extract_group_key(record);
            AggregateState& state = group_states_[group_key];
            for (const auto& agg_expr : agg_exprs_) {
                update_aggregate_state(state, agg_expr, record);
            }
        }
        // 处理结果
        for (const auto& pair : group_states_) {
            const GroupKey& group_key = pair.first;
            const AggregateState& state = pair.second;
            AggregateResult result = compute_final_result(state);
            if (evaluate_having_conditions(group_key, result)) {
                results_[group_key] = result;
            }
        }
        return;
    }

    // 尝试通过dynamic_cast获取SeqScanExecutor以检查是否有WHERE条件
    // 由于C++的限制，我们需要一个更安全的方法
    // 这里我们假设如果前置执行器是SeqScanExecutor，我们可以进行优化

    // 获取文件句柄并直接计算记录数
    RmFileHandle* fh = sm_manager_->fhs_.at(tab_name).get();
    if (!fh) {
        // 如果无法获取文件句柄，回退到常规方法
        return;
    }

    // 使用get_num_records()直接获取记录数
    int record_count = fh->get_num_records();

    // 创建空的分组键（因为没有GROUP BY）
    GroupKey empty_key(std::vector<Value>{});

    // 创建聚合结果
    AggregateResult result;
    std::string agg_key = get_agg_key(agg_exprs_[0]);

    Value count_val;
    count_val.set_int(record_count);

    result.values[agg_key] = count_val;

    // 检查HAVING条件（虽然前面已经检查过没有HAVING条件）
    if (evaluate_having_conditions(empty_key, result)) {
        results_[empty_key] = result;
    }
}
