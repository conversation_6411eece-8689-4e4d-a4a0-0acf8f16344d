/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "execution_common.h"

class DeleteExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Condition> conds_;  // delete的条件
    RmFileHandle *fh_;              // 表的数据文件句柄
    std::vector<Rid> rids_;         // 需要删除的记录的位置
    std::string tab_name_;          // 表名称
    SmManager *sm_manager_;
    std::unique_ptr<AbstractExecutor> prev_;  // 投影节点的子执行器

   public:
    DeleteExecutor(SmManager *sm_manager, const std::string &tab_name, std::unique_ptr<AbstractExecutor> prev, std::vector<Condition> conds,
                   std::vector<Rid> rids, Context *context) 
        : sm_manager_(sm_manager), tab_name_(tab_name), conds_(std::move(conds)), rids_(std::move(rids)) {
        prev_ = std::move(prev);
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    }

    std::unique_ptr<RmRecord> Next() override {
        Transaction *txn = context_->txn_;
        timestamp_t read_ts = txn->get_read_ts();
        timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
        TransactionManager *tmr = context_->txn_mgr_;

        // /*1.一次性删除所有元组*/
        // for(auto& rid : rids_)
        // {
        //     /*1.1读取原始记录与tuplemeta（若本事务更新过则从内存读否则页上读）*/
        //     TupleMeta tuplemeta;
        //     bool isSelfModify = false;
        //     bool isInsertDelete = false;
        //     auto keyPair = std::make_pair(fh_->GetFd(), rid);  // 表FD + Rid唯一标识记录
        //     std::unique_ptr<RmRecord> record;
        //     auto &private_inserts = txn->get_private_insert();
        //     auto &private_updates = txn->get_private_update();
        //     if (private_inserts.count(keyPair)) {
        //         /*代表是本事务新插入的*/
        //         if(private_updates.count(keyPair))
        //         {
        //             /*代表该记录被更新过且没被删除*/
        //             record = std::make_unique<RmRecord>(std::get<0>(private_updates[keyPair]));
        //             tuplemeta = {temp_ts, false};
        //             assert(std::get<1>(private_updates[keyPair]) != OperationType::DELETE);
        //         }
        //         else
        //         {
        //             record = std::make_unique<RmRecord>(std::get<0>(private_inserts[keyPair]));
        //             tuplemeta = {temp_ts, false};
        //         }
        //         isSelfModify = true;
        //         isInsertDelete = true;
        //     } else if(private_updates.count(keyPair))
        //     {
        //         /*代表该记录被更新过且没被删除*/
        //         record = std::make_unique<RmRecord>(std::get<0>(private_updates[keyPair]));
        //         tuplemeta = {temp_ts, false};
        //         assert(std::get<1>(private_updates[keyPair]) != OperationType::DELETE);
        //         isSelfModify = true;
        //     } else {
        //         bool iswwconflict = false;
        //         // 首次修改，从数据页读取原始版本,并读取时间戳
        //         record = get_visible_record(&tab_, fh_, rid, txn, &iswwconflict, context_);
        //         if(iswwconflict)
        //         {
        //             throw TransactionAbortException(txn->get_transaction_id(), AbortReason::UPGRADE_CONFLICT);
        //         }
        //         /*TODO：这里基本上一定非空，因为seqscan过滤过了*/
        //         tuplemeta = fh_->get_tuple_meta(rid);
        //     }
        //     /*私有空间删除*/
        //     if(!isInsertDelete)
        //     {
        //         /*写入私有变量集合*/
        //         private_updates[keyPair] = {*record, OperationType::DELETE};  // 存入私有内存
        //     }
        //     else
        //     {
        //         /*在私有变量中移除相应的值*/
        //         private_inserts.erase(keyPair);
        //         private_updates.erase(keyPair);
        //         /*释放槽位*/
        //         fh_->deAllocateRid(rid, context_);
        //         /*移除时间戳*/
        //         fh_->erase_tuple_meta(rid);
        //     }
        //     /*加入写集合*/
        //     WriteRecord *wr = new WriteRecord(WType::DELETE_TUPLE, tab_name_, rid, *(record.get()));
        //     txn->append_delete_record(wr);
        // }

        
        std::unique_ptr<RmRecord> record;
        auto &private_inserts = txn->get_private_insert();
        auto &private_updates = txn->get_private_update();
        for (prev_->beginTuple(); !prev_->is_end(); prev_->nextTuple()) {
            record = prev_->Next();
            if (!record) continue;
            if(prev_->is_conflict)
            {
                throw TransactionAbortException(txn->get_transaction_id(), AbortReason::UPGRADE_CONFLICT);
            }
            auto keyPair = std::make_pair(fh_->GetFd(), prev_->rid());  // 表FD + Rid唯一标识记录
            if(!private_inserts.count(keyPair))
            {
                /*写入私有变量集合*/
                private_updates[keyPair] = {*record, OperationType::DELETE};  // 存入私有内存
            }
            else
            {
                /*在私有变量中移除相应的值*/
                private_inserts.erase(keyPair);
                private_updates.erase(keyPair);
                /*释放槽位*/
                fh_->deAllocateRid(prev_->rid(), context_);
                /*移除时间戳*/
                fh_->erase_tuple_meta(prev_->rid());
            }
            /*加入写集合*/
            WriteRecord *wr = new WriteRecord(WType::DELETE_TUPLE, tab_name_, prev_->rid(), *(record.get()));
            txn->append_delete_record(wr);
        }
        return nullptr;
    }

    Rid& rid() override { return _abstract_rid; }
};
