/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "execution_common.h"

class UpdateExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;
    std::vector<Condition> conds_;
    RmFileHandle *fh_;
    std::vector<Rid> rids_;
    std::string tab_name_;
    std::vector<SetClause> set_clauses_;
    SmManager *sm_manager_;
    std::unique_ptr<AbstractExecutor> prev_;  // 投影节点的子执行器

   public:
    UpdateExecutor(SmManager *sm_manager, const std::string &tab_name, std::unique_ptr<AbstractExecutor> prev, std::vector<SetClause> set_clauses,
                   std::vector<Condition> conds, std::vector<Rid> rids, Context *context) {
        sm_manager_ = sm_manager;
        prev_ = std::move(prev);
        tab_name_ = tab_name;
        set_clauses_ = set_clauses;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }
    std::unique_ptr<RmRecord> Next() override {
        Transaction *txn = context_->txn_;
        timestamp_t read_ts = txn->get_read_ts();
        timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
        TransactionManager *tmr = context_->txn_mgr_;
        TabMeta tabmeta = sm_manager_->db_.get_table(tab_name_);
        std::vector<TabCol> change_cols;
        
        /*1.记录缓冲区*/
        RmRecord record(fh_->get_file_hdr().record_size);

        /*1. 提取本次更新涉及的列名*/
        std::unordered_set<std::string> updated_cols;
        for (auto &clause : set_clauses_) {
            updated_cols.insert(clause.lhs.col_name);
        }

        // /*2.更新每一个元组*/
        // for(const auto& rid : rids_)
        // {
        //     std::unique_ptr<RmRecord> targetRecord;
        //     /*1.1读取原始记录与tuplemeta（若本事务更新过则从内存读否则页上读）*/
        //     TupleMeta tuplemeta;
        //     bool isSelfModify = false;
        //     auto keyPair = std::make_pair(fh_->GetFd(), rid);  // 表FD + Rid唯一标识记录
        //     auto &private_inserts = txn->get_private_insert();
        //     auto &private_updates = txn->get_private_update();
        //     if (private_inserts.count(keyPair)) {
        //         /*代表是本事务新插入的*/
        //         if(private_updates.count(keyPair))
        //         {
        //             /*代表该记录被更新过且没被删除*/
        //             targetRecord = std::make_unique<RmRecord>(std::get<0>(private_updates[keyPair]));
        //             tuplemeta = {temp_ts, false};
        //             assert(std::get<1>(private_updates[keyPair]) != OperationType::DELETE);
        //         }
        //         else
        //         {
        //             targetRecord = std::make_unique<RmRecord>(std::get<0>(private_inserts[keyPair]));
        //             tuplemeta = {temp_ts, false};
        //         }
        //         isSelfModify = true;
        //     } else if(private_updates.count(keyPair))
        //     {
        //         /*代表该记录被更新过且没被删除*/
        //         targetRecord = std::make_unique<RmRecord>(std::get<0>(private_updates[keyPair]));
        //         tuplemeta = {temp_ts, false};
        //         assert(std::get<1>(private_updates[keyPair]) != OperationType::DELETE);
        //         isSelfModify = true;
        //     } else {
        //         bool isWWconflict = false;
        //         // 首次修改，从数据页读取原始版本,并读取时间戳
        //         targetRecord = get_visible_record(&tab_, fh_, rid, txn, &isWWconflict, context_);
        //         if(isWWconflict)
        //         {
        //             throw TransactionAbortException(txn->get_transaction_id(), AbortReason::UPGRADE_CONFLICT);
        //         }
        //         /*TODO：这里基本上一定非空，因为seqscan过滤过了*/
        //         tuplemeta = fh_->get_tuple_meta(rid);
        //     }
        //     record.SetData(targetRecord->data);

        //     /*2.1构造新的元组*/
        //     for(const auto& clause : set_clauses_)
        //     {
        //         TabCol lhs = clause.lhs;
        //         change_cols.push_back(lhs);
        //         SetValue rvalue = clause.rhs;
        //         Value rhs;
        //         auto col = tab_.get_col(lhs.col_name);
        //         if(rvalue.is_expr)
        //         {
        //             /*rcol是表达式中的那个列，rhs是表达式的值，
        //             col2val是从元组中取到的rcol的值,final_value是expr1 + expr2的值*/
        //             TabCol rcol;
        //             Value col2val;
        //             Value final_value;
        //             bool first_isvalue = false;
        //             if(rvalue.first_is_col && rvalue.second_is_val)
        //             {
        //                 rcol = rvalue.first_col;
        //                 rhs = rvalue.second_val;
        //             }
        //             else if(rvalue.second_is_col && rvalue.first_is_val)
        //             {
        //                 rcol = rvalue.second_col;
        //                 rhs = rvalue.first_val;
        //                 first_isvalue = true;
        //             }

        //             /*从元组中取值到col2val */
        //             auto rcolmeta = tab_.get_col(rcol.col_name);
        //             std::string str(targetRecord->data + rcolmeta->offset,rcolmeta->len);
        //             switch(col->type)
        //             {
        //                 case TYPE_INT:
        //                     col2val.set_int(*reinterpret_cast<const int*>(targetRecord->data + rcolmeta->offset));
        //                     break;
        //                 case TYPE_FLOAT:
        //                     col2val.set_float(*reinterpret_cast<const float*>(targetRecord->data + rcolmeta->offset));
        //                     break;
        //                 case TYPE_STRING:
        //                     col2val.set_str(str);
        //                     break;
        //                 default:
        //                     throw InternalError("invalid type\n");
        //             }
        //             col2val.init_raw(col->len);

        //             /*第一个expr的类型检查，要么同类型要么int->float*/
        //             if (col->type != col2val.type) {
        //                 if((col->type == TYPE_STRING && col2val.type != TYPE_STRING) 
        //                 || (col->type == TYPE_INT && col2val.type == TYPE_FLOAT)
        //                 || (col->type != TYPE_STRING && col2val.type == TYPE_STRING))
        //                 {
        //                     throw IncompatibleTypeError(coltype2str(col->type), coltype2str(col2val.type));
        //                 }
        //                 // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
        //                 // {
        //                 //     rhs.type = TYPE_INT;
        //                 //     rhs.set_int(int(rhs.float_val));
        //                 // }
        //                 else
        //                 {
        //                     col2val.type = TYPE_FLOAT;
        //                     col2val.set_float(float(rhs.int_val));
        //                 }
        //             }

        //             /*第二个expr的类型检查，要么同类型要么int->float*/
        //             if (col->type != rhs.type) {
        //                 if((col->type == TYPE_STRING && rhs.type != TYPE_STRING) 
        //                 || (col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
        //                 || (col->type != TYPE_STRING && rhs.type == TYPE_STRING))
        //                 {
        //                     throw IncompatibleTypeError(coltype2str(col->type), coltype2str(rhs.type));
        //                 }
        //                 // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
        //                 // {
        //                 //     rhs.type = TYPE_INT;
        //                 //     rhs.set_int(int(rhs.float_val));
        //                 // }
        //                 else
        //                 {
        //                     rhs.type = TYPE_FLOAT;
        //                     rhs.set_float(float(rhs.int_val));
        //                 }
        //             }

        //             /*现在两个expr的类型相同且都等于左列，可以直接运算*/
        //             switch(col->type)
        //             {
        //                 case TYPE_INT:
        //                 {
        //                     if(rvalue.op == OP_ADD)
        //                     {
        //                         final_value.set_int(col2val.int_val + rhs.int_val);
        //                     }
        //                     else if(rvalue.op == OP_SUB)
        //                     {
        //                         if(first_isvalue)
        //                         {
        //                             final_value.set_int(rhs.int_val - col2val.int_val);
        //                         }
        //                         else
        //                         {
        //                             final_value.set_int(col2val.int_val - rhs.int_val);
        //                         }
        //                     }
        //                     else if(rvalue.op == OP_MUL)
        //                     {
        //                         final_value.set_int(col2val.int_val * rhs.int_val);
        //                     }
        //                     else if(rvalue.op == OP_DIV)
        //                     {
        //                         if(first_isvalue)
        //                         {
        //                             final_value.set_int(rhs.int_val / col2val.int_val);
        //                         }
        //                         else
        //                         {
        //                             final_value.set_int(col2val.int_val / rhs.int_val);
        //                         }
        //                     }
        //                     else
        //                     {
        //                         throw InternalError("invalid type\n");
        //                     }
        //                     break;
        //                 }
        //                 case TYPE_FLOAT:
        //                 {
        //                     if(rvalue.op == OP_ADD)
        //                     {
        //                         final_value.set_float(col2val.float_val + rhs.float_val);
        //                     }
        //                     else if(rvalue.op == OP_SUB)
        //                     {
        //                         if(first_isvalue)
        //                         {
        //                             final_value.set_float(rhs.float_val - col2val.float_val);
        //                         }
        //                         else
        //                         {
        //                             final_value.set_float(col2val.float_val - rhs.float_val);
        //                         }
        //                     }
        //                     else if(rvalue.op == OP_MUL)
        //                     {
        //                         final_value.set_float(col2val.float_val * rhs.float_val);
        //                     }
        //                     else if(rvalue.op == OP_DIV)
        //                     {
        //                         if(first_isvalue)
        //                         {
        //                             final_value.set_float(rhs.float_val / col2val.float_val);
        //                         }
        //                         else
        //                         {
        //                             final_value.set_float(col2val.float_val / rhs.float_val);
        //                         }
        //                     }
        //                     else
        //                     {
        //                         throw InternalError("invalid type\n");
        //                     }
        //                     break;
        //                 }
        //                 case TYPE_STRING:
        //                 {
        //                     if(rvalue.op != OP_ADD)
        //                     {
        //                         throw InternalError("invalid type\n");
        //                     }
        //                     final_value.set_str(col2val.str_val + rhs.str_val);
        //                     break;
        //                 }
        //                 default:
        //                     throw InternalError("invalid type\n");
        //             }
        //             final_value.init_raw(col->len);
        //             memcpy(record.data + col->offset, final_value.raw->data,col->len);
        //         }
        //         else
        //         {
        //             rhs = rvalue.first_val;
        //             if (col->type != rhs.type) {
        //                 if((col->type == TYPE_STRING && rhs.type != TYPE_STRING) 
        //                 || (col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
        //                 || (col->type != TYPE_STRING && rhs.type == TYPE_STRING))
        //                 {
        //                     throw IncompatibleTypeError(coltype2str(col->type), coltype2str(rhs.type));
        //                 }
        //                 // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
        //                 // {
        //                 //     rhs.type = TYPE_INT;
        //                 //     rhs.set_int(int(rhs.float_val));
        //                 // }
        //                 else
        //                 {
        //                     rhs.type = TYPE_FLOAT;
        //                     rhs.set_float(float(rhs.int_val));
        //                 }
        //             }
        //             rhs.init_raw(col->len);
        //             memcpy(record.data + col->offset, rhs.raw->data,col->len);
        //         }
        //     }

        //     if(tmr->get_concurrency_mode() == ConcurrencyMode::MVCC)
        //     {
        //         /*写入私有变量集合*/
        //         auto keyPair = std::make_pair(fh_->GetFd(), rid);
        //         auto &private_updates = txn->get_private_update();
        //         private_updates[keyPair] = {record, OperationType::UPDATE};  // 存入私有内存
        //     }
        //     // /*5.写事务的写集合*/
        //     // WriteRecord *wr = new WriteRecord(WType::UPDATE_TUPLE, tab_name_, rid, *targetRecord);
        //     // txn->append_write_record(wr);
        // }

        // 遍历所有输入元组
        for (prev_->beginTuple(); !prev_->is_end(); prev_->nextTuple()) {
            auto targetRecord = prev_->Next();
            if (!targetRecord) continue;
            if(prev_->is_conflict)
            {
                throw TransactionAbortException(txn->get_transaction_id(), AbortReason::UPGRADE_CONFLICT);
            }
            RmRecord record(fh_->get_file_hdr().record_size);
            record.SetData(targetRecord->data);
            // TupleMeta tuplemeta;
            // bool isSelfModify = false;
            // auto keyPair = std::make_pair(fh_->GetFd(), prev_->rid());  // 表FD + Rid唯一标识记录
            // auto &private_inserts = txn->get_private_insert();
            // auto &private_updates = txn->get_private_update();
            /*2.1构造新的元组*/
            for(const auto& clause : set_clauses_)
            {
                TabCol lhs = clause.lhs;
                change_cols.push_back(lhs);
                SetValue rvalue = clause.rhs;
                Value rhs;
                auto col = tab_.get_col(lhs.col_name);
                if(rvalue.is_expr)
                {
                    /*rcol是表达式中的那个列，rhs是表达式的值，
                    col2val是从元组中取到的rcol的值,final_value是expr1 + expr2的值*/
                    TabCol rcol;
                    Value col2val;
                    Value final_value;
                    bool first_isvalue = false;
                    if(rvalue.first_is_col && rvalue.second_is_val)
                    {
                        rcol = rvalue.first_col;
                        rhs = rvalue.second_val;
                    }
                    else if(rvalue.second_is_col && rvalue.first_is_val)
                    {
                        rcol = rvalue.second_col;
                        rhs = rvalue.first_val;
                        first_isvalue = true;
                    }

                    /*从元组中取值到col2val */
                    auto rcolmeta = tab_.get_col(rcol.col_name);
                    std::string str(targetRecord->data + rcolmeta->offset,rcolmeta->len);
                    switch(col->type)
                    {
                        case TYPE_INT:
                            col2val.set_int(*reinterpret_cast<const int*>(targetRecord->data + rcolmeta->offset));
                            break;
                        case TYPE_FLOAT:
                            col2val.set_float(*reinterpret_cast<const float*>(targetRecord->data + rcolmeta->offset));
                            break;
                        case TYPE_STRING:
                            col2val.set_str(str);
                            break;
                        default:
                            throw InternalError("invalid type\n");
                    }
                    col2val.init_raw(col->len);

                    /*第一个expr的类型检查，要么同类型要么int->float*/
                    if (col->type != col2val.type) {
                        if((col->type == TYPE_STRING && col2val.type != TYPE_STRING) 
                        || (col->type == TYPE_INT && col2val.type == TYPE_FLOAT)
                        || (col->type != TYPE_STRING && col2val.type == TYPE_STRING))
                        {
                            throw IncompatibleTypeError(coltype2str(col->type), coltype2str(col2val.type));
                        }
                        // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        // {
                        //     rhs.type = TYPE_INT;
                        //     rhs.set_int(int(rhs.float_val));
                        // }
                        else
                        {
                            col2val.type = TYPE_FLOAT;
                            col2val.set_float(float(rhs.int_val));
                        }
                    }

                    /*第二个expr的类型检查，要么同类型要么int->float*/
                    if (col->type != rhs.type) {
                        if((col->type == TYPE_STRING && rhs.type != TYPE_STRING) 
                        || (col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        || (col->type != TYPE_STRING && rhs.type == TYPE_STRING))
                        {
                            throw IncompatibleTypeError(coltype2str(col->type), coltype2str(rhs.type));
                        }
                        // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        // {
                        //     rhs.type = TYPE_INT;
                        //     rhs.set_int(int(rhs.float_val));
                        // }
                        else
                        {
                            rhs.type = TYPE_FLOAT;
                            rhs.set_float(float(rhs.int_val));
                        }
                    }

                    /*现在两个expr的类型相同且都等于左列，可以直接运算*/
                    switch(col->type)
                    {
                        case TYPE_INT:
                        {
                            if(rvalue.op == OP_ADD)
                            {
                                final_value.set_int(col2val.int_val + rhs.int_val);
                            }
                            else if(rvalue.op == OP_SUB)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_int(rhs.int_val - col2val.int_val);
                                }
                                else
                                {
                                    final_value.set_int(col2val.int_val - rhs.int_val);
                                }
                            }
                            else if(rvalue.op == OP_MUL)
                            {
                                final_value.set_int(col2val.int_val * rhs.int_val);
                            }
                            else if(rvalue.op == OP_DIV)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_int(rhs.int_val / col2val.int_val);
                                }
                                else
                                {
                                    final_value.set_int(col2val.int_val / rhs.int_val);
                                }
                            }
                            else
                            {
                                throw InternalError("invalid type\n");
                            }
                            break;
                        }
                        case TYPE_FLOAT:
                        {
                            if(rvalue.op == OP_ADD)
                            {
                                final_value.set_float(col2val.float_val + rhs.float_val);
                            }
                            else if(rvalue.op == OP_SUB)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_float(rhs.float_val - col2val.float_val);
                                }
                                else
                                {
                                    final_value.set_float(col2val.float_val - rhs.float_val);
                                }
                            }
                            else if(rvalue.op == OP_MUL)
                            {
                                final_value.set_float(col2val.float_val * rhs.float_val);
                            }
                            else if(rvalue.op == OP_DIV)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_float(rhs.float_val / col2val.float_val);
                                }
                                else
                                {
                                    final_value.set_float(col2val.float_val / rhs.float_val);
                                }
                            }
                            else
                            {
                                throw InternalError("invalid type\n");
                            }
                            break;
                        }
                        case TYPE_STRING:
                        {
                            if(rvalue.op != OP_ADD)
                            {
                                throw InternalError("invalid type\n");
                            }
                            final_value.set_str(col2val.str_val + rhs.str_val);
                            break;
                        }
                        default:
                            throw InternalError("invalid type\n");
                    }
                    final_value.init_raw(col->len);
                    memcpy(record.data + col->offset, final_value.raw->data,col->len);
                }
                else
                {
                    rhs = rvalue.first_val;
                    if (col->type != rhs.type) {
                        if((col->type == TYPE_STRING && rhs.type != TYPE_STRING) 
                        || (col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        || (col->type != TYPE_STRING && rhs.type == TYPE_STRING))
                        {
                            throw IncompatibleTypeError(coltype2str(col->type), coltype2str(rhs.type));
                        }
                        // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        // {
                        //     rhs.type = TYPE_INT;
                        //     rhs.set_int(int(rhs.float_val));
                        // }
                        else
                        {
                            rhs.type = TYPE_FLOAT;
                            rhs.set_float(float(rhs.int_val));
                        }
                    }
                    rhs.init_raw(col->len);
                    memcpy(record.data + col->offset, rhs.raw->data,col->len);
                }
            }

            // if(tmr->get_concurrency_mode() == ConcurrencyMode::MVCC)
            // {
                /*写入私有变量集合*/
                auto keyPair = std::make_pair(fh_->GetFd(), prev_->rid());
                auto &private_updates = txn->get_private_update();
                private_updates[keyPair] = {record, OperationType::UPDATE};  // 存入私有内存
            // }
            /*5.写事务的写集合*/
            // WriteRecord *wr = new WriteRecord(WType::UPDATE_TUPLE, tab_name_, prev_->rid(), *targetRecord);
            // txn->append_write_record(wr);
        }
        return nullptr;
    }

    Rid &rid() override { return _abstract_rid; }
};
