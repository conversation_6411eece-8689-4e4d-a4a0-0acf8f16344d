#pragma once

#include <vector>
#include <optional>

#include "transaction/transaction.h"
#include "transaction/transaction_manager.h"
#include "common/common.h"

// 只保留函数声明
auto ReconstructTuple(const TabMeta *schema, const RmRecord &base_tuple, const TupleMeta &base_meta,
                      const std::vector<UndoLog> &undo_logs) -> std::optional<RmRecord>;

auto IsWriteWriteConflict(timestamp_t tuple_ts, Transaction *txn) -> bool;

void ExtractValuesFromRecord(const std::vector<ColMeta> &columns, const RmRecord *record,
                             std::vector<Value> &modifiedvalue_value);

void ExtractModifiedValues(
    TabMeta& table, 
    const std::vector<TabCol> &change_cols, 
    const RmRecord &record,
    std::vector<Value> &modified_values,
    std::vector<bool> &modified_flags
);

void ExtractDifferentValues(
    const TabMeta& table,
    const RmRecord& record1,
    const RmRecord& record2,
    std::vector<Value>& modified_values,
    std::vector<bool>& modified_flags
);

std::unique_ptr<RmRecord> get_visible_record(const TabMeta *schema, RmFileHandle *fh, Rid rid, Transaction *txn, bool *isWWconflict, Context *context);