#include "gtest/gtest.h"
#include "execution/execution_common.h"
#include <vector>
#include <memory>
#include <string>
#include <cstring>

// 辅助函数：初始化表元数据
TabMeta initializeTabMeta() {
    TabMeta tab_meta;
    tab_meta.name = "test_table";

    // 添加列元数据：id(int), name(string), score(float), age(int)
    ColMeta col1, col2, col3, col4;
    col1.name = "id"; col1.type = TYPE_INT; col1.offset = 0; col1.len = sizeof(int);
    col2.name = "name"; col2.type = TYPE_STRING; col2.offset = sizeof(int); col2.len = 20;
    col3.name = "score"; col3.type = TYPE_FLOAT; col3.offset = sizeof(int) + 20; col3.len = sizeof(float);
    col4.name = "age"; col4.type = TYPE_INT; col4.offset = sizeof(int) + 20 + sizeof(float); col4.len = sizeof(int);

    tab_meta.cols = {col1, col2, col3, col4};
    return tab_meta;
}

// 辅助函数：初始化基础元组数据
RmRecord initializeBaseRecord() {
    RmRecord base_tuple(64); // 4+20+4+4 = 32 bytes
    int id = 1001;
    std::string name = "Alice";
    float score = 95.5f;
    int age = 20;
    
    memcpy(base_tuple.data, &id, sizeof(int));
    memcpy(base_tuple.data + sizeof(int), name.c_str(), name.length());
    memcpy(base_tuple.data + sizeof(int) + 20, &score, sizeof(float));
    memcpy(base_tuple.data + sizeof(int) + 20 + sizeof(float), &age, sizeof(int));
    return base_tuple;
}

// 辅助函数：初始化基础元组元数据
TupleMeta initializeBaseMeta() {
    TupleMeta base_meta;
    base_meta.ts_ = 10;
    base_meta.is_deleted_ = false;
    return base_meta;
}

// 测试场景4: 多个撤销日志的情况
TEST(ExecutionCommonTest, ReconstructTuple_MultipleUndoLogs) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(2);
    
    // 第一个撤销日志：修改id和score
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {true, false, true, false}; // 修改id和score
    Value id_val, score_val;
    id_val.set_int(1002);
    score_val.set_float(98.0f);
    undo_logs[0].tuple_ = {id_val, score_val};
    
    // 第二个撤销日志：修改name和age
    undo_logs[1].is_deleted_ = false;
    undo_logs[1].ts_ = 30;
    undo_logs[1].modified_fields_ = {false, true, false, true}; // 修改name和age
    Value name_val, age_val;
    name_val.set_str("Bob");
    age_val.set_int(22);
    undo_logs[1].tuple_ = {name_val, age_val};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value());
    
    // 验证修改后的值
    int new_id;
    memcpy(&new_id, result.value().data, sizeof(int));
    EXPECT_EQ(new_id, 1002);
    
    char name_buf[21] = {0};
    memcpy(name_buf, result.value().data + sizeof(int), 20);
    EXPECT_STREQ(name_buf, "Bob");
    
    float new_score;
    memcpy(&new_score, result.value().data + sizeof(int) + 20, sizeof(float));
    EXPECT_FLOAT_EQ(new_score, 98.0f);
    
    int new_age;
    memcpy(&new_age, result.value().data + sizeof(int) + 20 + sizeof(float), sizeof(int));
    EXPECT_EQ(new_age, 22);
}

// 测试场景5: 基础元组已删除但撤销日志恢复
TEST(ExecutionCommonTest, ReconstructTuple_DeletedBaseRecoveredByUndo) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();
    base_meta.is_deleted_ = true; // 基础元组被删除
    
    std::vector<UndoLog> undo_logs(1);
    undo_logs[0].is_deleted_ = false; // 撤销日志标记为未删除
    undo_logs[0].ts_ = 20;
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value()); // 应恢复为可见元组
}

// 测试场景6: 撤销日志中修改同一字段多次
TEST(ExecutionCommonTest, ReconstructTuple_MultipleModificationsToSameField) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(2);
    
    // 第一次修改id为1002
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {true, false, false, false};
    Value id_val1;
    id_val1.set_int(1002);
    undo_logs[0].tuple_ = {id_val1};
    
    // 第二次修改id为1003
    undo_logs[1].is_deleted_ = false;
    undo_logs[1].ts_ = 30;
    undo_logs[1].modified_fields_ = {true, false, false, false};
    Value id_val2;
    id_val2.set_int(1003);
    undo_logs[1].tuple_ = {id_val2};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value());
    
    int new_id;
    memcpy(&new_id, result.value().data, sizeof(int));
    EXPECT_EQ(new_id, 1003); // 应应用最后一次修改
}

// 测试场景7: 撤销日志包含删除和修改操作
TEST(ExecutionCommonTest, ReconstructTuple_DeleteAndModifyCombination) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(2);
    
    // 第一次操作：修改name
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {false, true, false, false};
    Value name_val;
    name_val.set_str("Bob");
    undo_logs[0].tuple_ = {name_val};
    
    // 第二次操作：删除元组
    undo_logs[1].is_deleted_ = true;
    undo_logs[1].ts_ = 30;
    undo_logs[1].modified_fields_ = {};
    undo_logs[1].tuple_ = {};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_FALSE(result.has_value()); // 最终元组被删除，应返回nullopt
}

// 测试场景8: 字符串字段修改边界情况
TEST(ExecutionCommonTest, ReconstructTuple_StringFieldBoundary) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(1);
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {false, true, false, false}; // 修改name字段
    
    // 设置一个接近长度限制的字符串
    Value name_val;
    std::string long_name(19, 'x'); // 19个字符，加上\0共20字节
    name_val.set_str(long_name);
    undo_logs[0].tuple_ = {name_val};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value());
    
    char name_buf[21] = {0};
    memcpy(name_buf, result.value().data + sizeof(int), 20);
    EXPECT_STREQ(name_buf, long_name.c_str());
}

// 测试场景9: 浮点数精确修改测试
TEST(ExecutionCommonTest, ReconstructTuple_FloatPrecision) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(1);
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {false, false, true, false}; // 修改score字段
    
    Value score_val;
    score_val.set_float(99.99f);
    undo_logs[0].tuple_ = {score_val};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value());
    
    float new_score;
    memcpy(&new_score, result.value().data + sizeof(int) + 20, sizeof(float));
    EXPECT_FLOAT_EQ(new_score, 99.99f);
}

// 测试场景10: 部分字段未修改的情况
TEST(ExecutionCommonTest, ReconstructTuple_PartialFieldsUnmodified) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(1);
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {true, false, true, false}; // 只修改id和score
    
    Value id_val, score_val;
    id_val.set_int(1002);
    score_val.set_float(98.0f);
    undo_logs[0].tuple_ = {id_val, score_val};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value());
    
    // 验证未修改的字段
    char name_buf[21] = {0};
    memcpy(name_buf, result.value().data + sizeof(int), 20);
    EXPECT_STREQ(name_buf, "Alice"); // name字段未修改
    
    int age;
    memcpy(&age, result.value().data + sizeof(int) + 20 + sizeof(float), sizeof(int));
    EXPECT_EQ(age, 20); // age字段未修改
}

// 测试场景11: 撤销日志中包含未使用的字段
TEST(ExecutionCommonTest, ReconstructTuple_UndoLogWithExtraFields) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();

    std::vector<UndoLog> undo_logs(1);
    undo_logs[0].is_deleted_ = false;
    undo_logs[0].ts_ = 20;
    undo_logs[0].modified_fields_ = {true, true, true, true}; // 修改所有字段
    
    // 提供比实际字段更多的值（测试鲁棒性）
    Value id_val, name_val, score_val, age_val, extra_val;
    id_val.set_int(1002);
    name_val.set_str("Bob");
    score_val.set_float(98.0f);
    age_val.set_int(22);
    extra_val.set_int(999); // 额外值，应被忽略
    undo_logs[0].tuple_ = {id_val, name_val, score_val, age_val, extra_val};
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_TRUE(result.has_value());
    
    // 验证修改后的值，extra_val应被忽略
    int new_id;
    memcpy(&new_id, result.value().data, sizeof(int));
    EXPECT_EQ(new_id, 1002);
}

// 测试场景12: 基础元组删除且撤销日志未恢复
TEST(ExecutionCommonTest, ReconstructTuple_BaseDeletedAndNotRecovered) {
    TabMeta tab_meta = initializeTabMeta();
    RmRecord base_tuple = initializeBaseRecord();
    TupleMeta base_meta = initializeBaseMeta();
    base_meta.is_deleted_ = true; // 基础元组被删除
    
    std::vector<UndoLog> undo_logs(1);
    undo_logs[0].is_deleted_ = true; // 撤销日志也标记为删除
    undo_logs[0].ts_ = 20;
    
    auto result = ReconstructTuple(&tab_meta, base_tuple, base_meta, undo_logs);
    EXPECT_FALSE(result.has_value()); // 元组仍被删除，应返回nullopt
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}