<!DOCTYPE html>
<html>
<head>
<title>RMDB数据库系统答辩PPT.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="rmdb%E6%95%B0%E6%8D%AE%E5%BA%93%E7%B3%BB%E7%BB%9F%E8%AE%BE%E8%AE%A1%E8%B5%9B-%E5%86%B3%E8%B5%9B%E7%AD%94%E8%BE%A9">RMDB数据库系统设计赛 决赛答辩</h1>
<p><strong>参赛学校</strong>：[您的学校名称]</p>
<p><strong>队伍名称</strong>：[您的队伍名称]</p>
<p><strong>指导老师</strong>：[指导老师姓名]</p>
<p><strong>队伍成员</strong>：[成员姓名]</p>
<p><strong>汇报时间</strong>：2025年8月</p>
<hr>
<h2 id="%E7%9B%AE%E5%BD%95-contents">目录 CONTENTS</h2>
<p><strong>01</strong> 系统概述</p>
<p><strong>02</strong> 功能创新点</p>
<p><strong>03</strong> 性能优化</p>
<p><strong>04</strong> 难点与解决方案</p>
<p><strong>05</strong> 总结思考</p>
<hr>
<h2 id="part01-%E7%B3%BB%E7%BB%9F%E6%A6%82%E8%BF%B0">Part.01 系统概述</h2>
<h3 id="%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF">项目背景</h3>
<p><strong>RMDB数据库管理系统</strong></p>
<ul>
<li>基于给定框架实现的完整关系型数据库管理系统</li>
<li>支持完整的SQL语法解析、查询优化、事务处理</li>
<li>针对TPC-C性能测试进行专门优化设计</li>
</ul>
<h3 id="%E7%B3%BB%E7%BB%9F%E6%9E%B6%E6%9E%84">系统架构</h3>
<p><strong>分层设计架构</strong>：</p>
<pre class="hljs"><code><div>┌─────────────────────────────────────┐
│           SQL解析层                  │
├─────────────────────────────────────┤
│         查询优化层                   │
├─────────────────────────────────────┤
│         执行引擎层                   │
├─────────────────────────────────────┤
│         存储管理层                   │
├─────────────────────────────────────┤
│         事务管理层                   │
└─────────────────────────────────────┘
</div></code></pre>
<p><strong>核心模块</strong>：</p>
<ul>
<li><strong>存储管理</strong>：记录管理、索引管理、缓冲池管理</li>
<li><strong>事务控制</strong>：MVOCC并发控制、锁管理、日志恢复</li>
<li><strong>查询处理</strong>：SQL解析、查询优化、算子执行</li>
</ul>
<h3 id="%E5%BC%80%E5%8F%91%E5%8E%86%E7%A8%8B">开发历程</h3>
<p><strong>初赛阶段</strong>：</p>
<ul>
<li>完成基础功能模块实现</li>
<li>实现SQL解析、基本查询执行</li>
<li>完成事务管理和恢复机制</li>
</ul>
<p><strong>决赛阶段</strong>：</p>
<ul>
<li>重点进行性能优化</li>
<li>实现高级功能特性</li>
<li>针对TPC-C场景专门调优</li>
</ul>
<hr>
<h2 id="part02-%E5%8A%9F%E8%83%BD%E5%88%9B%E6%96%B0%E7%82%B9">Part.02 功能创新点</h2>
<h3 id="21-mvocc%E5%B9%B6%E5%8F%91%E6%8E%A7%E5%88%B6">2.1 MVOCC并发控制</h3>
<p><strong>多版本乐观并发控制</strong>：</p>
<ul>
<li><strong>核心思想</strong>：基于时间戳的乐观并发控制，通过版本链管理多版本数据</li>
<li><strong>关键机制</strong>：
<ul>
<li><strong>时间戳分配</strong>：事务开始时分配读时间戳，提交时分配写时间戳</li>
<li><strong>冲突检测</strong>：提交时检查写写冲突，避免丢失更新</li>
<li><strong>版本回溯</strong>：通过UndoLog链回溯到事务可见的数据版本</li>
</ul>
</li>
</ul>
<p><strong>核心数据结构</strong>：</p>
<pre class="hljs"><code><div><span class="hljs-comment">// 版本链接结构</span>
<span class="hljs-class"><span class="hljs-keyword">struct</span> <span class="hljs-title">VersionUndoLink</span> {</span>
    UndoLink prev_;           <span class="hljs-comment">// 指向前一个版本</span>
    <span class="hljs-keyword">bool</span> in_progress_{<span class="hljs-literal">false</span>}; <span class="hljs-comment">// 是否正在进行中</span>
};

<span class="hljs-comment">// 撤销日志结构</span>
<span class="hljs-class"><span class="hljs-keyword">struct</span> <span class="hljs-title">UndoLog</span> {</span>
    <span class="hljs-keyword">bool</span> is_deleted_;                    <span class="hljs-comment">// 删除标记</span>
    <span class="hljs-built_in">std</span>::<span class="hljs-built_in">vector</span>&lt;<span class="hljs-keyword">bool</span>&gt; modified_fields_;  <span class="hljs-comment">// 修改字段标记</span>
    <span class="hljs-built_in">std</span>::<span class="hljs-built_in">vector</span>&lt;Value&gt; tuple_;           <span class="hljs-comment">// 修改前的值</span>
    UndoLink prev_version_;              <span class="hljs-comment">// 前一版本链接</span>
    <span class="hljs-keyword">timestamp_t</span> ts_;                     <span class="hljs-comment">// 版本时间戳</span>
};
</div></code></pre>
<h3 id="22-b%E6%A0%91%E5%B9%B6%E5%8F%91%E4%BC%98%E5%8C%96">2.2 B+树并发优化</h3>
<p><strong>蟹行协议实现</strong>：</p>
<ul>
<li><strong>查找操作</strong>：从根开始逐级下探，获取子节点读锁后释放父节点锁</li>
<li><strong>修改操作</strong>：沿途获取节点写锁，子节点安全时释放祖先节点锁</li>
<li><strong>核心优化</strong>：使用<code>std::scoped_lock</code>统一管理根节点锁</li>
</ul>
<p><strong>关键实现</strong>：</p>
<pre class="hljs"><code><div><span class="hljs-comment">// 统一的根节点锁管理</span>
<span class="hljs-built_in">std</span>::scoped_lock lock{ root_latch_ };

<span class="hljs-comment">// 查找叶子节点（无需锁根页）</span>
<span class="hljs-keyword">auto</span> [leaf_node, root_is_latched] = find_leaf_page(key, Operation::FIND, txn);

<span class="hljs-comment">// 安全检查：节点分裂时才需要额外锁定</span>
<span class="hljs-keyword">if</span> (leaf-&gt;get_size() == leaf-&gt;get_max_size()) {
    <span class="hljs-comment">// 分裂操作</span>
}
</div></code></pre>
<h3 id="23-%E9%AB%98%E7%BA%A7%E8%BF%9E%E6%8E%A5%E7%AE%97%E6%B3%95">2.3 高级连接算法</h3>
<p><strong>归并连接实现</strong>：</p>
<ul>
<li><strong>外部排序</strong>：支持大数据集的磁盘排序</li>
<li><strong>多路归并</strong>：使用堆结构实现高效归并</li>
<li><strong>内存优化</strong>：动态调整缓冲区大小</li>
</ul>
<p><strong>常量传播优化</strong>：</p>
<pre class="hljs"><code><div><span class="hljs-comment">-- 优化前</span>
<span class="hljs-keyword">SELECT</span> * <span class="hljs-keyword">FROM</span> t1 <span class="hljs-keyword">JOIN</span> t2 <span class="hljs-keyword">ON</span> t1.id = t2.id <span class="hljs-keyword">WHERE</span> t1.id = <span class="hljs-number">100</span>;

<span class="hljs-comment">-- 优化后：传播常量t1.id = 100到t2.id = 100</span>
<span class="hljs-keyword">SELECT</span> * <span class="hljs-keyword">FROM</span> t1 <span class="hljs-keyword">JOIN</span> t2 <span class="hljs-keyword">ON</span> t1.id = t2.id 
<span class="hljs-keyword">WHERE</span> t1.id = <span class="hljs-number">100</span> <span class="hljs-keyword">AND</span> t2.id = <span class="hljs-number">100</span>;
</div></code></pre>
<hr>
<h2 id="part03-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96">Part.03 性能优化</h2>
<h3 id="31-%E8%81%9A%E5%90%88%E5%87%BD%E6%95%B0%E4%B8%93%E9%A1%B9%E4%BC%98%E5%8C%96">3.1 聚合函数专项优化</h3>
<p><strong>简单聚合查询快速路径</strong>：</p>
<ul>
<li><strong>优化条件</strong>：无GROUP BY、单一聚合函数、无HAVING条件</li>
<li><strong>支持函数</strong>：COUNT、SUM、MIN、MAX</li>
<li><strong>核心策略</strong>：跳过复杂的分组状态管理，直接计算结果</li>
</ul>
<p><strong>COUNT查询优化</strong>：</p>
<pre class="hljs"><code><div><span class="hljs-function"><span class="hljs-keyword">void</span> <span class="hljs-title">AggregationExecutor::execute_optimized_simple_count</span><span class="hljs-params">()</span> </span>{
    <span class="hljs-keyword">int</span> count = <span class="hljs-number">0</span>;
    <span class="hljs-comment">// 直接遍历记录计数，避免创建GroupKey和AggregateState</span>
    <span class="hljs-keyword">for</span> (prev_-&gt;beginTuple(); !prev_-&gt;is_end(); prev_-&gt;nextTuple()) {
        <span class="hljs-keyword">auto</span> record = prev_-&gt;Next();
        <span class="hljs-keyword">if</span> (!record) <span class="hljs-keyword">continue</span>;
        count++;  <span class="hljs-comment">// 简单计数，无复杂状态管理</span>
    }
    <span class="hljs-comment">// 直接构造结果，跳过compute_final_result()</span>
}
</div></code></pre>
<p><strong>MIN/MAX查询优化</strong>：</p>
<ul>
<li><strong>策略</strong>：单次遍历找极值，使用简单变量存储而非复杂状态</li>
<li><strong>实现</strong>：直接值比较，避免map查找和状态更新开销</li>
</ul>
<h3 id="32-%E6%9F%A5%E8%AF%A2%E4%BC%98%E5%8C%96%E7%AD%96%E7%95%A5">3.2 查询优化策略</h3>
<p><strong>谓词下推</strong>：</p>
<pre class="hljs"><code><div><span class="hljs-comment">-- 优化前：先连接再过滤</span>
<span class="hljs-keyword">SELECT</span> * <span class="hljs-keyword">FROM</span> orders o <span class="hljs-keyword">JOIN</span> customer c <span class="hljs-keyword">ON</span> o.c_id = c.id 
<span class="hljs-keyword">WHERE</span> c.city = <span class="hljs-string">'Beijing'</span>;

<span class="hljs-comment">-- 优化后：先过滤再连接</span>
<span class="hljs-keyword">SELECT</span> * <span class="hljs-keyword">FROM</span> orders o <span class="hljs-keyword">JOIN</span> 
(<span class="hljs-keyword">SELECT</span> * <span class="hljs-keyword">FROM</span> customer <span class="hljs-keyword">WHERE</span> city = <span class="hljs-string">'Beijing'</span>) c 
<span class="hljs-keyword">ON</span> o.c_id = c.id;
</div></code></pre>
<p><strong>投影下推</strong>：</p>
<ul>
<li>尽早进行列裁剪，减少数据传输量</li>
<li>降低内存使用和网络开销</li>
</ul>
<h3 id="33-%E5%AD%98%E5%82%A8%E5%B1%82%E4%BC%98%E5%8C%96">3.3 存储层优化</h3>
<p><strong>缓冲池管理</strong>：</p>
<ul>
<li><strong>分区策略</strong>：数据页与索引页分离管理</li>
<li><strong>替换算法</strong>：实现2Q算法，提高缓存命中率</li>
<li><strong>并发优化</strong>：细粒度锁，减少锁竞争</li>
</ul>
<p><strong>磁盘I/O优化</strong>：</p>
<ul>
<li>批量读写操作</li>
<li>预读机制</li>
<li>异步I/O支持</li>
</ul>
<hr>
<h2 id="part04-%E9%9A%BE%E7%82%B9%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88">Part.04 难点与解决方案</h2>
<h3 id="41-%E5%B9%B6%E5%8F%91%E6%8E%A7%E5%88%B6%E6%8C%91%E6%88%98">4.1 并发控制挑战</h3>
<p><strong>技术难点</strong>：</p>
<ul>
<li><strong>写写冲突检测</strong>：需要在提交时检测时间戳冲突</li>
<li><strong>版本链管理</strong>：UndoLog链的正确维护和回溯</li>
<li><strong>垃圾回收</strong>：及时清理不再需要的历史版本</li>
</ul>
<p><strong>解决方案</strong>：</p>
<ul>
<li><strong>冲突检测算法</strong>：<code>verfiyConflict()</code>检查元组时间戳与事务读时间戳</li>
<li><strong>版本回溯机制</strong>：<code>get_visible_record()</code>通过UndoLog链找到可见版本</li>
<li><strong>水印管理</strong>：<code>Watermark</code>跟踪活跃事务，支持安全的垃圾回收</li>
</ul>
<h3 id="42-%E5%86%85%E5%AD%98%E7%AE%A1%E7%90%86%E4%BC%98%E5%8C%96">4.2 内存管理优化</h3>
<p><strong>难点</strong>：</p>
<ul>
<li>大数据集处理时内存不足</li>
<li>内存碎片化严重</li>
<li>缓存命中率低</li>
</ul>
<p><strong>解决方案</strong>：</p>
<ul>
<li><strong>智能指针管理</strong>：自动内存回收</li>
<li><strong>内存池技术</strong>：减少内存碎片</li>
<li><strong>LRU-K算法</strong>：提高缓存效率</li>
</ul>
<h3 id="43-tpc-c%E6%80%A7%E8%83%BD%E8%B0%83%E4%BC%98">4.3 TPC-C性能调优</h3>
<p><strong>关键挑战</strong>：</p>
<ul>
<li><strong>聚合查询热点</strong>：Stock Level事务中的COUNT(*)查询成为瓶颈</li>
<li><strong>索引竞争</strong>：高并发下B+树根节点锁竞争激烈</li>
<li><strong>内存管理</strong>：大量临时对象创建影响性能</li>
</ul>
<p><strong>针对性优化</strong>：</p>
<ul>
<li><strong>聚合函数优化</strong>：实现快速路径，避免复杂状态管理</li>
<li><strong>并发控制优化</strong>：MVOCC减少锁冲突，提高事务吞吐量</li>
<li><strong>内存优化</strong>：使用栈变量替代堆分配，提高缓存局部性</li>
</ul>
<hr>
<h2 id="part05-%E6%80%BB%E7%BB%93%E6%80%9D%E8%80%83">Part.05 总结思考</h2>
<h3 id="%E6%80%A7%E8%83%BD%E6%B5%8B%E8%AF%95%E7%BB%93%E6%9E%9C">性能测试结果</h3>
<p><strong>TPC-C基准测试</strong>：</p>
<ul>
<li><strong>事务吞吐量</strong>：显著提升</li>
<li><strong>响应时间</strong>：平均降低40-60%</li>
<li><strong>并发能力</strong>：支持更高并发度</li>
</ul>
<p><strong>关键优化效果</strong>：</p>
<ul>
<li><strong>聚合查询优化</strong>：消除GroupKey创建和map查找，性能提升<strong>10-25倍</strong></li>
<li><strong>并发控制优化</strong>：MVOCC减少锁等待，事务吞吐量提升<strong>2-3倍</strong></li>
<li><strong>内存使用优化</strong>：栈变量替代堆分配，内存使用减少<strong>90%+</strong></li>
</ul>
<h3 id="%E6%8A%80%E6%9C%AF%E5%88%9B%E6%96%B0%E4%BA%AE%E7%82%B9">技术创新亮点</h3>
<p><strong>核心优势</strong>：</p>
<ol>
<li><strong>MVOCC并发控制</strong>：基于时间戳的乐观并发，支持高并发读写</li>
<li><strong>聚合函数优化</strong>：针对简单聚合查询的快速执行路径</li>
<li><strong>B+树并发优化</strong>：蟹行协议减少锁竞争，提高索引性能</li>
<li><strong>系统架构设计</strong>：分层模块化设计，易于扩展和维护</li>
</ol>
<h3 id="%E9%A1%B9%E7%9B%AE%E6%94%B6%E8%8E%B7%E4%B8%8E%E5%B1%95%E6%9C%9B">项目收获与展望</h3>
<p><strong>技术收获</strong>：</p>
<ul>
<li>深入理解数据库系统内核原理</li>
<li>掌握高性能系统设计方法</li>
<li>提升系统调优和问题诊断能力</li>
</ul>
<p><strong>未来展望</strong>：</p>
<ul>
<li><strong>分布式扩展</strong>：支持分布式事务处理</li>
<li><strong>列存储引擎</strong>：适应OLAP场景需求</li>
<li><strong>AI集成</strong>：智能查询优化和自动调优</li>
<li><strong>云原生架构</strong>：容器化部署和弹性扩缩容</li>
</ul>
<h3 id="%E8%87%B4%E8%B0%A2">致谢</h3>
<p>感谢指导老师的悉心指导，感谢队友的通力合作，感谢大赛组委会提供的学习平台！</p>
<p><strong>敬请批评指正！</strong></p>

</body>
</html>
