# 生成一致性检验SQL并保存到test_sql.sql文件
W_MAX = 50  # 仓库数量上限
D_MAX = 10 # 每个仓库的地区数量

# 打开文件准备写入
with open('test_sql.sql', 'w') as f:
    # 写入注释说明
    f.write("-- 自动生成的一致性检验SQL\n")
    f.write("-- 覆盖所有(w_id, d_id)组合，共{}个仓库，每个仓库{}个地区\n\n".format(W_MAX, D_MAX))
    
    # 遍历所有(w_id, d_id)组合
    for w_id in range(1, W_MAX + 1):
        for d_id in range(1, D_MAX + 1):
            # 写入当前组合的标识注释
            f.write("-- 检验组合: w_id={}, d_id={}\n".format(w_id, d_id))
            
            # 1. district、orders、new_orders一致性检验
            f.write("SELECT d_next_o_id FROM district WHERE d_w_id={} AND d_id={};\n".format(w_id, d_id))
            f.write("SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id={} AND o_d_id={};\n".format(w_id, d_id))
            f.write("SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id={} AND no_d_id={};\n".format(w_id, d_id))
            
            # 2. new_orders内部一致性检验
            f.write("SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id={} AND no_d_id={};\n".format(w_id, d_id))
            f.write("SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id={} AND no_d_id={};\n".format(w_id, d_id))
            f.write("SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id={} AND no_d_id={};\n".format(w_id, d_id))
            
            # 3. orders和order_line一致性检验
            f.write("SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id={} AND o_d_id={};\n".format(w_id, d_id))
            f.write("SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id={} AND ol_d_id={};\n".format(w_id, d_id))
            
            # 每个组合间空一行分隔
            f.write("\n")

print("SQL文件生成完成: test_sql.sql")
