def check_output_format():
    # 读取文件并预处理行（去除空白和分隔符）
    with open("testdb/output.txt", "r") as f:
        lines = [
            line.strip().strip("|").strip()  # 去除两端空白和|符号
            for line in f.readlines()
            if line.strip()  # 过滤空行
        ]

    # 检查总行数是否足够
    if len(lines) < 18:
        print("错误：文件行数不足，至少需要18行（前18行为表计数）")
        return

    # 跳过前18行，处理后续分组
    remaining_lines = lines[18:]
    group_size = 16  # 每个检验分组的行数

    # 检查剩余行数是否为分组大小的倍数
    if len(remaining_lines) % group_size != 0:
        print(f"错误：剩余行数{len(remaining_lines)}不是{group_size}的倍数，分组格式异常")
        return

    # 定义每组预期的键-值对（按顺序，每个键值对占2行：键行+值行）
    expected_pairs = [
        ("d_next_o_id", "3001"),
        ("max_o_id", "3000"),
        ("max_no_o_id", "3000"),
        ("count_no_o_id", "900"),
        ("max_no_o_id", "3000"),
        ("min_no_o_id", "2101"),
        ("sum_ol_cnt", "30000"),
        ("count_ol_o_id", "30000"),
    ]

    error_detected = False
    total_groups = len(remaining_lines) // group_size

    # 遍历每个分组（每组16行）
    for group_idx in range(total_groups):
        start = group_idx * group_size
        end = start + group_size
        group = remaining_lines[start:end]

        # 检查每组内的8个键值对（共16行，每2行一个键值对）
        for pair_idx in range(8):
            key_line_idx = 2 * pair_idx  # 键所在的行索引（偶数行）
            val_line_idx = 2 * pair_idx + 1  # 值所在的行索引（奇数行）
            expected_key, expected_val = expected_pairs[pair_idx]

            # 检查键是否符合预期
            actual_key = group[key_line_idx]
            if actual_key != expected_key:
                print(f"分组{group_idx}（起始行{start}）错误：\n"
                      f"  键行{start + key_line_idx}预期为「{expected_key}」，实际为「{actual_key}」")
                error_detected = True

            # 检查值是否符合预期
            actual_val = group[val_line_idx]
            if actual_val != expected_val:
                print(f"分组{group_idx}（起始行{start}）错误：\n"
                      f"  值行{start + val_line_idx}预期为「{expected_val}」，实际为「{actual_val}」")
                error_detected = True

    if not error_detected:
        print("所有分组均符合预期格式 ✔️")


if __name__ == "__main__":
    check_output_format()